import { z } from "zod";

const domainStatusSchema = z.union([
  z.literal("active"),
  z.literal("inactive"),
]);

export type DomainStatus = z.infer<typeof domainStatusSchema>;

export const domainSchema = z.object({
  id: z.string(),
  serialNumber: z.number(),
  moderator: z.string(),
  domain: z.string(),
  countriesAssignAsNative: z.array(z.string()),
  countriesAssignAsHybrid: z.array(z.string()),
  status: domainStatusSchema,
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date().optional(),
});

export type Domain = z.infer<typeof domainSchema>;
