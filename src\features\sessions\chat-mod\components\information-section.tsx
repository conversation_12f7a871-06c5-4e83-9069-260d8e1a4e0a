import { IconEdit } from "@tabler/icons-react";
import { useState } from "react";
import { InformationField, InformationFormData, FieldValues } from "../types";
import { InformationModal } from "./information-modal";

interface InformationSectionProps {
  fields: InformationField[];
  onUpdateField: (fieldId: string, data: InformationFormData) => void;
  fieldValues: FieldValues;
  onFieldValuesChange: (values: FieldValues) => void;
}

export function InformationSection({
  fields,
  onUpdateField,
  fieldValues,
  onFieldValuesChange,
}: InformationSectionProps) {
  const [showModal, setShowModal] = useState(false);
  const [selectedField, setSelectedField] = useState<
    InformationField | undefined
  >();

  const handleEditClick = (field: InformationField) => {
    setSelectedField(field);
    setShowModal(true);
  };

  const handleSave = (data: InformationFormData) => {
    if (selectedField) {
      onUpdateField(selectedField.id, data);
    }
    setSelectedField(undefined);
  };

  const handleModalClose = (open: boolean) => {
    setShowModal(open);
    if (!open) {
      setSelectedField(undefined);
    }
  };

  return (
    <>
      <div className="flex flex-col gap-3 bg-sidebar p-4 rounded-2xl">
        {fields.map((field) => {
          return (
            <div key={field.id} className="bg-sidebar-accent p-3 rounded-lg">
              <div className="flex gap-4 items-center justify-between">
                <div className="flex items-center gap-1 text-sm ">
                  {field.type}
                </div>
                <button
                  className="rounded-full flex justify-center w-[24px] h-[24px] bg-background hover:bg-background/80 transition-colors cursor-pointer"
                  onClick={() => handleEditClick(field)}
                >
                  <IconEdit width="14px" />
                </button>
              </div>
              <hr className="my-3" />
              <ul className="list-disc">
                {field.items.length > 0 ? (
                  <div className="text-xs">{field.items}</div>
                ) : (
                  <span className="text-xs text-muted-foreground">
                    No information added
                  </span>
                )}
              </ul>
            </div>
          );
        })}
      </div>

      <InformationModal
        open={showModal}
        onOpenChange={handleModalClose}
        selectedField={selectedField}
        onSave={handleSave}
        fieldValues={fieldValues}
        onFieldValuesChange={onFieldValuesChange}
      />
    </>
  );
}
