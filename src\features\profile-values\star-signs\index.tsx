import { Main } from "@/components/layout/main";
import { StarSignsPrimaryButtons } from "./components/star-signs-primary-buttons";
import { StarSignsTable } from "./components/star-signs-table";
import { columns } from "./components/star-signs-columns";

export default function StarSignsList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Star Signs List</h2>
                </div>
                <StarSignsPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <StarSignsTable columns={columns} />
            </div>
        </Main>
    )
}
