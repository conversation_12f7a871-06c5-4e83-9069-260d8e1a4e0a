import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "@/api/apiClient";
import { toast } from "sonner";
import { API_ENDPOINTS } from "@/features/members/api/api-endpoints";

// Create affiliate API
export const createAffiliateApi = () => {
    return useMutation({
        mutationFn: async (data: any) => {
            const response = await apiClient.post("/affiliates", data);
            return response.data;
        },
        onError: (error: any) => {
            toast.error(error?.response?.data?.message || "Failed to create affiliate");
        },
    });
};

// Get affiliate details API
export const getAffiliateDetails = (id?: string) => {
    return useQuery({
        queryKey: ["affiliate-details", id],
        queryFn: async () => {
            if (!id) return {};
            const response = await apiClient.get(`/affiliates/${id}`);
            return response.data;
        },
        enabled: !!id,
    });
};

// Update affiliate API
export const updateAffiliateApi = () => {
    return useMutation({
        mutationFn: async ({ id, data }: { id: string; data: any }) => {
            const response = await apiClient.put(`/affiliates/${id}`, data);
            return response.data;
        },
        onError: (error: any) => {
            toast.error(error?.response?.data?.message || "Failed to update affiliate");
        },
    });
};

// Country list (reused from members modules)
export const getCountryList = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.COUNTRY_LIST);
            return response?.data ?? {};
        },
        queryKey: ["country-list"],
    });
