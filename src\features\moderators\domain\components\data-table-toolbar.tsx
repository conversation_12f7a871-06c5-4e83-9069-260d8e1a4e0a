import { Cross2Icon } from '@radix-ui/react-icons'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useState } from 'react'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
}

const domainOptions = [
  'discreetdating.club',
  'passionhub.net',
  'lovequest.org',
  'secretlovers.co',
  'adultmatch.com',
  'flirtzone.com',
  'uforpls.female.deso',
  'example.com',
  'testdomain.net',
  'sample.org'
]

const countryOptions = [
  'Denmark',
  'Norway',
  'Sweden',
  'United Kingdom',
  'Ireland',
  'United States',
  'Canada',
  'Australia',
  'Germany',
  'France',
  'Spain',
  'Italy',
  'Japan',
  'India'
]

export function DataTableToolbar<TData>({
  table,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  const [filters, setFilters] = useState({
    search: '',
    domain: undefined as string | undefined,
    country: undefined as string | undefined,
  })

  const [hasSearched, setHasSearched] = useState(false)

  const handleFilterChange = (
    key: 'search' | 'domain' | 'country',
    value: string | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  const handleSearch = () => {
    // Apply filters to the table
    if (filters.search) {
      table.getColumn('moderator')?.setFilterValue(filters.search)
    }
    if (filters.domain) {
      table.getColumn('domain')?.setFilterValue(filters.domain)
    }
    if (filters.country) {
      // Filter by country in either native or hybrid countries
      const countryFilter = {
        filterFn: (row: any, columnId: string, filterValue: string) => {
          const nativeCountries = row.original.countriesAssignAsNative as string[]
          const hybridCountries = row.original.countriesAssignAsHybrid as string[]
          return nativeCountries.includes(filterValue) || hybridCountries.includes(filterValue)
        }
      }

      // Apply custom filter for country search
      const countryColumn = table.getColumn('countriesAssignAsNative')
      if (countryColumn) {
        countryColumn.columnDef.filterFn = countryFilter.filterFn
        countryColumn.setFilterValue(filters.country)
      }
    }
    setHasSearched(true)
  }

  const handleReset = () => {
    setFilters({
      search: '',
      domain: undefined,
      country: undefined,
    })
    table.resetColumnFilters()
    setHasSearched(false)
  }

  const hasActiveFilters = filters.search || filters.domain || filters.country

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center gap-4 flex-wrap'>
        <Input
          placeholder='Search moderator name'
          value={filters.search}
          onChange={(event) => handleFilterChange('search', event.target.value)}
          className='h-9 w-[250px]'
        />
        <FilterSelect
          value={filters.domain}
          placeholder="Select Domain"
          options={domainOptions}
          onChange={(value) => handleFilterChange('domain', value)}
        />
        <FilterSelect
          value={filters.country}
          placeholder="Select Country"
          options={countryOptions}
          onChange={(value) => handleFilterChange('country', value)}
        />
        <Button
          onClick={handleSearch}
          className="h-9 px-3"
          disabled={!hasActiveFilters}
        >
          Search
        </Button>
        {(isFiltered || hasSearched) && (
          <Button
            variant='outline'
            onClick={handleReset}
            className='h-9 px-2 lg:px-3'
          >
            Reset
            {/* <Cross2Icon className='ml-2 h-4 w-4' /> */}
          </Button>
        )}
      </div>

    </div>
  )
}
