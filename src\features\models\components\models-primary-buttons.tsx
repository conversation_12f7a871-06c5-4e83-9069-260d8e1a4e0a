import { IconUserPlus } from "@tabler/icons-react";
import { But<PERSON> } from "@/components/ui/button";
import { useModels } from "../context/models-context";
import { useRouter } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";

export function ModelsPrimaryButtons() {
  const { setOpen } = useModels();
  const router = useRouter();
  return (
    <div className="flex gap-2">
      <Button
        className="space-x-1"
        onClick={() =>
          router.navigate({ to: END_POINTS.CREATE_MODEL })
        }
      >
        <span>Add Model</span> <IconUserPlus size={18} />
      </Button>
    </div>
  );
}
