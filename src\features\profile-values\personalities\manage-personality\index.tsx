import { Main } from "@/components/layout/main";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { addPersonalityApi, updatePersonalityApi, getPersonalityDetails, getMasterLanguagesApi } from "../api";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import { END_POINTS } from "@/features/members/utils/constant";

export default function ManagePersonality() {
    const navigate = useNavigate()
    const { personalityId } = useParams({ strict: false })
    const { data: { languages = [] } = {} } = getMasterLanguagesApi()
    const { mutateAsync: addPersonalityMutation } = addPersonalityApi();
    const { mutateAsync: updatePersonalityMutation } = updatePersonalityApi();
    const { data = {} } = getPersonalityDetails(personalityId || "")
    const [isSubmitting, setIsSubmitting] = useState(false);
    const initializedRef = useRef(false)

    // Create dynamic schema based on available languages
    const PersonalitySchema = useMemo(() => {
        const schemaObject: Record<string, z.ZodString> = {};

        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                const languageCode = lang.code || lang.languageCode || lang.id;
                if (languageCode) {
                    schemaObject[languageCode] = z.string().min(1, "Personality name is required");
                }
            });
        } else {
            const defaultLanguages = ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'];
            defaultLanguages.forEach(lang => {
                schemaObject[lang] = z.string().min(1, "Personality name is required");
            });
        }

        return z.object(schemaObject);
    }, [languages]);

    type PersonalityFormValues = z.infer<typeof PersonalitySchema>;

    // Create dynamic default values (prefill for update)
    const defaultValues = useMemo(() => {
        const defaults: Record<string, string> = {};
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                const code = lang.code || lang.languageCode || lang.id;
                if (code) {
                    const fromApi = (data as any)?.items?.find((it: any) => it.languageCode === code)?.message
                    defaults[code] = fromApi || "";
                }
            });
        } else {
            const defaultLanguages = ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'];
            defaultLanguages.forEach(lang => {
                const fromApi = (data as any)?.items?.find((it: any) => it.languageCode === lang)?.message
                defaults[lang] = fromApi || "";
            });
        }
        return defaults;
    }, [languages, data]);

    const form = useForm<PersonalityFormValues>({
        resolver: zodResolver(PersonalitySchema),
        defaultValues: defaultValues as PersonalityFormValues,
    });

    const { control, handleSubmit, reset } = form;

    useEffect(() => {
        if (initializedRef.current) return
        // Initialize once when languages loaded (and data if editing)
        if ((languages && languages.length > 0) || (personalityId && (data as any))) {
            reset(defaultValues as PersonalityFormValues)
            initializedRef.current = true
        }
    }, [languages, personalityId, data, defaultValues, reset])

    const onSubmit = async (values: PersonalityFormValues) => {
        try {
            setIsSubmitting(true);
            const items = Object.entries(values).map(([languageCode, message]) => ({ languageCode, message }))
            const payload = { items }

            if (personalityId) {
                await updatePersonalityMutation({ id: personalityId, data: payload });
                toast.success("Personality updated successfully!");
            } else {
                await addPersonalityMutation(payload);
                toast.success("Personality added successfully!");
            }
            navigate({ to: END_POINTS.PERSONALITIES })
        } catch (error: any) {
            toast.error(error?.response?.data?.message || "Action failed");
        } finally {
            setIsSubmitting(false)
        }
    };

    const isEditing = typeof personalityId === "string" && personalityId.length > 0

    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">
                        {isEditing ? "Update Personality" : "Add Personality"}
                    </h2>
                </div>
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <Card>
                    <CardHeader>
                        <CardTitle>Personality Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                                <div className="grid grid-cols-2 gap-4">
                                    {languages && languages.length > 0 ? (
                                        languages.map((lang: any) => {
                                            const languageCode = lang.code || lang.languageCode || lang.id;
                                            const languageName = lang.name || languageCode?.toUpperCase() || "";
                                            return (
                                                <FormField
                                                    key={languageCode}
                                                    control={control}
                                                    name={languageCode as keyof PersonalityFormValues}
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel className="text-sm font-medium">
                                                                {languageName}
                                                            </FormLabel>
                                                            <FormControl>
                                                                <Input placeholder={`Enter personality name in ${languageName}`} {...field} />
                                                            </FormControl>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                            );
                                        })
                                    ) : (
                                        ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].map(lang => (
                                            <FormField
                                                key={lang}
                                                control={control}
                                                name={lang as keyof PersonalityFormValues}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="text-sm font-medium">
                                                            {lang.toUpperCase()}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input placeholder={`Enter personality name in ${lang.toUpperCase()}`} {...field} />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        ))
                                    )}
                                </div>
                                <Separator />
                                <div className="flex justify-end space-x-2">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => navigate({ to: END_POINTS.PERSONALITIES })}
                                    >
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={isSubmitting}>
                                        {isEditing ? "Update Personality" : "Add Personality"}
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </Main>
    );
}
