import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getPersonalitiesApi = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.PERSONALITIES, {
                params,
            });
            return response?.data ?? {};
        },
        queryKey: ["personalities-list"],
    });

export const addPersonalityApi = () =>
    useMutation({
        mutationFn: async (data: any) => {
            const response = await apiClient.post(API_ENDPOINTS.PERSONALITIES, data);
            return response?.data;
        },
    });

export const updatePersonalityApi = () =>
    useMutation({
        mutationFn: async ({ id, data }: { id: string; data: any }) => {
            const response = await apiClient.put(`${API_ENDPOINTS.PERSONALITIES}/${id}`, data);
            return response?.data;
        },
    });

export const deletePersonalityApi = () =>
    useMutation({
        mutationFn: async (id: string) => {
            const response = await apiClient.delete(`${API_ENDPOINTS.PERSONALITIES}/${id}`);
            return response?.data;
        },
    });

export const getPersonalityDetails = (id: string) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(`${API_ENDPOINTS.PERSONALITIES}/${id}`);
            return response?.data ?? {};
        },
        queryKey: ["personality-details", id],
        enabled: !!id,
    });

export const getMasterLanguagesApi = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES);
            return response?.data ?? {};
        },
        queryKey: ["get-personality-language-list"],
    });
