import React, { useState } from 'react'
import { Member } from '../data/schema'
import useDialogState from '@/hooks/use-dialog-state'

type MembersDialogType = 'invite' | 'add' | 'edit' | 'delete' | 'view' | 'login' | 'reply' | 'transactions' | 'deactivate'

interface MembersContextType {
  open: MembersDialogType | null
  setOpen: (str: MembersDialogType | null) => void
  currentRow: Member | null
  setCurrentRow: React.Dispatch<React.SetStateAction<Member | null>>
}

const MembersContext = React.createContext<MembersContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function MembersProvider({ children }: Props) {
  const [currentRow, setCurrentRow] = useState<Member | null>(null)
  const { open, setOpen } = useDialogState()

  return (
    <MembersContext.Provider
      value={{
        open,
        setOpen,
        currentRow,
        setCurrentRow,
      }}
    >
      {children}
    </MembersContext.Provider>
  )
}

export const useMembers = () => {
  const membersContext = React.useContext(MembersContext)

  if (!membersContext) {
    throw new Error('useMembers has to be used within <MembersContext.Provider>')
  }

  return membersContext
}
