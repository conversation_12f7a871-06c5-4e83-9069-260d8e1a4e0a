import { use<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import { members } from '../../data/members'
import ProfileCard from '../components/profile-card'
import { IconArrowLeft, IconMessage } from '@tabler/icons-react'
import { Main } from '@/components/layout/main'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import MessageHistoryList from './components/message-list'

// Mock message history data
const messageHistory = [
  {
    id: '1',
    date: 'Feb 12, 2025 10:30 AM',
    recipient: '<PERSON>',
    recipientAvatar: '',
    message: 'Hey there! How are you doing today?',
    status: 'delivered',
    type: 'sent'
  },
  {
    id: '2',
    date: 'Feb 12, 2025 10:32 AM',
    recipient: '<PERSON>',
    recipientAvatar: '',
    message: 'I\'m doing great! Thanks for asking. How about you?',
    status: 'delivered',
    type: 'received'
  },
  {
    id: '3',
    date: 'Feb 11, 2025 3:15 PM',
    recipient: '<PERSON>',
    recipientAvatar: '',
    message: 'Would you like to grab coffee sometime?',
    status: 'read',
    type: 'sent'
  },
  {
    id: '4',
    date: 'Feb 11, 2025 4:20 PM',
    recipient: '<PERSON>',
    recipientAvatar: '',
    message: 'That sounds lovely! I\'d love to.',
    status: 'delivered',
    type: 'received'
  },
  {
    id: '5',
    date: 'Feb 10, 2025 8:45 AM',
    recipient: 'Lisa Anderson',
    recipientAvatar: '',
    message: 'Good morning! Hope you have a wonderful day.',
    status: 'read',
    type: 'sent'
  }
]

export default function MemberMessageHistory() {
  const { memberId } = useParams({ from: '/_authenticated/members/profile/$memberId/messages' })

  // Find the member by ID (in a real app, this would be an API call)
  const member = members.find(m => m.id === memberId)

  if (!member) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Member not found</p>
      </div>
    )
  }

  return (
    <Main>
      <div className=" mx-auto">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
          <Link to="/members" className="flex items-center gap-1 hover:text-foreground">
            <IconArrowLeft className="h-4 w-4" />
            Members List
          </Link>
          <span>/</span>
          <Link to="/members/profile/$memberId" params={{ memberId }} className="hover:text-foreground">
            View Profile
          </Link>
          <span>/</span>
          <span className="text-foreground">Message History</span>
        </div>

        {/* Main Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Side - Profile Card */}
          <div className="lg:col-span-1">
            <ProfileCard member={member} />
          </div>

          {/* Right Side - Message History */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <IconMessage className="h-5 w-5" />
                  Message History
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <MessageHistoryList messages={messageHistory} />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Main>
  )
}
