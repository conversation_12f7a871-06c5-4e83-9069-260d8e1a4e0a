import { Main } from "@/components/layout/main";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useState, useRef, useEffect } from "react";
import { toast } from "sonner";
import { useAuthStore } from "@/stores/authStore";
import { addPackageApi, getPackageDetails, getPresignedUrl, updatePackageApi, uploadFileToS3 } from "../api";

const languageMap = [
    { code: "en", label: "English" },
    { code: "fr", label: "French" },
    { code: "de", label: "German" },
    { code: "nl", label: "Dutch" },
    { code: "da", label: "Danish" },
    { code: "fi", label: "Finnish" },
    { code: "it", label: "Italian" },
    { code: "no", label: "Norwegian" },
    { code: "pl", label: "Polish" },
    { code: "pt", label: "Portuguese" },
    { code: "el", label: "Greek" },
    { code: "es", label: "Spanish" },
    { code: "sv", label: "Swedish" },
];

const giftSchema = z.object({
    ...Object.fromEntries(languageMap.map(({ code, label }) => [code, z.string().min(1, `Package name (${label}) is required.`)])),
    amount: z.string().refine(val => /^\d+(\.\d{1,2})?$/.test(val), "Amount is required and must be a valid number."),
    discountAmount: z.string().refine(val => /^\d+(\.\d{1,2})?$/.test(val), "Discount Amount is required and must be a valid number."),
    coins: z.string().refine(val => /^\d+$/.test(val), "Coins is required and must be a valid integer."),
});

type GiftFormValues = z.infer<typeof giftSchema> & { [key: string]: string };


export default function AddPackage() {
    const navigate = useNavigate()
    const { auth: { setShowSpinner } } = useAuthStore((state) => state)

    const { mutateAsync: addPackageMutation } = addPackageApi()
    const { mutateAsync: updatePackageMutation } = updatePackageApi()

    const { msgId } = useParams({ strict: false });
    const { data = {} } = getPackageDetails(msgId);

    const form = useForm<GiftFormValues>({
        resolver: zodResolver(giftSchema),
        defaultValues: {
            ...Object.fromEntries(languageMap.map(({ code }) => [code, ""])),
            amount: "",
            discountAmount: "",
            coins: "",
        },
    });

    const { control, handleSubmit, reset } = form;

    useEffect(() => {
        if (data && data.package) {
            const pkg = data.package;
            const translationsMap: { [key: string]: string } = {};
            languageMap.forEach(({ code }) => {
                const translation = pkg.translations?.find((t: any) => t.languageCode === code);
                translationsMap[code] = translation ? translation.name : "";
            });
            reset({
                ...translationsMap,
                amount: pkg.amount ? String(pkg.amount) : "",
                discountAmount: pkg.discountAmount ? String(pkg.discountAmount) : "",
                coins: pkg.coins ? String(pkg.coins) : "",
            });
        }
    }, [data, reset]);

    const onSubmit = async (values: GiftFormValues) => {
        // setShowSpinner(true);
        const { amount, discountAmount, coins, ...langs } = values;
        const translations = languageMap.map(({ code }) => ({
            languageCode: code,
            name: langs[code]
        }));
        const payload = {
            amount: Number(amount),
            discountAmount: Number(discountAmount),
            coins: Number(coins),
            isSuspended: false,
            isDeleted: false,
            translations
        };
        if (typeof msgId === "string") {
            const response: any = await updatePackageMutation({
                ...payload,
                id: msgId
            })
            if (response?.success) {
                toast.success("Package has been updated!")
            }
        } else {
            const response: any = addPackageMutation(payload)
            toast.success("Package created successfully!");
        }
    };

    return (
        <Main>
            <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
                    {typeof msgId === "string" ? "Update Package" : "Add Package"}
                </h1>
                <p className="text-muted-foreground">
                    {typeof msgId === "string" ? "Update Packge to the store." : "Add a new package to the store."}
                </p>
            </div>
            <Separator className="my-4 lg:my-3" />
            <div className="flex flex-1">
                <Form {...form}>
                    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full">
                        <Card>
                            <CardContent className="grid grid-cols-2 gap-4 mt-6">
                                {/* Package Name Fields for Multiple Languages */}
                                {languageMap.map(({ code, label }) => (
                                    <FormField
                                        key={code}
                                        control={control}
                                        name={code}
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Package Name ({label})</FormLabel>
                                                <FormControl>
                                                    <Input {...field} type="text" placeholder={`Package Name (${label})`} autoComplete="off" />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                ))}
                                {/* Amount Field */}
                                <FormField
                                    control={control}
                                    name="amount"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Amount</FormLabel>
                                            <FormControl>
                                                <Input {...field} inputMode="decimal" pattern="[0-9.]*" type="text" placeholder="Enter amount" autoComplete="off" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                {/* Discount Amount Field */}
                                <FormField
                                    control={control}
                                    name="discountAmount"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Discount Amount</FormLabel>
                                            <FormControl>
                                                <Input {...field} inputMode="decimal" pattern="[0-9.]*" type="text" placeholder="Enter discount amount" autoComplete="off" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                {/* Coins Field */}
                                <FormField
                                    control={control}
                                    name="coins"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Coins</FormLabel>
                                            <FormControl>
                                                <Input {...field} inputMode="numeric" pattern="[0-9]*" type="text" placeholder="Enter coins" autoComplete="off" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </CardContent>
                        </Card>
                        <div className="flex mt-4 justify-end">
                            <Button type="submit">
                                {typeof msgId === "string" ? "Update" : "Save"}
                            </Button>
                        </div>
                    </form>
                </Form>
            </div>
        </Main>
    );
} 