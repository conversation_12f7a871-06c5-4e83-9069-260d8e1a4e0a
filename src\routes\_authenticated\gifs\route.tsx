import { END_POINTS } from "@/features/members/utils/constant";
import CommonLayout from "@/features/gifs/common-layout";
import { createFileRoute } from "@tanstack/react-router";
import { roleGuards } from '@/utils/route-protection'

export const Route = createFileRoute<any>(`/_authenticated${END_POINTS.GIFS}`)({
  beforeLoad: ({ location }) => {
    roleGuards.managerAndAbove(location.pathname)
  },
  component: CommonLayout,
});
