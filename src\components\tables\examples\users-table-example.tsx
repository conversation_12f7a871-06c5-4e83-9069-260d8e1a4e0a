/**
 * Example: How to refactor existing users table to use the common DataTable component
 * This shows how the existing users-table.tsx could be simplified using our common component
 */

import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTable, DataTableFilter } from '@/components/tables'
import { DataTableColumnHeader } from '@/components/data-table-column-header'
import { cn } from '@/lib/utils'

// Example user type (should match your actual User type)
interface User {
  id: string
  username: string
  email: string
  status: 'active' | 'inactive' | 'invited' | 'suspended'
  role: string
  createdAt: string
}

// Column definitions using the common pattern
export const userColumns: ColumnDef<User>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    meta: {
      className: cn(
        'sticky md:table-cell left-0 z-10 rounded-tl',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted'
      ),
    },
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'username',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Username' />
    ),
    cell: ({ row }) => <div>{row.getValue('username')}</div>,
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Email' />
    ),
    cell: ({ row }) => <div>{row.getValue('email')}</div>,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string
      return (
        <div className={cn(
          'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
          {
            'bg-green-100 text-green-800': status === 'active',
            'bg-red-100 text-red-800': status === 'inactive',
            'bg-yellow-100 text-yellow-800': status === 'invited',
            'bg-gray-100 text-gray-800': status === 'suspended',
          }
        )}>
          {status}
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'role',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Role' />
    ),
    cell: ({ row }) => <div>{row.getValue('role')}</div>,
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Created At' />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue('createdAt'))
      return <div>{date.toLocaleDateString()}</div>
    },
  },
]

// Filter configurations
const userFilters: DataTableFilter[] = [
  {
    column: 'status',
    title: 'Status',
    options: [
      { label: 'Active', value: 'active' },
      { label: 'Inactive', value: 'inactive' },
      { label: 'Invited', value: 'invited' },
      { label: 'Suspended', value: 'suspended' },
    ],
  },
  {
    column: 'role',
    title: 'Role',
    options: [
      { label: 'Admin', value: 'admin' },
      { label: 'User', value: 'user' },
      { label: 'Moderator', value: 'moderator' },
    ],
  },
]

// Simplified Users Table Component
interface UsersTableProps {
  data: User[]
}

export function UsersTableExample({ data }: UsersTableProps) {
  return (
    <DataTable
      columns={userColumns}
      data={data}
      searchKey="username"
      searchPlaceholder="Filter users..."
      filters={userFilters}
      enableRowSelection={true}
      enableSorting={true}
      enableFiltering={true}
      enablePagination={true}
      showToolbar={true}
      showViewOptions={true}
      showPagination={true}
      pageSize={10}
    />
  )
}

// Sample data for testing
export const sampleUsers: User[] = [
  {
    id: '1',
    username: 'john_doe',
    email: '<EMAIL>',
    status: 'active',
    role: 'admin',
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    username: 'jane_smith',
    email: '<EMAIL>',
    status: 'active',
    role: 'user',
    createdAt: '2024-01-16T14:20:00Z',
  },
  {
    id: '3',
    username: 'bob_wilson',
    email: '<EMAIL>',
    status: 'inactive',
    role: 'moderator',
    createdAt: '2024-01-17T09:15:00Z',
  },
  {
    id: '4',
    username: 'alice_brown',
    email: '<EMAIL>',
    status: 'invited',
    role: 'user',
    createdAt: '2024-01-18T16:45:00Z',
  },
  {
    id: '5',
    username: 'charlie_davis',
    email: '<EMAIL>',
    status: 'suspended',
    role: 'user',
    createdAt: '2024-01-19T11:30:00Z',
  },
]

/**
 * Usage in a page component:
 * 
 * import { UsersTableExample, sampleUsers } from '@/components/tables/examples/users-table-example'
 * 
 * export function UsersPage() {
 *   return (
 *     <div className="space-y-4">
 *       <h1>Users Management</h1>
 *       <UsersTableExample data={sampleUsers} />
 *     </div>
 *   )
 * }
 */
