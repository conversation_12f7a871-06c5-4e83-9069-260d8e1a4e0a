const fs = require('fs');
const path = require('path');

// Profile value types to generate
const profileValueTypes = [
    'ethnicities',
    'religions',
    'hair-colors',
    'appearances',
    'eye-colors',
    'body-types',
    'star-signs',
    'smoking-habits',
    'drinking-habits',
    'best-features',
    'body-arts',
    'interests',
    'sexual-orientations'
];

// API endpoint mappings
const apiEndpointMappings = {
    'ethnicities': 'ethnicity',
    'religions': 'religion',
    'hair-colors': 'hair-color',
    'appearances': 'appearance',
    'eye-colors': 'eye-color',
    'body-types': 'body-type',
    'star-signs': 'star-sign',
    'smoking-habits': 'smoking-habit',
    'drinking-habits': 'drinking-habit',
    'best-features': 'best-feature',
    'body-arts': 'body-art',
    'interests': 'interest',
    'sexual-orientations': 'sexual-orientation'
};

// Function to create directory if it doesn't exist
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
}

// Function to generate API endpoints file
function generateApiEndpoints(profileType, apiEndpoint) {
    const content = `export const API_ENDPOINTS = {
    ${profileType.toUpperCase().replace(/-/g, '_')}: "/${apiEndpoint}",
    MASTER_LANGUAGES: "/master/languages"
};`;
    
    const filePath = path.join(__dirname, 'src', 'features', 'profile-values', profileType, 'api', 'api-endpoints.ts');
    ensureDirectoryExists(path.dirname(filePath));
    fs.writeFileSync(filePath, content);
    console.log(`Generated: ${filePath}`);
}

// Function to generate API index file
function generateApiIndex(profileType, apiEndpoint) {
    const className = profileType.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');
    const queryKey = profileType.replace(/-/g, '-');
    
    const content = `import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const get${className}Api = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.${profileType.toUpperCase().replace(/-/g, '_')}, {
                params,
            });
            return response?.data ?? {};
        },
        queryKey: ["${queryKey}-list"],
    });

export const add${className}Api = () =>
    useMutation({
        mutationFn: async (data: any) => {
            const response = await apiClient.post(API_ENDPOINTS.${profileType.toUpperCase().replace(/-/g, '_')}, data);
            return response?.data;
        },
    });

export const update${className}Api = () =>
    useMutation({
        mutationFn: async ({ id, data }: { id: string; data: any }) => {
            const response = await apiClient.put(\`\${API_ENDPOINTS.${profileType.toUpperCase().replace(/-/g, '_')}}/\${id}\`, data);
            return response?.data;
        },
    });

export const delete${className}Api = () =>
    useMutation({
        mutationFn: async (id: string) => {
            const response = await apiClient.delete(\`\${API_ENDPOINTS.${profileType.toUpperCase().replace(/-/g, '_')}}/\${id}\`);
            return response?.data;
        },
    });

export const get${className}Details = (id: string) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(\`\${API_ENDPOINTS.${profileType.toUpperCase().replace(/-/g, '_')}}/\${id}\`);
            return response?.data ?? {};
        },
        queryKey: ["${queryKey}-details", id],
        enabled: !!id,
    });

export const getMasterLanguagesApi = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES);
            return response?.data ?? {};
        },
        queryKey: ["get-${queryKey}-language-list"],
    });`;
    
    const filePath = path.join(__dirname, 'src', 'features', 'profile-values', profileType, 'api', 'index.ts');
    ensureDirectoryExists(path.dirname(filePath));
    fs.writeFileSync(filePath, content);
    console.log(`Generated: ${filePath}`);
}

// Function to generate types file
function generateTypes(profileType) {
    const className = profileType.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');
    const classNamePlural = className + 's';
    
    const content = `export interface ${className} {
    id: string;
    name: string;
    items: ${className}Item[];
    createdAt?: string;
    updatedAt?: string;
}

export interface ${className}Item {
    id: string;
    languageCode: string;
    message: string;
}

export interface ${className}FormData {
    [key: string]: string;
}

export interface ${className}ApiResponse {
    ${profileType.replace(/-/g, '_')}: ${className}[];
    meta: {
        page: number;
        limit: number;
        total: number;
        pages: number;
    };
}`;
    
    const filePath = path.join(__dirname, 'src', 'features', 'profile-values', profileType, 'data', 'types.ts');
    ensureDirectoryExists(path.dirname(filePath));
    fs.writeFileSync(filePath, content);
    console.log(`Generated: ${filePath}`);
}

// Function to generate index component
function generateIndex(profileType) {
    const className = profileType.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');
    const classNamePlural = className + 's';
    
    const content = `import { Main } from "@/components/layout/main";
import { ${classNamePlural}PrimaryButtons } from "./components/${profileType}-primary-buttons";
import { ${classNamePlural}Table } from "./components/${profileType}-table";
import { columns } from "./components/${profileType}-columns";

export default function ${classNamePlural}List() {
  return (
    <Main>
      <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            ${classNamePlural} List
          </h2>
        </div>
        <${classNamePlural}PrimaryButtons />
      </div>
      <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
        <${classNamePlural}Table columns={columns} />
      </div>
    </Main>
  );
}`;
    
    const filePath = path.join(__dirname, 'src', 'features', 'profile-values', profileType, 'index.tsx');
    ensureDirectoryExists(path.dirname(filePath));
    fs.writeFileSync(filePath, content);
    console.log(`Generated: ${filePath}`);
}

// Function to generate primary buttons component
function generatePrimaryButtons(profileType) {
    const className = profileType.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');
    const classNamePlural = className + 's';
    const endpoint = profileType.replace(/-/g, '-');
    
    const content = `import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";

export function ${classNamePlural}PrimaryButtons() {
  const navigate = useNavigate();

  return (
    <div className="flex items-center space-x-2">
      <Button
        onClick={() => navigate({ to: END_POINTS.${profileType.toUpperCase().replace(/-/g, '_')} + "/add" })}
        className="flex items-center space-x-2"
      >
        <Plus className="h-4 w-4" />
        <span>Add ${className}</span>
      </Button>
    </div>
  );
}`;
    
    const filePath = path.join(__dirname, 'src', 'features', 'profile-values', profileType, 'components', `${profileType}-primary-buttons.tsx`);
    ensureDirectoryExists(path.dirname(filePath));
    fs.writeFileSync(filePath, content);
    console.log(`Generated: ${filePath}`);
}

// Function to generate columns file
function generateColumns(profileType) {
    const className = profileType.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');
    const classNamePlural = className + 's';
    const endpoint = profileType.replace(/-/g, '-');
    
    const content = `import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";
import { ${className} } from "../data/types";

const ActionCell = ({ row }: { row: any }) => {
  const navigate = useNavigate();
  const ${profileType.replace(/-/g, '')} = row.original;

  return (
    <div className="flex items-center space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => navigate({ to: \`\${END_POINTS.${profileType.toUpperCase().replace(/-/g, '_')}}/update/\${${profileType.replace(/-/g, '')}.id}\` })}
      >
        <Edit className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() => {
          // Handle delete - you can add confirmation dialog here
          if (confirm("Are you sure you want to delete this ${profileType.replace(/-/g, ' ')}?")) {
            // Call delete API
          }
        }}
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
};

export const columns: ColumnDef<${className}>[] = [
  {
    accessorKey: "serialNumber",
    header: "S.No",
    meta: {
      className: "w-16",
    },
  },
  {
    accessorKey: "name",
    header: "Name",
    meta: {
      className: "w-48",
    },
  },
  {
    accessorKey: "items",
    header: "Languages",
    cell: ({ row }) => {
      const items = row.original.items || [];
      return (
        <div className="flex flex-wrap gap-1">
          {items.map((item, index) => (
            <span
              key={index}
              className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800"
            >
              {item.languageCode}
            </span>
          ))}
        </div>
      );
    },
    meta: {
      className: "w-64",
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    cell: ({ row }) => {
      const date = row.original.createdAt;
      return date ? new Date(date).toLocaleDateString() : "-";
    },
    meta: {
      className: "w-32",
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ActionCell,
    meta: {
      className: "w-32",
    },
  },
];`;
    
    const filePath = path.join(__dirname, 'src', 'features', 'profile-values', profileType, 'components', `${profileType}-columns.tsx`);
    ensureDirectoryExists(path.dirname(filePath));
    fs.writeFileSync(filePath, content);
    console.log(`Generated: ${filePath}`);
}

// Function to generate table component
function generateTable(profileType) {
    const className = profileType.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');
    const classNamePlural = className + 's';
    const queryKey = profileType.replace(/-/g, '-');
    
    const content = `import { useEffect, useState } from 'react'
import {
  ColumnDef,
  ColumnFiltersState,
  RowData,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { DataTableToolbar } from './data-table-toolbar'
import { PaginationControls } from '@/components/ui/PaginationControls'
import { get${className}Api } from '../api'

declare module '@tanstack/react-table' {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface ColumnMeta<TData extends RowData, TValue> {
    className?: string
  }
}

export function ${classNamePlural}Table({ columns }: any) {
  const [rowSelection, setRowSelection] = useState({})
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [sorting, setSorting] = useState<SortingState>([])
  const [filters, setFilters] = useState<any>({ search: '' })
  const [pageIndex, setPageIndex] = useState(0)
  const [pageSize, setPageSize] = useState(10)

  const { data = {}, refetch }: any = get${className}Api({
    page: pageIndex + 1,
    limit: pageSize,
    ...filters
  })

  // Inject serial numbers into the data for the current page
  const dataWithSerialNumbers = (data?.${profileType.replace(/-/g, '_')} || []).map((item: any, idx: number) => ({
    ...item,
    serialNumber: idx + 1 + (pageIndex * pageSize),
  }));

  const table = useReactTable({
    data: dataWithSerialNumbers,
    columns,
    pageCount: data?.meta?.pages ?? -1,
    manualPagination: true,
    state: {
      pagination: { pageIndex, pageSize },
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    onPaginationChange: (updater) => {
      const newState =
        typeof updater === 'function' ? updater({ pageIndex, pageSize }) : updater
      setPageIndex(newState.pageIndex)
      setPageSize(newState.pageSize)
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  })

  useEffect(() => {
    refetch()
  }, [pageIndex, filters.search, pageSize])

  const onPageChange = (pageIndex: any) => {
    setPageIndex(pageIndex)
  }

  const onFilterChanged = (filterValues: any, type: any) => {
    if (type === 0) {
      setPageIndex(0)
    }
    setFilters(filterValues)
  }

  return (
    <div className="space-y-4">
      <DataTableToolbar table={table} onFilterChanged={onFilterChanged} />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className={header.column.columnMeta?.className}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      className={cell.column.columnMeta?.className}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <PaginationControls
        table={table}
        onPageChange={onPageChange}
        totalPages={data?.meta?.pages || 0}
        currentPage={pageIndex + 1}
        totalItems={data?.meta?.total || 0}
      />
    </div>
  )
}`;
    
    const filePath = path.join(__dirname, 'src', 'features', 'profile-values', profileType, 'components', `${profileType}-table.tsx`);
    ensureDirectoryExists(path.dirname(filePath));
    fs.writeFileSync(filePath, content);
    console.log(`Generated: ${filePath}`);
}

// Function to generate toolbar component
function generateToolbar(profileType) {
    const content = `import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { X } from 'lucide-react'
import { useState } from 'react'

interface DataTableToolbarProps<TData> {
  readonly table: Table<TData>
  readonly onFilterChanged?: any
}

export function DataTableToolbar<TData>({
  table,
  onFilterChanged
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const [searchValue, setSearchValue] = useState('')

  const handleSearch = (value: string) => {
    setSearchValue(value)
    onFilterChanged({ search: value }, 1)
  }

  const handleClear = () => {
    setSearchValue('')
    onFilterChanged({ search: '' }, 0)
  }

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Filter by name..."
          value={searchValue}
          onChange={(event) => handleSearch(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        />
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={handleClear}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
}`;
    
    const filePath = path.join(__dirname, 'src', 'features', 'profile-values', profileType, 'components', 'data-table-toolbar.tsx');
    ensureDirectoryExists(path.dirname(filePath));
    fs.writeFileSync(filePath, content);
    console.log(`Generated: ${filePath}`);
}

// Function to generate add component
function generateAddComponent(profileType) {
    const className = profileType.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');
    const endpoint = profileType.toUpperCase().replace(/-/g, '_');
    
    const content = `import { Main } from "@/components/layout/main";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { add${className}Api, getMasterLanguagesApi } from "../api";
import { useNavigate } from "@tanstack/react-router";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { END_POINTS } from "@/features/members/utils/constant";

// Language mapping for display names
const LANGUAGE_NAMES: Record<string, string> = {
    en: "English",
    fr: "French",
    de: "German",
    nl: "Dutch",
    da: "Danish",
    fi: "Finnish",
    it: "Italian",
    no: "Norwegian",
    pl: "Polish",
    pt: "Portuguese",
    el: "Greek",
    es: "Spanish",
    sv: "Swedish",
    // Add more languages as needed
};

export default function Add${className}() {
    const navigate = useNavigate()
    const { data: { languages = [] } = {} } = getMasterLanguagesApi()
    const { mutateAsync: add${className}Mutation } = add${className}Api();
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Create dynamic schema based on available languages
    const ${className}Schema = useMemo(() => {
        const schemaObject: Record<string, z.ZodString> = {};

        // If languages are available from API, use them
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                const languageCode = lang.code || lang.languageCode || lang.id;
                if (languageCode) {
                    schemaObject[languageCode] = z.string().min(1, "${className} name is required");
                }
            });
        } else {
            // Use default languages if API doesn't provide them
            const defaultLanguages = ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'];
            defaultLanguages.forEach(lang => {
                schemaObject[lang] = z.string().min(1, "${className} name is required");
            });
        }

        return z.object(schemaObject);
    }, [languages]);

    type ${className}FormValues = z.infer<typeof ${className}Schema>;

    // Create dynamic default values
    const defaultValues = useMemo(() => {
        const defaults: Record<string, string> = {};

        // If languages are available from API, use them
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                const languageCode = lang.code || lang.languageCode || lang.id;
                if (languageCode) {
                    defaults[languageCode] = "";
                }
            });
        } else {
            // Use default languages if API doesn't provide them
            const defaultLanguages = ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'];
            defaultLanguages.forEach(lang => {
                defaults[lang] = "";
            });
        }

        return defaults;
    }, [languages]);

    const form = useForm<${className}FormValues>({
        resolver: zodResolver(${className}Schema),
        defaultValues: defaultValues as ${className}FormValues,
    });

    const { control, handleSubmit } = form;

    const onSubmit = async (values: ${className}FormValues) => {
        try {
            setIsSubmitting(true);

            // Transform the form data to match API structure
            const items = Object.entries(values).map(([languageCode, message]) => ({
                languageCode,
                message
            }));

            const payload = {
                items
            };

            await add${className}Mutation(payload);
            toast.success("${className} added successfully!");
            navigate({ to: END_POINTS.${endpoint} });
        } catch (error: any) {
            console.error("Error adding ${profileType.replace(/-/g, ' ')}:", error);
            toast.error(error?.response?.data?.message || "Failed to add ${profileType.replace(/-/g, ' ')}");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">
                        Add ${className}
                    </h2>
                </div>
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <Card>
                    <CardHeader>
                        <CardTitle>${className} Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                                <div className="grid gap-4">
                                    {languages && languages.length > 0 ? (
                                        languages.map((lang: any) => {
                                            const languageCode = lang.code || lang.languageCode || lang.id;
                                            const languageName = lang.name || LANGUAGE_NAMES[languageCode] || languageCode;
                                            
                                            return (
                                                <FormField
                                                    key={languageCode}
                                                    control={control}
                                                    name={languageCode as keyof ${className}FormValues}
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel className="text-sm font-medium">
                                                                {languageName}
                                                            </FormLabel>
                                                            <FormControl>
                                                                <Input
                                                                    placeholder={\`Enter ${profileType.replace(/-/g, ' ')} name in \${languageName}\`}
                                                                    {...field}
                                                                />
                                                            </FormControl>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                            );
                                        })
                                    ) : (
                                        // Fallback to default languages
                                        ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].map(lang => (
                                            <FormField
                                                key={lang}
                                                control={control}
                                                name={lang as keyof ${className}FormValues}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="text-sm font-medium">
                                                            {LANGUAGE_NAMES[lang]}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                placeholder={\`Enter ${profileType.replace(/-/g, ' ')} name in \${LANGUAGE_NAMES[lang]}\`}
                                                                {...field}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        ))
                                    )}
                                </div>
                                <Separator />
                                <div className="flex justify-end space-x-2">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => navigate({ to: END_POINTS.${endpoint} })}
                                    >
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={isSubmitting}>
                                        {isSubmitting ? "Adding..." : "Add ${className}"}
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </Main>
    );
}`;
    
    const filePath = path.join(__dirname, 'src', 'features', 'profile-values', profileType, 'add-${profileType.replace(/-/g, '')}', 'index.tsx');
    ensureDirectoryExists(path.dirname(filePath));
    fs.writeFileSync(filePath, content);
    console.log(`Generated: ${filePath}`);
}

// Function to generate update component
function generateUpdateComponent(profileType) {
    const className = profileType.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');
    const endpoint = profileType.toUpperCase().replace(/-/g, '_');
    
    const content = `import { Main } from "@/components/layout/main";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { update${className}Api, get${className}Details, getMasterLanguagesApi } from "../api";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { END_POINTS } from "@/features/members/utils/constant";

// Language mapping for display names
const LANGUAGE_NAMES: Record<string, string> = {
    en: "English",
    fr: "French",
    de: "German",
    nl: "Dutch",
    da: "Danish",
    fi: "Finnish",
    it: "Italian",
    no: "Norwegian",
    pl: "Polish",
    pt: "Portuguese",
    el: "Greek",
    es: "Spanish",
    sv: "Swedish",
    // Add more languages as needed
};

export default function Update${className}() {
    const navigate = useNavigate()
    const { ${profileType.replace(/-/g, '')}Id } = useParams({ strict: false });
    const { data: { languages = [] } = {} } = getMasterLanguagesApi()
    const { mutateAsync: update${className}Mutation } = update${className}Api();
    const { data = {} } = get${className}Details(${profileType.replace(/-/g, '')}Id);
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Create dynamic schema based on available languages
    const ${className}Schema = useMemo(() => {
        const schemaObject: Record<string, z.ZodString> = {};

        // If languages are available from API, use them
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                const languageCode = lang.code || lang.languageCode || lang.id;
                if (languageCode) {
                    schemaObject[languageCode] = z.string().min(1, "${className} name is required");
                }
            });
        } else {
            // Use default languages if API doesn't provide them
            const defaultLanguages = ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'];
            defaultLanguages.forEach(lang => {
                schemaObject[lang] = z.string().min(1, "${className} name is required");
            });
        }

        return z.object(schemaObject);
    }, [languages]);

    type ${className}FormValues = z.infer<typeof ${className}Schema>;

    // Create dynamic default values
    const defaultValues = useMemo(() => {
        const defaults: Record<string, string> = {};

        // If languages are available from API, use them
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                const languageCode = lang.code || lang.languageCode || lang.id;
                if (languageCode) {
                    defaults[languageCode] = "";
                }
            });
        } else {
            // Use default languages if API doesn't provide them
            const defaultLanguages = ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'];
            defaultLanguages.forEach(lang => {
                defaults[lang] = "";
            });
        }

        return defaults;
    }, [languages]);

    const form = useForm<${className}FormValues>({
        resolver: zodResolver(${className}Schema),
        defaultValues: defaultValues as ${className}FormValues,
    });

    // Prefill form when data.items is available
    useEffect(() => {
        if (data.items) {
            const itemsObject = (data.items || []).reduce((acc: any, item: any) => {
                acc[item.languageCode] = item.message;
                return acc;
            }, {});
            form.reset({
                ...form.getValues(),
                ...itemsObject
            });
        }
    }, [data, form]);

    const { control, handleSubmit } = form;

    const onSubmit = async (values: ${className}FormValues) => {
        try {
            setIsSubmitting(true);

            // Transform the form data to match API structure
            const items = Object.entries(values).map(([languageCode, message]) => ({
                languageCode,
                message
            }));

            const payload = {
                items
            };

            await update${className}Mutation({ id: ${profileType.replace(/-/g, '')}Id, data: payload });
            toast.success("${className} updated successfully!");
            navigate({ to: END_POINTS.${endpoint} });
        } catch (error: any) {
            console.error("Error updating ${profileType.replace(/-/g, ' ')}:", error);
            toast.error(error?.response?.data?.message || "Failed to update ${profileType.replace(/-/g, ' ')}");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">
                        Update ${className}
                    </h2>
                </div>
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <Card>
                    <CardHeader>
                        <CardTitle>${className} Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                                <div className="grid gap-4">
                                    {languages && languages.length > 0 ? (
                                        languages.map((lang: any) => {
                                            const languageCode = lang.code || lang.languageCode || lang.id;
                                            const languageName = lang.name || LANGUAGE_NAMES[languageCode] || languageCode;
                                            
                                            return (
                                                <FormField
                                                    key={languageCode}
                                                    control={control}
                                                    name={languageCode as keyof ${className}FormValues}
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel className="text-sm font-medium">
                                                                {languageName}
                                                            </FormLabel>
                                                            <FormControl>
                                                                <Input
                                                                    placeholder={\`Enter ${profileType.replace(/-/g, ' ')} name in \${languageName}\`}
                                                                    {...field}
                                                                />
                                                            </FormControl>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                            );
                                        })
                                    ) : (
                                        // Fallback to default languages
                                        ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].map(lang => (
                                            <FormField
                                                key={lang}
                                                control={control}
                                                name={lang as keyof ${className}FormValues}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="text-sm font-medium">
                                                            {LANGUAGE_NAMES[lang]}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                placeholder={\`Enter ${profileType.replace(/-/g, ' ')} name in \${LANGUAGE_NAMES[lang]}\`}
                                                                {...field}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        ))
                                    )}
                                </div>
                                <Separator />
                                <div className="flex justify-end space-x-2">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => navigate({ to: END_POINTS.${endpoint} })}
                                    >
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={isSubmitting}>
                                        {isSubmitting ? "Updating..." : "Update ${className}"}
                                        </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </Main>
    );
}`;
    
    const filePath = path.join(__dirname, 'src', 'features', 'profile-values', profileType, 'update-${profileType.replace(/-/g, '')}', 'index.tsx');
    ensureDirectoryExists(path.dirname(filePath));
    fs.writeFileSync(filePath, content);
    console.log(`Generated: ${filePath}`);
}

// Main execution
console.log('Starting to generate profile value types...');

profileValueTypes.forEach(profileType => {
    console.log(`\nGenerating ${profileType}...`);
    
    const apiEndpoint = apiEndpointMappings[profileType];
    
    // Generate all files for this profile type
    generateApiEndpoints(profileType, apiEndpoint);
    generateApiIndex(profileType, apiEndpoint);
    generateTypes(profileType);
    generateIndex(profileType);
    generatePrimaryButtons(profileType);
    generateColumns(profileType);
    generateTable(profileType);
    generateToolbar(profileType);
    generateAddComponent(profileType);
    generateUpdateComponent(profileType);
    
    console.log(`✅ Generated all files for ${profileType}`);
});

console.log('\n🎉 All profile value types have been generated successfully!');
console.log('\nNext steps:');
console.log('1. Run the script to generate all remaining profile value types');
console.log('2. Create the routing files for each profile value type');
console.log('3. Update the route protection file with new endpoints');
console.log('4. Test the functionality');
