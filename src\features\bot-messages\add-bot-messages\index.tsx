import { Main } from "@/components/layout/main";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { addBotMessageApi, getBotMessageDetails, getMastrLanguagesApi, updateBotMessageApi } from "../api";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { END_POINTS } from "@/features/members/utils/constant";

// Language mapping for display names
const LANGUAGE_NAMES: Record<string, string> = {
    en: "English",
    fr: "French",
    de: "German",
    nl: "Dutch",
    da: "Danish",
    fi: "Finnish",
    it: "Italian",
    no: "Norwegian",
    pl: "Polish",
    pt: "Portuguese",
    el: "Greek",
    es: "Spanish",
    sv: "Swedish",
    // Add more languages as needed
};

export default function AddBotMessage() {
    const navigate = useNavigate()
    const { data: { languages = [] } = {} } = getMastrLanguagesApi()
    const { mutateAsync: addBotMessageMutation } = addBotMessageApi();
    const { mutateAsync: updateBotMessageMutation } = updateBotMessageApi()
    const { msgId } = useParams({ strict: false });
    const { data = {} } = getBotMessageDetails(msgId);
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Create dynamic schema based on available languages
    const botMessageSchema = useMemo(() => {
        const languageSchemaObject: Record<string, z.ZodString> = {};

        // If languages are available from API, use them
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                const languageCode = lang.code || lang.languageCode || lang.id;
                if (languageCode) {
                    languageSchemaObject[languageCode] = z.string().min(1, "Message is required");
                }
            });
        } else {
            // Use default languages if API doesn't provide them
            const defaultLanguages = ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'];
            defaultLanguages.forEach(lang => {
                languageSchemaObject[lang] = z.string().min(1, "Message is required");
            });
        }

        return z.object({
            ...languageSchemaObject,
            type: z.enum(["regular", "new"]),
            preference: z.enum(["male", "female"]),
            order: z.coerce.number().min(0, "Order is required"),
        });
    }, [languages]);

    type BotMessageFormValues = z.infer<typeof botMessageSchema>;

    // Create dynamic default values
    const defaultValues = useMemo(() => {
        const defaults: Record<string, string | number> = {
            type: "regular",
            preference: "male",
            order: 0,
        };

        // If languages are available from API, use them
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                const languageCode = lang.code || lang.languageCode || lang.id;
                if (languageCode) {
                    defaults[languageCode] = "";
                }
            });
        } else {
            // Use default languages if API doesn't provide them
            const defaultLanguages = ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'];
            defaultLanguages.forEach(lang => {
                defaults[lang] = "";
            });
        }

        return defaults;
    }, [languages]);

    const form = useForm<BotMessageFormValues>({
        resolver: zodResolver(botMessageSchema),
        defaultValues: defaultValues as BotMessageFormValues,
    });

    // Prefill form when data.items is available
    useEffect(() => {
        if (data.items) {
            const itemsObject = (data.items || []).reduce((acc: any, item: any) => {
                acc[item.languageCode] = item.message;
                return acc;
            }, {});
            form.reset({
                ...form.getValues(),
                ...itemsObject,
                ...(data.type && { type: data.type }),
                ...(data.preference && { preference: data.preference }),
                ...(data.order !== undefined && { order: data.order }),
            });
        }
    }, [data, form]);

    const { control, handleSubmit } = form;

    const onSubmit = async (values: BotMessageFormValues) => {
        setIsSubmitting(true);

        try {
            // Dynamically create items array based on form values, excluding non-language fields
            const { type, preference, order, ...languageValues } = values;
            const items = Object.entries(languageValues).map(([languageCode, message]) => ({
                languageCode,
                message: message as string
            }));

            const payload = {
                items,
                type,
                preference,
                order,
            };

            if (typeof msgId === "string") {
                const response: any = await updateBotMessageMutation({
                    ...payload,
                    id: data?.id
                })
                if (response?.success) {
                    toast.success("Bot Message has been updated!")
                    navigate({ to: END_POINTS.BOT_MESSAGES });
                }
            } else {
                const response: any = await addBotMessageMutation(payload);
                if (response?.success) {
                    toast.success("Bot Message has been created!")
                    navigate({ to: END_POINTS.BOT_MESSAGES });
                }
            }
        } catch (error) {
            toast.error("An error occurred while saving the bot message")
        } finally {
            setIsSubmitting(false);
        }
    }

    return (
        <Main>
            <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
                    Create Bot Message
                </h1>
                <p className="text-muted-foreground">Create a new bot message.</p>
            </div>
            <Separator className="my-4 lg:my-3" />
            <div className="flex flex-1 flex-col space-y-2 overflow-hidden md:space-y-2 xl:flex-row lg:space-y-0 lg:space-x-12">
                <div className="flex w-full overflow-y-hidden p-1">
                    <Form {...form}>
                        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Bot Message Details</CardTitle>
                                </CardHeader>
                                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                                    {Object.keys(defaultValues).filter(key => !['type', 'preference', 'order'].includes(key)).length === 0 ? (
                                        <div className="col-span-full text-center py-8 text-muted-foreground">
                                            Loading languages...
                                        </div>
                                    ) : (
                                        Object.keys(defaultValues)
                                            .filter(key => !['type', 'preference', 'order'].includes(key))
                                            .map((languageCode) => {
                                                const languageName = LANGUAGE_NAMES[languageCode] || languageCode.toUpperCase();

                                                return (
                                                    <FormField
                                                        key={languageCode}
                                                        name={languageCode as keyof BotMessageFormValues}
                                                        control={control}
                                                        render={({ field }) => (
                                                            <FormItem>
                                                                <FormLabel className="flex items-center gap-2">
                                                                    {languageName}
                                                                    <span className="text-xs text-muted-foreground">({languageCode})</span>
                                                                </FormLabel>
                                                                <FormControl>
                                                                    <Input
                                                                        {...field}
                                                                        placeholder={`Enter bot message in ${languageName}`}
                                                                        className="min-h-[40px]"
                                                                    />
                                                                </FormControl>
                                                                <FormMessage />
                                                            </FormItem>
                                                        )}
                                                    />
                                                );
                                            })
                                    )}
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader>
                                    <CardTitle>Type & Preference</CardTitle>
                                </CardHeader>
                                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                                    <FormField name="type" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Type</FormLabel>
                                            <FormControl>
                                                <RadioGroup onValueChange={field.onChange} value={field.value} className="flex flex-row gap-4">
                                                    <div className="flex items-center gap-2">
                                                        <RadioGroupItem value="regular" id="type-regular" />
                                                        <label htmlFor="type-regular">Regular Member</label>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <RadioGroupItem value="new" id="type-new" />
                                                        <label htmlFor="type-new">New Member</label>
                                                    </div>
                                                </RadioGroup>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="preference" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Preference</FormLabel>
                                            <FormControl>
                                                <RadioGroup onValueChange={field.onChange} value={field.value} className="flex flex-row gap-4">
                                                    <div className="flex items-center gap-2">
                                                        <RadioGroupItem value="male" id="pref-male" />
                                                        <label htmlFor="pref-male">For Male</label>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <RadioGroupItem value="female" id="pref-female" />
                                                        <label htmlFor="pref-female">For Female</label>
                                                    </div>
                                                </RadioGroup>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                    <FormField name="order" control={control} render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Order</FormLabel>
                                            <FormControl><Input type="number" {...field} /></FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )} />
                                </CardContent>
                            </Card>
                            <div className="md:col-span-2 flex mt-4 justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => navigate({ to: END_POINTS.BOT_MESSAGES })}
                                    disabled={isSubmitting}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={isSubmitting || Object.keys(defaultValues).filter(key => !['type', 'preference', 'order'].includes(key)).length === 0}
                                >
                                    {isSubmitting ? "Saving..." : (typeof msgId === "string" ? "Update" : "Save")}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </div>
            </div>
        </Main>
    );
} 