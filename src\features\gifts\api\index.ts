import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";
import axios from "axios";


export const getGiftsApi = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.GIFTS, {
                params,
            });
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["gifts-list"],
    });


export const addGiftApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.GIFTS, payload);
        },
    });


export const updateGiftApi = () =>
    useMutation({
        mutationFn: async ({ id, ...payload }: any) => {
            return await apiClient.put(`${API_ENDPOINTS.GIFTS}/${id}`, payload);
        },
    });

export const getGiftDetails = (id: any = {}) =>
    useQuery({
        queryFn: async () => {
            if (typeof id === 'string') {
                const response = await apiClient.get(`${API_ENDPOINTS.GIFTS}/${id}`)
                return response?.data ?? {}; // return [] or {} as a fallback
            }
            return {}
        },
        queryKey: ["gift-details", id],
        enabled: !!id
    });

export const deleteGiftApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.delete(`${API_ENDPOINTS.GIFTS}/${payload?.id}`);
        },
    });

export const getPresignedUrl = () =>
    useMutation({
        mutationFn: async (params: any) => {
            return await apiClient.get(`${API_ENDPOINTS.S3_PRESIGNED_URL}`, { params });
        },
    });

export const uploadFileToS3 = () =>
    useMutation({
        mutationFn: async ({ url, file }: any) => {
            return await axios.put(url, file);
        },
    });