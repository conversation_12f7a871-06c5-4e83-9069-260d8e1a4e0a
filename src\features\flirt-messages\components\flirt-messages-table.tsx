import { useEffect, useState } from "react";
import {
    ColumnDef, useReactTable, getCoreRowModel, getPaginationRowModel, getSortedRowModel, getFilteredRowModel, flexRender,
    VisibilityState, SortingState, ColumnFiltersState, getFacetedUniqueValues, getFacetedRowModel,
} from "@tanstack/react-table";
import {
    Table, TableBody, TableCell, TableHead, TableHeader, TableRow,
} from "@/components/ui/table";
import { PaginationControls } from "@/components/ui/PaginationControls";
import { getFlirtMessageApi } from "../api";

interface DataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
}

export function FlirtMessagesTable<TData, TValue>({ columns }: DataTableProps<TData, TValue>) {
    const [rowSelection, setRowSelection] = useState({})
    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [sorting, setSorting] = useState<SortingState>([])
    const [pageIndex, setPageIndex] = useState(0)
    const [pageSize, setPageSize] = useState(10)

    const { data = {}, refetch }: any = getFlirtMessageApi({
        page: pageIndex + 1,
        limit: pageSize,
    })

    // Inject serial numbers into the data for the current page
    const dataWithSerialNumbers = (data?.messages || []).map((item: any, idx: number) => ({
        ...item,
        serialNumber: idx + 1 + (pageIndex * pageSize),
    }));

    useEffect(() => {
        refetch()
    }, [pageIndex, pageSize])

    const table = useReactTable({
        data: dataWithSerialNumbers,
        columns,
        pageCount: data?.meta?.pages ?? -1,
        manualPagination: true,
        state: {
            pagination: { pageIndex, pageSize },
            sorting,
            columnVisibility,
            rowSelection,
            columnFilters,
        },
        onPaginationChange: (updater) => {
            const newState =
                typeof updater === 'function' ? updater({ pageIndex, pageSize }) : updater
            setPageIndex(newState.pageIndex)
            setPageSize(newState.pageSize)
        },
        enableRowSelection: true,
        onRowSelectionChange: setRowSelection,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
    })

    const onPageChange = (pageIndex: any) => {
        setPageIndex(pageIndex);
    };

    return (
        <div className="space-y-4">
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id} colSpan={header.colSpan}>
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(header.column.columnDef.header, header.getContext())}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow key={row.id}>
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="h-24 text-center">
                                    No results.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
            <PaginationControls table={table} meta={data?.meta} onPageChange={onPageChange} />
        </div>
    );
} 