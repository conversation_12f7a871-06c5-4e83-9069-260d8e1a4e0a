import { END_POINTS } from "@/features/members/utils/constant";
import List from "@/features/moderators";
import { createFileRoute } from "@tanstack/react-router";
import { roleGuards } from '@/utils/route-protection'

export const Route = createFileRoute<any>(`/_authenticated${END_POINTS.MODERATORS}/`)({
  beforeLoad: ({ location }) => {
    roleGuards.adminOnly(location.pathname)
  },
  component: List,
});
