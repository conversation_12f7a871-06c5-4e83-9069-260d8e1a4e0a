import { createFileRoute } from '@tanstack/react-router'
import Settings from '@/features/settings'
import { END_POINTS } from '@/features/members/utils/constant'
import { roleGuards } from '@/utils/route-protection'

export const Route = createFileRoute<any>(`/_authenticated${END_POINTS.SETTINGS}/`)({
  beforeLoad: ({ location }) => {
    roleGuards.adminOnly(location.pathname)
  },
  component: Settings,
})
