import { Main } from "@/components/layout/main";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { Separator } from "@radix-ui/react-separator";
import { useForm } from "react-hook-form";
import { useNavigate, useParams } from "@tanstack/react-router";
import { toast } from "sonner";
import { useEffect, useMemo, useRef, useState } from "react";
import { END_POINTS } from "@/features/members/utils/constant";
import { addRelationshipStatusApi, updateRelationshipStatusApi, getRelationshipStatusDetails, getMasterLanguagesApi } from "../api";
import { RelationshipStatusFormData } from "../data/types";
import * as z from "zod";

export default function ManageRelationshipStatus() {
    const navigate = useNavigate();
    const { relationshipStatusId } = useParams({ strict: false });
    const { data: { languages = [] } = {} } = getMasterLanguagesApi();
    const { mutateAsync: addRelationshipStatusMutation } = addRelationshipStatusApi();
    const { mutateAsync: updateRelationshipStatusMutation } = updateRelationshipStatusApi();
    const { data = {} } = getRelationshipStatusDetails(relationshipStatusId || "");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const initializedRef = useRef(false)

    // Dynamic schema based on available languages
    const relationshipStatusSchema = useMemo(() => {
        const schemaFields: any = {};
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                schemaFields[lang.code] = z.string().min(1, `${lang.name} name is required`);
            });
        } else {
            // Fallback schema with common languages
            ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].forEach(lang => {
                schemaFields[lang] = z.string().min(1, `${lang.toUpperCase()} name is required`);
            });
        }
        return z.object(schemaFields);
    }, [languages]);

    // Dynamic default values based on available languages
    const defaultValues = useMemo(() => {
        const values: any = {};
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                values[lang.code] = (data as any)?.items?.find((it: any) => it.languageCode === lang.code)?.message || '';
            });
        } else {
            // Fallback default values
            ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].forEach(lang => {
                values[lang] = (data as any)?.items?.find((it: any) => it.languageCode === lang)?.message || '';
            });
        }
        return values;
    }, [languages, data]);

    const form = useForm<RelationshipStatusFormData>({
        resolver: zodResolver(relationshipStatusSchema),
        defaultValues,
    });

    // Pre-fill form when editing (one-time init)
    useEffect(() => {
        if (initializedRef.current) return
        if ((languages && languages.length > 0) || relationshipStatusId) {
            form.reset(defaultValues)
            initializedRef.current = true
        }
    }, [languages, relationshipStatusId, defaultValues, form])

    const onSubmit = async (values: RelationshipStatusFormData) => {
        try {
            const payload = {
                items: Object.entries(values).map(([languageCode, message]) => ({ languageCode, message }))
            };

            if (relationshipStatusId) {
                await updateRelationshipStatusMutation({ id: relationshipStatusId, data: payload });
                toast.success("Relationship status updated successfully!");
            } else {
                await addRelationshipStatusMutation(payload);
                toast.success("Relationship status added successfully!");
            }
            navigate({ to: END_POINTS.RELATIONSHIP_STATUS });
        } catch (error) {
            toast.error("Something went wrong!");
        }
    };

    const isEditing = !!relationshipStatusId;

    return (
        <Main>
            <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
                    {isEditing ? "Update Relationship Status" : "Add Relationship Status"}
                </h1>
                <p className="text-muted-foreground">
                    {isEditing ? "Update relationship status details." : "Add a new relationship status."}
                </p>
            </div>
            <Separator className="my-4 lg:my-6" />

            <Card>
                <CardHeader>
                    <CardTitle>Relationship Status Information</CardTitle>
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <div className="grid grid-cols-2 gap-4">
                                {languages && languages.length > 0 ? (
                                    languages.map((lang: any) => {
                                        const languageName = lang.name || lang.code.toUpperCase();
                                        return (
                                            <FormField
                                                key={lang.code}
                                                control={form.control}
                                                name={lang.code as keyof RelationshipStatusFormData}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="text-sm font-medium">
                                                            {languageName}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                placeholder={`Enter relationship status name in ${languageName}`}
                                                                {...field}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        );
                                    })
                                ) : (
                                    ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].map(lang => {
                                        const languageName = lang.toUpperCase();
                                        return (
                                            <FormField
                                                key={lang}
                                                control={form.control}
                                                name={lang as keyof RelationshipStatusFormData}
                                                render={({ field }) => (
                                                    <FormItem>
                                                        <FormLabel className="text-sm font-medium">
                                                            {languageName}
                                                        </FormLabel>
                                                        <FormControl>
                                                            <Input
                                                                placeholder={`Enter relationship status name in ${languageName}`}
                                                                {...field}
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}
                                            />
                                        );
                                    })
                                )}
                            </div>
                            <Separator />
                            <div className="flex justify-end space-x-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => navigate({ to: END_POINTS.RELATIONSHIP_STATUS })}
                                >
                                    Cancel
                                </Button>
                                <Button type="submit">
                                    {isEditing ? "Update Relationship Status" : "Add Relationship Status"}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </CardContent>
            </Card>
        </Main>
    );
}
