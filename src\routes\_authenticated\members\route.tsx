import CommonLayout from "@/features/members/common-layout";
import { END_POINTS } from "@/features/members/utils/constant";
import { createFileRoute } from "@tanstack/react-router";
import { roleGuards } from '@/utils/route-protection'

export const Route = createFileRoute<any>(`/_authenticated${END_POINTS.MEMBERS}`)({
  beforeLoad: ({ location }) => {
    roleGuards.managerAndAbove(location.pathname)
  },
  component: () => <CommonLayout />,
});
