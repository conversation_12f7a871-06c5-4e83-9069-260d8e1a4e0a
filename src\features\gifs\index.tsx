import { Main } from "@/components/layout/main";
import { GifTable } from "./components/gif-table";
import { columns } from "./components/gif-columns";
import { GifPrimaryButtons } from "./components/gif-primary-button";

export default function List() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">
                        GIFs
                    </h2>
                </div>
                <GifPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <GifTable columns={columns} />
            </div>
        </Main>
    );
}
