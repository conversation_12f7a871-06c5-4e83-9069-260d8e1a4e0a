"use client";

import { Main } from "@/components/layout/main";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { Separator } from "@radix-ui/react-separator";
import { useForm } from "react-hook-form";
import { modelCreateSchema, ModelFormValues } from "./data/models";
import { addModelApi, updateModelApi, getModelDetails } from "./api";
import { useEffect, useMemo } from "react";
import { useNavigate, useParams } from "@tanstack/react-router";
import { toast } from "sonner";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { X } from "lucide-react";
import { END_POINTS } from "../members/utils/constant";
import { getCountryList, getMasterApi } from "../members/api";
import { Command, CommandGroup, CommandItem, CommandInput, CommandEmpty } from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import CityAutocomplete from "@/components/CityAutocomplete";

export default function CreateModel() {
    type MasterOption = { id: string | number; title: string };

    const navigate = useNavigate()
    const { mutateAsync: addModelMutation } = addModelApi()
    const { mutateAsync: updateModelMutation } = updateModelApi()
    const { modelId } = useParams({ strict: false })
    const { data = {} }: any = getModelDetails(modelId)
    const { data: masterData } = getMasterApi()
    const { data: countryList } = getCountryList()

    // Helper function to get days in month
    const getDaysInMonth = (month: number, year: number) => {
        return new Date(year, month, 0).getDate();
    };

    const masterOptions = useMemo(() => {
        const master = masterData?.master || {};
        return {
            appearance: master.appearance || [],
            relationshipStatus: master.relationship_status || [],
            personality: master.personality || [],
            eyeColor: master.eye_color || [],
            bodyType: master.body_type || [],
            hairColor: master.hair_color || [],
            smokingHabits: master.smoking_habits || [],
            drinkingHabits: master.drinking_habits || [],
            bestFeature: master.best_feature || [],
            bodyArt: master.body_art || [],
            sexualOrientation: master.sexual_orientation || [],
            ethnicity: master.ethnicity || [],
            seekingFor: master.seeking_for || [],
            gender: master.gender || [],
            interest: master.interest || [],
            modelGroup: master.model_group || [],
        };
    }, [masterData]);


    const form = useForm<ModelFormValues>({
        resolver: zodResolver(modelCreateSchema),
        defaultValues: {
            username: data?.username || "",
            personality: String(data?.personality?.id || ""),
            relationshipStatus: String(data?.relationshipStatus?.id || ""),
            hairColor: String(data?.hairColor?.id || ""),
            ethnicity: String(data?.ethnicity?.id || ""),
            appearance: String(data?.appearance?.id || ""),
            eyeColor: String(data?.eyeColor?.id || ""),
            bodyType: String(data?.bodyType?.id || ""),
            smokingHabit: String(data?.smokingHabit?.id || ""),
            bodyArt: String(data?.bodyArt?.id || ""),
            drinkingHabit: String(data?.drinkingHabit?.id || ""),
            bestFeature: String(data?.bestFeature?.id || ""),
            sexualOrientation: String(data?.sexualOrientation?.id || ""),
            kids: data?.kids || "",
            interest: data?.interest ? [Number(data?.interest?.id)] : [],
            seekingFor: String(data?.seekingFor?.id || ""),
            gender: String(data?.gender?.id || ""),
            aboutMe: data?.aboutMe || "",
            domainType: data?.domainType || "",
            modelGroup: String(data?.modelGroup?.id || ""),
            country: String(data?.country?.id || ""),
            city: data?.city || "",
            height: data?.height || "",
            weight: data?.weight || "",
            day: "",
            month: "",
            year: "",
        },
    });

    const { control, handleSubmit, setValue } = form;
    const selectedCountryId = form.watch('country');
    const selectedCountry = countryList?.country?.find((c: any) => String(c.id) === String(selectedCountryId));

    useEffect(() => {
        if (data?.username) {
            const dropdownKeys = [
                'appearance',
                'hairColor',
                'eyeColor',
                'seekingFor',
                'personality',
                'relationshipStatus',
                'ethnicity',
                'bodyType',
                'smokingHabit',
                'drinkingHabit',
                'bestFeature',
                'bodyArt',
                'sexualOrientation',
                'gender',
                'interest',
                "modelGroup"
            ];
            dropdownKeys.forEach((key: any) => {
                if (data[key] && data[key].id !== undefined) {
                    setValue(key, String(data[key].id));
                }
            });
            // Set country from data.country
            if (data?.country && data?.country?.id !== undefined) {
                setValue('country', String(data?.country?.id));
            }
            // Set other fields as needed
            if (data?.kids) setValue('kids', data?.kids);
            if (data?.aboutMe !== undefined) setValue('aboutMe', data?.aboutMe);

            if (data.height !== undefined) setValue('height', data.height);
            if (data.weight !== undefined) setValue('weight', data.weight);
            if (data.city) setValue('city', data.city);
            if (data.username) setValue('username', data.username);
            if (data.domainType) setValue('domainType', data.domainType);

            // Set interest as array
            if (data?.interest) {
                if (Array.isArray(data.interest)) {
                    // If it's an array of interest objects
                    const interestIds = data.interest.map((item: any) => Number(item.id)).filter((id: number) => !isNaN(id));
                    setValue('interest', interestIds);
                } else if (data.interest.id !== undefined) {
                    // If it's a single interest object
                    setValue('interest', [Number(data.interest.id)]);
                } else if (typeof data.interest === 'number') {
                    // If it's a single interest ID number
                    setValue('interest', [data.interest]);
                }
            }

            // Parse and set date fields from dob
            if (data.dob) {
                try {
                    const dobDate = new Date(data.dob);
                    if (!isNaN(dobDate.getTime())) {
                        setValue('day', dobDate.getDate().toString().padStart(2, '0'));
                        setValue('month', (dobDate.getMonth() + 1).toString().padStart(2, '0'));
                        setValue('year', dobDate.getFullYear().toString());
                    }
                } catch (error) {
                    console.error('Error parsing date:', error);
                }
            }
        }
    }, [data?.username, setValue]);

    const onSubmit = async (values: ModelFormValues) => {

        // All fields that should be sent as strings in the payload
        const stringFields = [
            'gender',
            'seekingFor',
            'appearance',
            'hairColor',
            'eyeColor',
            'bodyType',
            'relationshipStatus',
            'bestFeature',
            'sexualOrientation',
            'personality',
            'ethnicity',
            'country',
            'modelGroup',
            'smokingHabit',
            'drinkingHabit',
            'bodyArt',
            'kids',
        ];

        // Convert date fields to ISO string for backend
        const day = parseInt(values.day);
        const month = parseInt(values.month);
        const year = parseInt(values.year);
        const dob = new Date(year, month - 1, day).toISOString();

        const payload: any = {
            ...values,
            username: values?.username.trim(),
            height: Number(values?.height),
            weight: Number(values?.weight),
            city: values?.city.trim(),
            aboutMe: values?.aboutMe!.trim(),
            dob: dob, // Send as ISO string to backend
            interest: values.interest, // Send array of numbers to backend
        }

        // Remove the separate date fields from payload
        delete payload.day;
        delete payload.month;
        delete payload.year;

        // Ensure all specified fields are sent as strings
        stringFields.forEach((key) => {
            if (payload[key] !== undefined && payload[key] !== null && payload[key] !== "") {
                payload[key] = String(payload[key]);
            }
        });

        // Handle interest field separately - keep as array of numbers
        if (payload.interest && Array.isArray(payload.interest)) {
            payload.interest = payload.interest.map((id: number) => Number(id));
        }


        if (typeof modelId === "string") {
            const response: any = await updateModelMutation({
                ...payload,
                id: data?.id
            })
            if (response?.success) {
                toast.success("Model Profile has been updated!")
            }
        } else {
            const response: any = await addModelMutation(payload)
            if (response?.success) {
                navigate({ to: END_POINTS.MODELS });
            }
        }
    };

    return (
        <Main>
            <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
                    {typeof modelId === "string" ? "Update Model" : "Add Model"}
                </h1>
                <p className="text-muted-foreground">
                    {typeof modelId === "string" ? "Update model profile." : "Add a new model profile."}
                </p>
            </div>
            <Separator className="my-4 lg:my-6" />
            <div className="flex flex-1 flex-col space-y-2 overflow-hidden md:space-y-2 lg:flex-row lg:space-y-0 lg:space-x-12">
                <div className="flex w-full overflow-y-hidden p-1">
                    <Form {...form}>
                        <form
                            onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                    e.preventDefault();
                                }
                            }}
                            onSubmit={handleSubmit(onSubmit)}
                            className="flex lg:flex-row flex-col gap-4 w-full"
                        >

                            <div className="flex flex-col gap-4 flex-1">

                                {/* Basic Information */}
                                <Card  >
                                    <CardHeader>
                                        <CardTitle>Basic Information</CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-2 gap-4">

                                        <FormField name="username" control={control} render={({ field }) => (
                                            <FormItem className="sm:col-span-1 col-span-2">
                                                <FormLabel className="form-label">Username</FormLabel>
                                                <FormControl><Input {...field} /></FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />

                                        <FormField name="gender" control={control} render={({ field }) => (
                                            <FormItem className="sm:col-span-1 col-span-2">
                                                <FormLabel className="form-label">Gender</FormLabel>
                                                <FormControl>
                                                    <Select onValueChange={field.onChange} value={field.value}>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select Gender">
                                                                {field.value
                                                                    ? masterOptions.gender.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                    : "Select gender"}
                                                            </SelectValue>
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {masterOptions.gender.map((opt: MasterOption) => (
                                                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />
                                        <FormField name="seekingFor" control={control} render={({ field }) => (
                                            <FormItem className="sm:col-span-1 col-span-2">
                                                <FormLabel className="form-label">Seeking For</FormLabel>
                                                <FormControl >
                                                    <Select onValueChange={field.onChange} value={field.value}>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select Seeking For">
                                                                {field.value ? masterOptions.seekingFor.find((opt: any) => String(opt.id) === String(field.value))?.title : "Select seeking for"}
                                                            </SelectValue>
                                                        </SelectTrigger>
                                                        <SelectContent >
                                                            {masterOptions.seekingFor.map((opt: MasterOption) => (
                                                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />
                                        <FormItem className="col-span-2">
                                            <FormLabel className="form-label">Date of Birth</FormLabel>
                                            <FormControl>
                                                <div className="flex gap-2">
                                                    {/* Day */}
                                                    <FormField
                                                        name="day"
                                                        control={control}
                                                        render={({ field: dayField }) => {
                                                            const watchMonth = form.watch('month');
                                                            const watchYear = form.watch('year');
                                                            const month = parseInt(watchMonth) || 1;
                                                            const year = parseInt(watchYear) || new Date().getFullYear();
                                                            const daysInMonth = getDaysInMonth(month, year);

                                                            return (
                                                                <div className="flex-1">
                                                                    <select
                                                                        className={cn(
                                                                            "border rounded px-2 py-1 w-full",
                                                                            form.formState.errors.day && "border-red-500"
                                                                        )}
                                                                        value={dayField.value}
                                                                        onChange={dayField.onChange}
                                                                    >
                                                                        <option value="">DD</option>
                                                                        {Array.from({ length: daysInMonth }, (_, i) => i + 1).map((d) => (
                                                                            <option key={d} value={d.toString().padStart(2, "0")}>
                                                                                {d.toString().padStart(2, "0")}
                                                                            </option>
                                                                        ))}
                                                                    </select>
                                                                    {form.formState.errors.day && (
                                                                        <p className="text-sm text-red-500 mt-1">{form.formState.errors.day.message}</p>
                                                                    )}
                                                                </div>
                                                            );
                                                        }}
                                                    />

                                                    {/* Month */}
                                                    <FormField
                                                        name="month"
                                                        control={control}
                                                        render={({ field: monthField }) => (
                                                            <div className="flex-1">
                                                                <select
                                                                    className={cn(
                                                                        "border rounded px-2 py-1 w-full",
                                                                        form.formState.errors.month && "border-red-500"
                                                                    )}
                                                                    value={monthField.value}
                                                                    onChange={(e) => {
                                                                        monthField.onChange(e);
                                                                        // Reset day if it's invalid for the new month
                                                                        const currentDay = form.getValues('day');
                                                                        const currentYear = form.getValues('year');
                                                                        if (currentDay && currentYear) {
                                                                            const newMonth = parseInt(e.target.value);
                                                                            const year = parseInt(currentYear);
                                                                            const daysInNewMonth = getDaysInMonth(newMonth, year);
                                                                            const day = parseInt(currentDay);
                                                                            if (day > daysInNewMonth) {
                                                                                form.setValue('day', '');
                                                                            }
                                                                        }
                                                                    }}
                                                                >
                                                                    <option value="">MM</option>
                                                                    {Array.from({ length: 12 }, (_, i) => i + 1).map((m) => (
                                                                        <option key={m} value={m.toString().padStart(2, "0")}>
                                                                            {m.toString().padStart(2, "0")}
                                                                        </option>
                                                                    ))}
                                                                </select>
                                                                {form.formState.errors.month && (
                                                                    <p className="text-sm text-red-500 mt-1">{form.formState.errors.month.message}</p>
                                                                )}
                                                            </div>
                                                        )}
                                                    />

                                                    {/* Year */}
                                                    <FormField
                                                        name="year"
                                                        control={control}
                                                        render={({ field: yearField }) => (
                                                            <div className="flex-1">
                                                                <select
                                                                    className={cn(
                                                                        "border rounded px-2 py-1 w-full",
                                                                        form.formState.errors.year && "border-red-500"
                                                                    )}
                                                                    value={yearField.value}
                                                                    onChange={(e) => {
                                                                        yearField.onChange(e);
                                                                        // Reset day if it's invalid for the new year (leap year handling)
                                                                        const currentDay = form.getValues('day');
                                                                        const currentMonth = form.getValues('month');
                                                                        if (currentDay && currentMonth) {
                                                                            const newYear = parseInt(e.target.value);
                                                                            const month = parseInt(currentMonth);
                                                                            const daysInNewYear = getDaysInMonth(month, newYear);
                                                                            const day = parseInt(currentDay);
                                                                            if (day > daysInNewYear) {
                                                                                form.setValue('day', '');
                                                                            }
                                                                        }
                                                                    }}
                                                                >
                                                                    <option value="">YYYY</option>
                                                                    {Array.from({ length: 100 }, (_, i) => new Date().getFullYear() - i).map((y) => (
                                                                        <option key={y} value={y}>
                                                                            {y}
                                                                        </option>
                                                                    ))}
                                                                </select>
                                                                {form.formState.errors.year && (
                                                                    <p className="text-sm text-red-500 mt-1">{form.formState.errors.year.message}</p>
                                                                )}
                                                            </div>
                                                        )}
                                                    />
                                                </div>
                                            </FormControl>
                                        </FormItem>

                                        {/* <FormField name="dob" control={control} render={({ field }) => {
                                            const [open, setOpen] = React.useState(false);
                                            const selectedDate = field.value ? new Date(field.value) : undefined;
                                            const displayValue = selectedDate ? format(selectedDate, "yyyy-MM-dd") : "";
                                            return (
                                                <FormItem className="sm:col-span-1 col-span-2">
                                                    <FormLabel className="form-label">DOB</FormLabel>
                                                    <FormControl className="w-full">
                                                        <Popover open={open} onOpenChange={setOpen}>
                                                            <PopoverTrigger asChild>
                                                                <div className="relative w-full">
                                                                    <Input
                                                                        readOnly
                                                                        value={displayValue}
                                                                        placeholder="YYYY-MM-DD"
                                                                        className="pr-10 cursor-pointer"
                                                                        onClick={() => setOpen(true)}
                                                                    />
                                                                    <CalendarIcon
                                                                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"
                                                                        width={20}
                                                                        height={20}
                                                                    />
                                                                </div>
                                                            </PopoverTrigger>
                                                            <PopoverContent className="w-auto p-0" align="start">

                                                                <Calendar
                                                                    mode="single"
                                                                    selected={selectedDate}
                                                                    className="rounded-md border shadow-sm"
                                                                    captionLayout="dropdown"
                                                                    onSelect={(date) => {
                                                                        if (date) {
                                                                            field.onChange(date.toISOString());
                                                                            setOpen(false);
                                                                        }
                                                                    }}
                                                                />

                                                            </PopoverContent>
                                                        </Popover>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            );
                                        }} /> */}
                                    </CardContent>
                                </Card>


                                {/* Other Information */}
                                <Card >
                                    <CardHeader>
                                        <CardTitle>Other Informations</CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-2 gap-4">
                                        <FormField name="domainType" control={control} render={({ field }) => (
                                            <FormItem className="sm:col-span-1 col-span-2">
                                                <FormLabel className="form-label">Domain Type</FormLabel>
                                                <FormControl>
                                                    <Select onValueChange={field.onChange} value={field.value}>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select Domain Type" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="fashion">Fashion</SelectItem>
                                                            <SelectItem value="fitness">Fitness</SelectItem>
                                                            <SelectItem value="adult">Adult</SelectItem>
                                                            <SelectItem value="beauty">Beauty</SelectItem>
                                                            <SelectItem value="travel">Travel</SelectItem>
                                                            <SelectItem value="other">Other</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />
                                        <FormField name="modelGroup" control={control} render={({ field }) => (
                                            <FormItem className="sm:col-span-1 col-span-2">
                                                <FormLabel className="form-label">Model Group</FormLabel>
                                                <FormControl>
                                                    <Select onValueChange={field.onChange} value={field.value}>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select Model Group">
                                                                {field.value ? masterOptions.modelGroup.find((opt: any) => String(opt.id) === String(field.value))?.title : "Select Model Group"}
                                                            </SelectValue>
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {masterOptions.modelGroup.map((opt: MasterOption) => (
                                                                <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />
                                        <FormField name="country" control={control} render={({ field }) => (
                                            <FormItem className="sm:col-span-1 col-span-2">
                                                <FormLabel className="form-label">Country</FormLabel>
                                                <FormControl>
                                                    <Select onValueChange={field.onChange} value={field.value}>
                                                        <SelectTrigger className="w-full">
                                                            <SelectValue placeholder="Select Country">
                                                                {field.value
                                                                    ? countryList?.country?.find((c: any) => String(c.id) === String(field.value))?.name
                                                                    : "Select country"}
                                                            </SelectValue>
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {countryList?.country?.map((c: any) => (
                                                                <SelectItem key={c.id} value={String(c.id)}>{c.name}</SelectItem>
                                                            ))}
                                                        </SelectContent>
                                                    </Select>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />
                                        <FormField
                                            name="city"
                                            control={control}
                                            render={({ field, fieldState }) => (
                                                <FormItem className="sm:col-span-1 col-span-2">
                                                    <FormLabel className="form-label">City</FormLabel>
                                                    <FormControl>
                                                        <CityAutocomplete
                                                            onPlaceSelect={(place: any) => {
                                                                setValue('city', place?.name)
                                                            }}
                                                            countryCode={selectedCountry?.code}
                                                            disabled={!selectedCountryId}
                                                            placeholder="Select a city"
                                                            isInvalid={!!fieldState.error}
                                                            value={field.value}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                    </CardContent>
                                </Card>
                            </div>


                            <div className="flex-1">

                                {/* Personal Attributes */}
                                <Card  >
                                    <CardHeader>
                                        <CardTitle>Personal Attributes</CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="contents">
                                            <FormField name="personality" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Personality</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Personality" >
                                                                    {field.value
                                                                        ? masterOptions.personality.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Personality"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.personality.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="relationshipStatus" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Relationship Status</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Relationship Status">
                                                                    {field.value
                                                                        ? masterOptions.relationshipStatus.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Relationship Status"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.relationshipStatus.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="ethnicity" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Ethnicity</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Ethnicity" >
                                                                    {field.value
                                                                        ? masterOptions.ethnicity.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Ethnicity"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.ethnicity.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />

                                            <FormField name="hairColor" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Hair Color</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Hair Color">
                                                                    {field.value
                                                                        ? masterOptions.hairColor.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Hair Color"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.hairColor.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="appearance" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Appearance</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Appearance">
                                                                    {field.value
                                                                        ? masterOptions.appearance.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Appearance"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.appearance.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="eyeColor" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Eye Color</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Eye Color">
                                                                    {field.value
                                                                        ? masterOptions.eyeColor.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Eye Color"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.eyeColor.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="bodyType" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Body Type</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Body Type" >
                                                                    {field.value
                                                                        ? masterOptions.bodyType.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Body Type"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.bodyType.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />

                                            <FormField name="smokingHabit" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Smoking Habit</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Smoking Habit">
                                                                    {field.value
                                                                        ? masterOptions.smokingHabits.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Smoking Habit"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.smokingHabits.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="drinkingHabit" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Drinking Habit</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Drinking Habit">
                                                                    {field.value
                                                                        ? masterOptions.drinkingHabits.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Drinking Habit"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.drinkingHabits.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="bestFeature" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Best Feature</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Best Feature">
                                                                    {field.value
                                                                        ? masterOptions.bestFeature.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Best Feature"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.bestFeature.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="bodyArt" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Body Art</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Body Art">
                                                                    {field.value
                                                                        ? masterOptions.bodyArt.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Body Art"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.bodyArt.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="sexualOrientation" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Sexual Orientation</FormLabel>
                                                    <FormControl>
                                                        <Select onValueChange={field.onChange} value={field.value}>
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Select Sexual Orientation" >
                                                                    {field.value
                                                                        ? masterOptions.sexualOrientation.find((opt: any) => String(opt.id) === String(field.value))?.title
                                                                        : "Select Sexual Orientation"}
                                                                </SelectValue>
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {masterOptions.sexualOrientation.map((opt: MasterOption) => (
                                                                    <SelectItem key={opt.id} value={String(opt.id)}>{opt.title}</SelectItem>
                                                                ))}
                                                            </SelectContent>
                                                        </Select>
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="height" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Height (CM)</FormLabel>
                                                    <FormControl>
                                                        <Input type="number" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="weight" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Weight (KG)</FormLabel>
                                                    <FormControl>
                                                        <Input type="number" {...field} />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField name="kids" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">Kids</FormLabel>
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            min="0"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                            <FormField
                                                control={control}
                                                name="interest"
                                                render={({ field }) => {
                                                    const selectedValues: number[] = field.value || []

                                                    const handleRemove = (value: number) => {
                                                        const updated = selectedValues.filter((v) => v !== value)
                                                        field.onChange(updated)
                                                    }

                                                    const toggleValue = (value: number) => {
                                                        const updated = selectedValues.includes(value)
                                                            ? selectedValues.filter((v) => v !== value)
                                                            : [...selectedValues, value]
                                                        field.onChange(updated)
                                                    }

                                                    return (
                                                        <FormItem className="w-full">
                                                            <FormLabel className="form-label">Interests</FormLabel>
                                                            <Popover>
                                                                <PopoverTrigger asChild>
                                                                    <FormControl>
                                                                        <div
                                                                            className={cn(
                                                                                "flex min-h-[40px] w-full flex-wrap items-center gap-1 rounded-md border border-input px-3 py-2 text-sm shadow-sm",
                                                                                !selectedValues.length && "text-muted-foreground"
                                                                            )}
                                                                        >
                                                                            {selectedValues.length === 0 && <span>Select interests</span>}
                                                                            {selectedValues.map((val) => {
                                                                                const label = masterOptions.interest.find((opt: MasterOption) => Number(opt.id) === val)?.title ?? val
                                                                                return (
                                                                                    <div key={val} className="flex gap-1 items-center bg-muted rounded p-1">
                                                                                        <Badge
                                                                                            className="cursor-default border-0 p-0 bg-transparent text-foreground"
                                                                                            onClick={(e) => e.stopPropagation()}
                                                                                        >
                                                                                            {label}
                                                                                        </Badge>
                                                                                        <X
                                                                                            className="h-3 w-3 cursor-pointer"
                                                                                            onClick={(e) => {
                                                                                                e.stopPropagation()
                                                                                                handleRemove(val)
                                                                                            }}
                                                                                        />
                                                                                    </div>
                                                                                )
                                                                            })}
                                                                        </div>
                                                                    </FormControl>
                                                                </PopoverTrigger>
                                                                <PopoverContent className="w-full p-0">
                                                                    <Command>
                                                                        <CommandInput placeholder="Search interests..." />
                                                                        <CommandEmpty>No interest found.</CommandEmpty>
                                                                        <CommandGroup>
                                                                            {masterOptions.interest.map((interest: MasterOption) => (
                                                                                <CommandItem
                                                                                    key={interest.id}
                                                                                    onSelect={() => toggleValue(Number(interest.id))}
                                                                                    className="cursor-pointer"
                                                                                >
                                                                                    <span>{interest.title}</span>
                                                                                    {selectedValues.includes(Number(interest.id)) && (
                                                                                        <span className="ml-auto text-primary">✓</span>
                                                                                    )}
                                                                                </CommandItem>
                                                                            ))}
                                                                        </CommandGroup>
                                                                    </Command>
                                                                </PopoverContent>
                                                            </Popover>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )
                                                }}
                                            />
                                            <FormField name="aboutMe" control={control} render={({ field }) => (
                                                <FormItem className="w-full">
                                                    <FormLabel className="form-label">About Me</FormLabel>
                                                    <FormControl><Textarea {...field} rows={3} className="w-full" /></FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                        </div>
                                    </CardContent>
                                </Card>
                                <div className="md:col-span-2 flex mt-4 justify-end gap-4">
                                    <Button onClick={() => {
                                        navigate({ to: END_POINTS.MODELS })
                                    }} variant={"secondary"} style={{ cursor: 'pointer' }}>
                                        Cancel
                                    </Button>
                                    <Button type="submit">
                                        {typeof modelId === "string" ? "Update" : "Save"}
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </Form>
                </div>
            </div>
        </Main>
    );
} 