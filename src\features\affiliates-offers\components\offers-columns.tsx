import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { DataTableColumnHeader } from '@/features/affiliates/components/data-table-column-header'

export interface AffiliateOfferRow {
    serialNumber?: number
    title: string
    niche: string
    domain: string
    deviceTypes: string
    status: 'Active' | 'Archived'
}

export const columns: ColumnDef<AffiliateOfferRow>[] = [
    {
        accessorKey: 'serialNumber',
        header: ({ column }) => (<DataTableColumnHeader column={column} title='#' />),
        cell: ({ row }) => (<div className='w-8'>{row.getValue('serialNumber')}</div>),
        meta: { className: cn('bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted', 'w-8') },
        enableHiding: false,
    },
    {
        accessorKey: 'title',
        header: ({ column }) => (<DataTableColumnHeader column={column} title='Title' />),
        cell: ({ row }) => (<div className="text-blue-600 cursor-pointer hover:underline">{row.getValue('title')}</div>),
    },
    { accessorKey: 'niche', header: ({ column }) => (<DataTableColumnHeader column={column} title='Niche' />) },
    { accessorKey: 'domain', header: ({ column }) => (<DataTableColumnHeader column={column} title='Domain' />) },
    { accessorKey: 'deviceTypes', header: ({ column }) => (<DataTableColumnHeader column={column} title='Device Type' />) },
    {
        accessorKey: 'status',
        header: ({ column }) => (<DataTableColumnHeader column={column} title='Status' />),
        cell: ({ row }) => {
            const status = row.getValue('status') as string
            return (
                <Badge variant="secondary" className={cn(status === 'Active' && 'bg-green-100 text-green-800', status === 'Archived' && 'bg-gray-200 text-gray-800')}>{status}</Badge>
            )
        }
    },
]


