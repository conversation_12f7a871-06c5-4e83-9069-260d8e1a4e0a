import { LineChart, Line, ResponsiveContainer } from 'recharts'
import { ArrowUpRight } from 'lucide-react'

interface StatCardProps {
    title: string
    value: string | number
    percentage: string
    isPositive?: boolean
    data: { value: number }[]
}

export function StatCard({ title, value, percentage, isPositive = true, data }: StatCardProps) {
    return (
        <div className="rounded-xl border bg-card text-card-foreground p-4 shadow-sm w-full">
            <div className="flex justify-between items-start">
                <div>
                    <div className="text-2xl font-semibold text-card-foreground">{value}</div>
                    <div className="text-xs text-muted-foreground">{title}</div>
                </div>
                <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="text-xs mt-1 text-muted-foreground">
                <span className={isPositive ? 'text-green-500' : 'text-red-500'}>
                    ({isPositive ? '+' : '-'}
                    {percentage})
                </span>
            </div>
            <div className="mt-3 h-16">
                <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={data}>
                        <Line type="monotone" dataKey="value" stroke="currentColor" strokeWidth={1.5} dot={false} className="text-card-foreground" />
                    </LineChart>
                </ResponsiveContainer>
            </div>
        </div>
    )
}
