import { Main } from "@/components/layout/main";
import { BodyArtsPrimaryButtons } from "./components/body-arts-primary-buttons";
import { BodyArtsTable } from "./components/body-arts-table";
import { columns } from "./components/body-arts-columns";

export default function BodyArtsList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Body Arts List</h2>
                </div>
                <BodyArtsPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <BodyArtsTable columns={columns} />
            </div>
        </Main>
    )
}
