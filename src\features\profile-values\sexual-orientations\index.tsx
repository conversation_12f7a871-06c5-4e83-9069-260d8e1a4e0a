import { Main } from "@/components/layout/main";
import { SexualOrientationsPrimaryButtons } from "./components/sexual-orientations-primary-buttons";
import { SexualOrientationsTable } from "./components/sexual-orientations-table";
import { columns } from "./components/sexual-orientations-columns";

export default function SexualOrientationsList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Sexual Orientations List</h2>
                </div>
                <SexualOrientationsPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <SexualOrientationsTable columns={columns} />
            </div>
        </Main>
    )
}
