import { z } from "zod";

export const basicInformationSchema = z.object({
    chatType: z.enum(["GROUP", "PRIVATE"], {
        required_error: "Chat Type is required"
    }),

    minChatLetters: z.preprocess((val) => Number(val),
        z.number({ invalid_type_error: "Minimum chat letters must be a number" })
            .nonnegative("Minimum chat letters must be 0 or more")
    ),

    chatIdleTime: z.preprocess((val) => Number(val),
        z.number({ invalid_type_error: "Chat Idle Time must be a number" })
            .nonnegative("Chat Idle Time must be 0 or more")
    ),

    moderatorLoginIdleTime: z.preprocess((val) => Number(val),
        z.number({ invalid_type_error: "Moderator Login Idle Time must be a number" })
            .nonnegative("Moderator Login Idle Time must be 0 or more")
    ),

    platformBotMessageOn: z.boolean(),

    botMessageInterval: z.preprocess((val) => Number(val),
        z.number({ invalid_type_error: "Bot Message Interval must be a number" })
            .nonnegative("Bot Message Interval must be 0 or more")
    ),

    botMessagesForNewRegistered: z.string().min(1, "Messages for new registered users is required"),

    platformBotViewOn: z.boolean(),

    botViewInterval: z.preprocess((val) => Number(val),
        z.number({ invalid_type_error: "Bot View Interval must be a number" })
            .nonnegative("Bot View Interval must be 0 or more")
    ),

    botViewsForNewRegistered: z.preprocess((val) => Number(val),
        z.number({ invalid_type_error: "Views for new registered users must be a number" })
            .nonnegative("Views for new registered users must be 0 or more")
    ),

    messageSendToHold: z.string().min(1, "Message Send to Hold is required"),

    companyName: z.string().min(1, "Company Name is required"),
    companyEmail: z.string().email("Invalid Email Address"),
    companyVatNo: z.preprocess((val) => Number(val),
        z.number({ invalid_type_error: "Company VAT No. must be a number" })
            .nonnegative("Company VAT No. must be 0 or more")
    ),
    companyAddress: z.string().min(1, "Company Address is required"),

    generalCommissionAmount: z.preprocess((val) => Number(val),
        z.number({ invalid_type_error: "General Commission Amount must be a number" })
            .nonnegative("General Commission Amount must be 0 or more")
    ),

    reLobbyCommissionAmount: z.preprocess((val) => Number(val),
        z.number({ invalid_type_error: "Re Lobby Commission Amount must be a number" })
            .nonnegative("Re Lobby Commission Amount must be 0 or more")
    ),
});

export type BasicInfoValues = z.infer<typeof basicInformationSchema>;