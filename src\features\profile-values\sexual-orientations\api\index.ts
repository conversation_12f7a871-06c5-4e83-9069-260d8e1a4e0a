import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getSexualOrientationsApi = (params: any = {}) => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.SEXUAL_ORIENTATIONS, { params })).data ?? {},
    queryKey: ["sexual-orientations-list"],
});

export const addSexualOrientationsApi = () => useMutation({
    mutationFn: async (data: any) => (await apiClient.post(API_ENDPOINTS.SEXUAL_ORIENTATIONS, data)).data,
});

export const updateSexualOrientationsApi = () => useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => (await apiClient.put(`${API_ENDPOINTS.SEXUAL_ORIENTATIONS}/${id}`, data)).data,
});

export const deleteSexualOrientationsApi = () => useMutation({
    mutationFn: async (id: string) => (await apiClient.delete(`${API_ENDPOINTS.SEXUAL_ORIENTATIONS}/${id}`)).data,
});

export const getSexualOrientationsDetails = (id: string) => useQuery({
    queryFn: async () => (await apiClient.get(`${API_ENDPOINTS.SEXUAL_ORIENTATIONS}/${id}`)).data ?? {},
    queryKey: ["sexual-orientations-details", id],
    enabled: !!id,
});

export const getMasterLanguagesApi = () => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES)).data ?? {},
    queryKey: ["master-languages"],
});
