import { PasswordInput } from '@/components/password-input'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { END_POINTS } from '@/features/members/utils/constant'
import { getUserRole } from '@/features/members/utils/utilities'
import { cn } from '@/lib/utils'
import { useAuthStore } from '@/stores/authStore'
import { zodResolver } from '@hookform/resolvers/zod'
import { Link, useNavigate, useSearch } from '@tanstack/react-router'
import { HTMLAttributes, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { getSignin } from '../api'
import useChatStore from '@/stores/useChatStore'

type UserAuthFormProps = HTMLAttributes<HTMLFormElement>

const formSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Please enter your email' })
    .email({ message: 'Invalid email address' }),
  password: z
    .string()
    .min(1, {
      message: 'Please enter your password',
    })
    .min(7, {
      message: 'Password must be at least 7 characters long',
    }),
  rememberMe: z.boolean().optional(),
})
const rememberedEmail = localStorage.getItem('rememberedEmail')
const rememberMe = localStorage.getItem('rememberMe')

export function UserAuthForm({ className, ...props }: UserAuthFormProps) {
  const navigate = useNavigate()
  const search = useSearch({ strict: false }) as { redirect?: string }

  const { mutateAsync: userSigninMutation } = getSignin()
  const [isLoading, setIsLoading] = useState(false)
  const { auth: { setUser, setAccessToken } } = useAuthStore((state) => state)
  const { initializeSocket, addRoomJoined } = useChatStore()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  })

  // Load remembered email on component mount
  useEffect(() => {
    localStorage.setItem('activeSidebarItem', `"/"`)
    if (rememberedEmail && rememberMe === 'true') {
      form.setValue('email', rememberedEmail)
      form.setValue('rememberMe', true)
    } else {
      form.setValue('email', "")
      form.setValue('password', "")

    }
  }, [form, rememberedEmail, rememberMe])

  // Check if email is pre-filled from remember me
  const isEmailPrefilled = form.watch('email') && localStorage.getItem('rememberedEmail') === form.watch('email')

  async function onSubmit(params: z.infer<typeof formSchema>) {
    setIsLoading(true)
    let response: any;
    try {
      const { rememberMe, ...data } = params
      response = await userSigninMutation(data)
      if (response?.success) {
        setAccessToken(response?.data?.access_token)
        const user_roles = getUserRole(response?.data?.user?.userRoles)
        setUser(response?.data?.user, user_roles)

        // Handle remember me functionality
        if (rememberMe) {
          // Store credentials in localStorage for persistent login
          localStorage.setItem('rememberedEmail', data.email)
          localStorage.setItem('rememberMe', 'true')
        } else {
          // Clear any previously stored credentials
          localStorage.removeItem('rememberedEmail')
          localStorage.removeItem('rememberMe')
        }
        initializeSocket()
        // addRoomJoined()

        // navigate({ to: search.redirect ?? END_POINTS.DASHBOARD })
        navigate({ to: END_POINTS.DASHBOARD })
      }
    } finally {
      setTimeout(() => {
        setIsLoading(false)
      }, 500)
    }

  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn('grid gap-3', className)}
        {...props}
      >
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder='<EMAIL>' {...field} />
              </FormControl>
              <FormMessage />
              {isEmailPrefilled && (
                <p className="text-xs text-muted-foreground">
                  Email loaded from previous session
                </p>
              )}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='password'
          render={({ field }) => (
            <FormItem className='relative'>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <PasswordInput placeholder='********' {...field} />
              </FormControl>
              <FormMessage />
              <Link
                to='/forgot-password'
                className='text-muted-foreground absolute -top-0.5 right-0 text-sm font-medium hover:opacity-75'
              >
                Forgot password?
              </Link>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='rememberMe'
          render={({ field }) => (
            <FormItem className='flex flex-row items-start space-x-3 space-y-0'>
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className='space-y-1 leading-none'>
                <FormLabel className='text-sm font-normal'>
                  Remember me
                </FormLabel>
              </div>
            </FormItem>
          )}
        />
        <Button className='mt-2' disabled={isLoading}>
          Login
        </Button>

        {/* <div className='relative my-2'>
          <div className='absolute inset-0 flex items-center'>
            <span className='w-full border-t' />
          </div>
          <div className='relative flex justify-center text-xs uppercase'>
            <span className='bg-background text-muted-foreground px-2'>
              Or continue with
            </span>
          </div>
        </div>

        <div className='grid grid-cols-2 gap-2'>
          <Button variant='outline' type='button' disabled={isLoading}>
            <IconBrandGithub className='h-4 w-4' /> GitHub
          </Button>
          <Button variant='outline' type='button' disabled={isLoading}>
            <IconBrandFacebook className='h-4 w-4' /> Facebook
          </Button>
        </div> */}
      </form>
    </Form>
  )
}