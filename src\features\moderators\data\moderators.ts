import { faker } from '@faker-js/faker'

// Add a specific moderator that matches the profile design
const ericModerator = {
    id: 'eric-wl-mod-123',
    serialNumber: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    moderatorType: 'WL Moderator',
    phoneNumber: '(44) 123 024 123',
    nickname: 'eric_wl_mod',
    createdAt: new Date('2023-01-23'),
}

export const moderators = [
    ericModerator,
    ...Array.from({ length: 19 }, (_, index) => {
        const firstName = faker.person.firstName()
        const lastName = faker.person.lastName()
        return {
            id: faker.string.uuid(),
            serialNumber: index + 2,
            name: `${firstName} ${lastName}`,
            moderatorType: faker.helpers.arrayElement(['Default Moderator', 'WL Moderator']),
            // email: faker.internet.email({ firstName }).toLocaleLowerCase(),
            phoneNumber: faker.phone.number({ style: 'international' }),
            nickname: faker.internet
                .username({ firstName, lastName })
                .toLocaleLowerCase(),
            // status: faker.helpers.arrayElement([
            //     'active',
            //     'inactive',
            //     'invited',
            //     'suspended',
            // ]),
            // role: faker.helpers.arrayElement([
            //     'superadmin',
            //     'admin',
            //     'cashier',
            //     'manager',
            // ]),
            createdAt: faker.date.past(),
        }
    })
]
