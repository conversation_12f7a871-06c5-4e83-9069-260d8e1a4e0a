import { Main } from "@/components/layout/main";
import { AppearancesPrimaryButtons } from "./components/appearances-primary-buttons";
import { columns } from "./components/appearances-columns";
import { AppearancesTable } from "./components/appearances-table";

export default function AppearancesList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Appearances List</h2>
                </div>
                <AppearancesPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <AppearancesTable columns={columns} />
            </div>
        </Main>
    )
}
