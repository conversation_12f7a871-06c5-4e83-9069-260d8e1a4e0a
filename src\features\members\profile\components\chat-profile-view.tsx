import { IconUser, IconUsersGroup } from "@tabler/icons-react";
import { ChatLayout } from "@/components/chat";
import type { UserPanelProps, ChatPanelProps, UserProfile, Note, PersonalInfo, Message, ChatStats } from "@/components/chat";

export default function ChatProfileView() {
    // Sample data - replace with actual data from your API/state
    const leftUser: UserProfile = {
        id: "1",
        name: "Fit-n_Sexy_milf",
        age: 49,
        avatar: "/avatar1.png",
        fallback: "FS",
        location: "Portarlington",
        vtl: 0,
        timezone: "GMT+5:30",
        relationStatus: "Married",
        country: "United Kingdom",
        interests: "Sex toys, Lingerie, Soft sex, Oral/69, Long foreplay, Exhibitionism, Hardcore, Wild and crazy, Try everything",
        isAffiliate: true
    };

    const rightUser: UserProfile = {
        id: "2",
        name: "<PERSON>",
        avatar: "/avatar2.png",
        fallback: "DH",
        lastActive: "11 Sep 2024, 1:45 PM",
        timezone: "GMT+5:30",
        relationStatus: "Married",
        country: "United Kingdom",
        interests: "Sex toys, Lingerie, Soft sex, Oral/69, Long foreplay, Exhibitionism, Hardcore, Wild and crazy, Try everything",
        isAffiliate: false
    };

    const notes: Note[] = [
        {
            id: "1",
            content: "He is back after 3 days.",
            editedDate: "Dec 2, 2024"
        }
    ];

    const personalInfo: PersonalInfo[] = [
        {
            id: "1",
            icon: IconUser,
            title: "Name",
            items: ["John Doe"]
        },
        {
            id: "2",
            icon: IconUser,
            title: "Living Conditions",
            items: ["Lives in Fulham", "Splits bills with partner"]
        },
        {
            id: "3",
            icon: IconUsersGroup,
            title: "Family / Pets",
            items: ["In a complicated long-term relationship", "2 Kids boy is 6 and girl is 3 1/2 years old"]
        }
    ];

    const messages: Message[] = [
        {
            id: "1",
            content: "next time you'll be awake at this hour why not now",
            timestamp: "2024-12-02T05:10:00Z",
            isReceived: true
        },
        {
            id: "2",
            content: "Didn't I tell you not to put your phone on charge just because it's the weekend?",
            timestamp: "2024-12-02T05:10:00Z",
            isReceived: true
        },
        {
            id: "3",
            content: "🥰🥰😘😘❤️❤️",
            timestamp: "2024-12-02T05:10:00Z",
            isReceived: true
        },
        {
            id: "4",
            content: "next time you'll be awake at this hour why not now next time you'll be awake at this hour why not nownext time you'll be awake at this hour why not now",
            timestamp: "2024-12-02T05:15:00Z",
            isReceived: false
        },
        {
            id: "5",
            content: "🥰🥰😘😘❤️❤️",
            timestamp: "2024-12-02T05:15:00Z",
            isReceived: false
        },
        {
            id: "6",
            content: "next time you'll be awake at this hour why not now",
            timestamp: "2024-12-02T05:10:00Z",
            isReceived: true
        }
    ];

    const chatStats: ChatStats = {
        total: 0,
        down: 8,
        up: 9
    };

    // Event handlers
    const handleAddNote = (note: string) => {
        console.log("Adding note:", note);
        // Implement your note adding logic here
    };

    const handleAffiliateChange = (isAffiliate: boolean) => {
        console.log("Affiliate changed:", isAffiliate);
        // Implement your affiliate change logic here
    };

    const handleSendMessage = (message: string) => {
        console.log("Sending message:", message);
        // Implement your message sending logic here
    };

    const handleReplyStay = () => {
        console.log("Reply & Stay clicked");
        // Implement your reply & stay logic here
    };

    const handleReply = () => {
        console.log("Reply clicked");
        // Implement your reply logic here
    };

    const handleLobby = () => {
        console.log("Lobby clicked");
        // Implement your lobby logic here
    };

    const handleConfirmAction = () => {
        console.log("Confirm action");
        // Implement your confirm action logic here
    };

    const handleHoldAction = () => {
        console.log("Hold action");
        // Implement your hold action logic here
    };

    const handleProblemAction = () => {
        console.log("Problem action");
        // Implement your problem action logic here
    };

    // Prepare props for components
    const leftUserProps: UserPanelProps = {
        user: leftUser,
        notes: notes,
        personalInfo: personalInfo,
        onAddNote: handleAddNote,
        onAffiliateChange: handleAffiliateChange
    };

    const rightUserProps: UserPanelProps = {
        user: rightUser,
        notes: notes,
        personalInfo: personalInfo,
        onAddNote: handleAddNote,
        onAffiliateChange: handleAffiliateChange
    };

    const chatProps: ChatPanelProps = {
        messages: messages,
        stats: chatStats,
        onReplyStay: handleReplyStay,
        onReply: handleReply,
        onLobby: handleLobby,
        onSendMessage: handleSendMessage
    };

    return (
        <ChatLayout
            leftUser={leftUserProps}
            rightUser={rightUserProps}
            chat={chatProps}
            onConfirmAction={handleConfirmAction}
            onHoldAction={handleHoldAction}
            onProblemAction={handleProblemAction}
        />
    );
}
