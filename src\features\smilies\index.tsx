import { Main } from "@/components/layout/main";
import { SmilyTable } from "./components/smiley-table";
import { columns } from "./components/smiley-columns";
import { SmilyPrimaryButtons } from "./components/smiley-primary-button";

export default function List() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">
                        Smilies
                    </h2>
                </div>
                <SmilyPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <SmilyTable columns={columns} />
            </div>
        </Main>
    );
} 