import { useState } from "react";
import { ModAssignModal } from "./mod-assign-modal";
import { Button } from "@/components/ui/button";

export function DomainPrimaryButtons() {
  const [open, setOpen] = useState(false);

  return (
    <div className="flex gap-2">
      <Button
        className="space-x-1"
        onClick={() => setOpen(true)}
      >
        <span>+ Assign WL MOD</span>
      </Button>
      <ModAssignModal open={open} onClose={() => setOpen(false)} />
    </div>
  );
}