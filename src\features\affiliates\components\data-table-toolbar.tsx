import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useState } from 'react'

interface DataTableToolbarProps<TData> {
    readonly table: Table<TData>
    readonly onFilterChanged?: any
}

const statusOptions = ['Active', 'Archived']

const offerOptions = [
    { label: 'All Offers', value: '' },
    { label: 'Offer 1', value: 'offer1' },
    { label: 'Offer 2', value: 'offer2' },
    { label: 'Offer 3', value: 'offer3' },
    { label: 'Premium Offer', value: 'premium' },
    { label: 'Basic Offer', value: 'basic' },
]

export function DataTableToolbar<TData>({
    table,
    onFilterChanged
}: DataTableToolbarProps<TData>) {
    const isFiltered = table.getState().columnFilters.length > 0
    const [filters, setFilters] = useState<{
        search: string;
        status: string;
        offers: string;
    }>({
        search: '',
        status: '',
        offers: '',
    })

    const [hasSearched, setHasSearched] = useState(false)
    const [initialFilters, setInitialFilters] = useState<{
        search: string;
        status: string;
        offers: string;
    }>({
        search: '',
        status: '',
        offers: '',
    })

    const handleFilterChange = (
        key: 'search' | 'status' | 'offers',
        value: string
    ) => {
        setFilters((prev) => ({ ...prev, [key]: value }));
    }

    const handleSearch = () => {
        if (filters.search) {
            const searchColumn = table.getColumn('email')
            if (searchColumn) {
                searchColumn.setFilterValue(filters.search)
            }
        }
        if (filters.status) {
            const statusColumn = table.getColumn('status')
            if (statusColumn) {
                statusColumn.setFilterValue(filters.status)
            }
        }
        if (filters.offers) {
            const selected = filters.offers.split(',').filter(Boolean);
            const offersColumn = table.getColumn('offers')
            if (offersColumn) {
                offersColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string[]) => {
                    return filterValue.includes(row.original.offers)
                }
                offersColumn.setFilterValue(selected)
            }
        }
        setHasSearched(true)
        setInitialFilters({ ...filters })
        onFilterChanged(filters, 1)
    }

    const handleReset = () => {
        table.resetColumnFilters()
        const f: any = {
            search: '',
            status: '',
            offers: '',
        }
        setFilters(f)
        setInitialFilters(f)
        setHasSearched(false)
        onFilterChanged(f, 0)
    }

    const hasFilterChanges = Boolean(
        filters.search !== initialFilters.search ||
        filters.status !== initialFilters.status ||
        filters.offers !== initialFilters.offers
    )

    return (
        <div className='flex items-center justify-between'>
            <div className='flex flex-1 items-center gap-4 flex-wrap'>
                <Input
                    placeholder='Search by email'
                    value={filters.search}
                    onChange={(event) => handleFilterChange('search', event.target.value)}
                    className='h-9 w-[250px]'
                />

                <FilterSelect
                    value={filters.status || undefined}
                    placeholder="Select status"
                    options={statusOptions}
                    onChange={(value) => handleFilterChange('status', value || '')}
                    className="w-[200px] bg-card"
                />

                {/* Offers multi-select popover */}
                <Popover>
                    <PopoverTrigger asChild>
                        <div className={cn(
                            "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer",
                            !filters.offers && "text-muted-foreground"
                        )}>
                            {filters.offers === '' && <span>Select offers</span>}
                            {filters.offers && filters.offers.split(',').filter(Boolean).slice(0, 2).map((val: string) => {
                                const offer = offerOptions.find(o => o.value === val);
                                return (
                                    <div className="flex gap-1 items-center bg-muted rounded p-1" key={val}>
                                        <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                                            {offer?.label ?? val}
                                        </Badge>
                                        <X className="h-3 w-3 cursor-pointer" onClick={e => {
                                            e.stopPropagation();
                                            handleFilterChange('offers', filters.offers.split(',').filter(Boolean).filter((v: string) => v !== val).join(','))
                                        }} />
                                    </div>
                                )
                            })}
                            {filters.offers && filters.offers.split(',').filter(Boolean).length > 2 && (
                                <span className="ml-2 text-muted-foreground">...</span>
                            )}
                        </div>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                        <Command>
                            <CommandInput placeholder="Search offers..." />
                            <CommandEmpty>No offers found.</CommandEmpty>
                            <CommandGroup>
                                {(() => {
                                    const sortedOffers = [
                                        ...offerOptions.filter((offer) => filters.offers.split(',').includes(offer.value)),
                                        ...offerOptions.filter((offer) => !filters.offers.split(',').includes(offer.value)),
                                    ];
                                    return sortedOffers.map((offer) => (
                                        <CommandItem
                                            key={offer.value}
                                            onSelect={() => {
                                                const currentValues = filters.offers.split(',').filter(Boolean);
                                                const selected = currentValues.includes(offer.value)
                                                    ? currentValues.filter((v: string) => v !== offer.value)
                                                    : [...currentValues, offer.value];
                                                handleFilterChange('offers', selected.join(','))
                                            }}
                                            className="cursor-pointer"
                                        >
                                            <span>{offer.label}</span>
                                            {filters.offers.split(',').includes(offer.value) && (
                                                <span className="ml-auto text-primary">✓</span>
                                            )}
                                        </CommandItem>
                                    ));
                                })()}
                            </CommandGroup>
                        </Command>
                    </PopoverContent>
                </Popover>

                <Button
                    onClick={handleSearch}
                    className="h-8 px-3"
                    disabled={!hasFilterChanges}
                >
                    Search
                </Button>
                {(isFiltered || hasSearched) && (
                    <Button
                        variant='outline'
                        onClick={handleReset}
                        className='h-8 px-2 lg:px-3'
                    >
                        Reset
                    </Button>
                )}
            </div>
        </div>
    )
}
