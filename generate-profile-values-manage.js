const fs = require('fs');
const path = require('path');

const profileValueTypes = [
  'ethnicities',
  'religions',
  'hair-colors',
  'appearances',
  'eye-colors',
  'body-types',
  'star-signs',
  'smoking-habits',
  'drinking-habits',
  'best-features',
  'body-arts',
  'interests',
  'sexual-orientations',
];

const apiEndpointMappings = {
  'ethnicities': 'ethnicity',
  'religions': 'religion',
  'hair-colors': 'hair-color',
  'appearances': 'appearance',
  'eye-colors': 'eye-color',
  'body-types': 'body-type',
  'star-signs': 'star-sign',
  'smoking-habits': 'smoking-habit',
  'drinking-habits': 'drinking-habit',
  'best-features': 'best-feature',
  'body-arts': 'body-art',
  'interests': 'interest',
  'sexual-orientations': 'sexual-orientation',
};

function ensureDir(dir) {
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
}

function pascalCase(type) {
  return type.split('-').map(s => s.charAt(0).toUpperCase() + s.slice(1)).join('');
}

function writeApi(type) {
  const Class = pascalCase(type);
  const epConst = type.toUpperCase().replace(/-/g, '_');
  const apiDir = path.join(__dirname, 'src', 'features', 'profile-values', type, 'api');
  ensureDir(apiDir);
  fs.writeFileSync(path.join(apiDir, 'api-endpoints.ts'), `export const API_ENDPOINTS = {
  ${epConst}: "/${apiEndpointMappings[type]}",
  MASTER_LANGUAGES: "/master/languages",
};`);

  const apiIndexContent = `import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const get${Class}Api = (params: any = {}) => useQuery({
  queryFn: async () => (await apiClient.get(API_ENDPOINTS.${epConst}, { params })).data ?? {},
  queryKey: ["${type}-list"],
});

export const add${Class}Api = () => useMutation({
  mutationFn: async (data: any) => (await apiClient.post(API_ENDPOINTS.${epConst}, data)).data,
});

export const update${Class}Api = () => useMutation({
  mutationFn: async ({ id, data }: { id: string; data: any }) => (await apiClient.put(API_ENDPOINTS.${epConst} + '/' + id, data)).data,
});

export const delete${Class}Api = () => useMutation({
  mutationFn: async (id: string) => (await apiClient.delete(API_ENDPOINTS.${epConst} + '/' + id)).data,
});

export const get${Class}Details = (id: string) => useQuery({
  queryFn: async () => (await apiClient.get(API_ENDPOINTS.${epConst} + '/' + id)).data ?? {},
  queryKey: ["${type}-details", id],
  enabled: !!id,
});

export const getMasterLanguagesApi = () => useQuery({
  queryFn: async () => (await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES)).data ?? {},
  queryKey: ["master-languages"],
});
`;
  fs.writeFileSync(path.join(apiDir, 'index.ts'), apiIndexContent);
}

function writeTypes(type) {
  const Class = pascalCase(type);
  const dataDir = path.join(__dirname, 'src', 'features', 'profile-values', type, 'data');
  ensureDir(dataDir);
  fs.writeFileSync(path.join(dataDir, 'types.ts'), `export interface ${Class}Item {
  id: string;
  languageCode: string;
  message: string;
}

export interface ${Class} {
  id: string;
  items: ${Class}Item[];
  createdAt?: string;
}

export interface ${Class}FormData {
  [key: string]: string;
}
`);
}

function writeManageComponent(type) {
  const Class = pascalCase(type);
  const dir = path.join(__dirname, 'src', 'features', 'profile-values', type, `manage-${type.replace(/-/g, '')}`);
  ensureDir(dir);
  const display = type.replace(/-/g, ' ');
  const content = `import { Main } from "@/components/layout/main";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { add${Class}Api, update${Class}Api, get${Class}Details, getMasterLanguagesApi } from "../api";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { END_POINTS } from "@/features/members/utils/constant";

export default function Manage${Class}() {
  const navigate = useNavigate()
  const { ${type.replace(/-/g, '')}Id } = useParams({ strict: false })
  const { data: { languages = [] } = {} } = getMasterLanguagesApi()
  const { mutateAsync: addMutation } = add${Class}Api();
  const { mutateAsync: updateMutation } = update${Class}Api();
  const { data = {} } = get${Class}Details((${type.replace(/-/g, '')}Id as string) || "")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const Schema = useMemo(() => {
    const shape: Record<string, z.ZodString> = {}
    if (languages && languages.length > 0) {
      languages.forEach((lang: any) => { const code = lang.code || lang.languageCode || lang.id; if (code) shape[code] = z.string().min(1, "Name is required") })
    } else {
      ['en','fr','de','nl','da','fi','it','no','pl','pt','el','es','sv'].forEach(code => { shape[code] = z.string().min(1, "Name is required") })
    }
    return z.object(shape)
  }, [languages])
  type FormValues = z.infer<typeof Schema>

  const defaultValues = useMemo(() => {
    const d: Record<string, string> = {}
    const items = (data as any)?.items || []
    if (languages && languages.length > 0) {
      languages.forEach((lang: any) => { const code = lang.code || lang.languageCode || lang.id; if (code) d[code] = (items.find((it: any) => it.languageCode === code)?.message) || "" })
    } else {
      ['en','fr','de','nl','da','fi','it','no','pl','pt','el','es','sv'].forEach(code => { d[code] = (items.find((it: any) => it.languageCode === code)?.message) || "" })
    }
    return d
  }, [languages, data])

  const form = useForm<FormValues>({ resolver: zodResolver(Schema), defaultValues: defaultValues as FormValues })
  const { control, handleSubmit, reset } = form
  useEffect(() => { reset(defaultValues as FormValues) }, [defaultValues, reset])

  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true)
      const items = Object.entries(values).map(([languageCode, message]) => ({ languageCode, message }))
      const payload = { items }
      if (${type.replace(/-/g, '')}Id) { await updateMutation({ id: ${type.replace(/-/g, '')}Id as string, data: payload }); toast.success("${Class} updated successfully!") }
      else { await addMutation(payload); toast.success("${Class} added successfully!") }
      navigate({ to: END_POINTS.${type.toUpperCase().replace(/-/g, '_')} })
    } catch (e: any) { toast.error(e?.response?.data?.message || "Action failed") } finally { setIsSubmitting(false) }
  }

  const isEditing = typeof ${type.replace(/-/g, '')}Id === "string" && (${type.replace(/-/g, '')}Id as string).length > 0

  return (
    <Main>
      <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{isEditing ? "Update ${Class}" : "Add ${Class}"}</h2>
        </div>
      </div>
      <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
        <Card>
          <CardHeader>
            <CardTitle>${Class} Information</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  {(languages && languages.length > 0 ? languages.map((lang: any) => {
                    const code = lang.code || lang.languageCode || lang.id; const name = lang.name || (code ? String(code).toUpperCase() : '')
                    return (
                      <FormField key={code} control={control} name={code as any} render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium">{name}</FormLabel>
                          <FormControl><Input placeholder={"Enter ${display} name in " + name} {...field} /></FormControl>
                          <FormMessage />
                        </FormItem>
                      )} />
                    )
                  }) : ['en','fr','de','nl','da','fi','it','no','pl','pt','el','es','sv'].map(code => (
                    <FormField key={code} control={control} name={code as any} render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium">{String(code).toUpperCase()}</FormLabel>
                        <FormControl><Input placeholder={"Enter ${display} name in " + String(code).toUpperCase()} {...field} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  )))}
                </div>
                <Separator />
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => navigate({ to: END_POINTS.${type.toUpperCase().replace(/-/g, '_')} })}>Cancel</Button>
                  <Button type="submit" disabled={isSubmitting}>{isEditing ? "Update ${Class}" : "Add ${Class}"}</Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </Main>
  )
}
`;
  fs.writeFileSync(path.join(dir, 'index.tsx'), content);
}

function writeRoutes(type) {
  const routesDir = path.join(__dirname, 'src', 'routes', '_authenticated', 'profile-values', type);
  ensureDir(routesDir);
  const Class = pascalCase(type);
  const ep = type.toUpperCase().replace(/-/g, '_');
  // list route using concatenation to avoid nested template issues
  fs.writeFileSync(path.join(routesDir, 'index.tsx'), "import { createFileRoute } from '@tanstack/react-router'\n" +
    "import " + Class + "List from '@/features/profile-values/" + type + "'\n" +
    "import { END_POINTS } from '@/features/members/utils/constant'\n\n" +
    "export const Route = createFileRoute<any>('/_authenticated' + END_POINTS." + ep + " + '/')({\n  component: " + Class + "List,\n})\n");

  fs.writeFileSync(path.join(routesDir, 'add.tsx'), "import { createFileRoute } from '@tanstack/react-router'\n" +
    "import Manage" + Class + " from '@/features/profile-values/" + type + "/manage-" + type.replace(/-/g, '') + "'\n" +
    "import { END_POINTS } from '@/features/members/utils/constant'\n\n" +
    "export const Route = createFileRoute<any>('/_authenticated' + END_POINTS." + ep + " + '/add')({\n  component: Manage" + Class + ",\n})\n");

  const updateDir = path.join(routesDir, 'update');
  ensureDir(updateDir);
  const param = type.replace(/-/g, '') + 'Id';
  fs.writeFileSync(path.join(updateDir, `$${param}.tsx`), "import { createFileRoute } from '@tanstack/react-router'\n" +
    "import Manage" + Class + " from '@/features/profile-values/" + type + "/manage-" + type.replace(/-/g, '') + "'\n" +
    "import { END_POINTS } from '@/features/members/utils/constant'\n\n" +
    "export const Route = createFileRoute<any>('/_authenticated' + END_POINTS." + ep + " + '/update/$" + param + "')({\n  component: Manage" + Class + ",\n})\n");
}

function writeListAndHelpers(type) {
  const Class = pascalCase(type);
  const pluralName = Class + 's';
  const featDir = path.join(__dirname, 'src', 'features', 'profile-values', type);
  ensureDir(featDir);
  // index
  fs.writeFileSync(path.join(featDir, 'index.tsx'), `import { Main } from "@/components/layout/main";
import { ${pluralName}PrimaryButtons } from "./components/${type}-primary-buttons";
import { ${pluralName}Table } from "./components/${type}-table";
import { columns } from "./components/${type}-columns";

export default function ${pluralName}List() {
  return (
    <Main>
      <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">${pluralName} List</h2>
        </div>
        <${pluralName}PrimaryButtons />
      </div>
      <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
        <${pluralName}Table columns={columns} />
      </div>
    </Main>
  );
}
`);
  // primary button
  const compDir = path.join(featDir, 'components');
  ensureDir(compDir);
  fs.writeFileSync(path.join(compDir, `${type}-primary-buttons.tsx`), `import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";

export function ${pluralName}PrimaryButtons() {
  const navigate = useNavigate();
  return (
    <div className="flex items-center space-x-2">
      <Button onClick={() => navigate({ to: END_POINTS.${type.toUpperCase().replace(/-/g, '_')} + "/add" })} className="flex items-center space-x-2">
        <Plus className="h-4 w-4" />
        <span>Add ${Class}</span>
      </Button>
    </div>
  );
}
`);
  // columns (simple)
  fs.writeFileSync(path.join(compDir, `${type}-columns.tsx`), `import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";

export interface RowType { id: string; items: { languageCode: string; message: string }[]; createdAt?: string }

const ActionCell = ({ row }: { row: any }) => {
  const navigate = useNavigate();
  return (
    <div className="flex items-center space-x-2">
      <Button variant="outline" size="sm" onClick={() => navigate({ to: END_POINTS.${type.toUpperCase().replace(/-/g, '_')} + '/update/' + row.original.id })}><Edit className="h-4 w-4" /></Button>
      <Button variant="outline" size="sm"><Trash2 className="h-4 w-4" /></Button>
    </div>
  );
};

export const columns: ColumnDef<RowType>[] = [
  { accessorKey: "serialNumber", header: "S.No" },
  { accessorKey: "items", header: "Name", cell: ({ row }) => (row.original.items?.[0]?.message || '-') },
  { accessorKey: "items", header: "Languages", cell: ({ row }) => <div className="flex flex-wrap gap-1">{(row.original.items||[]).map((i: any, idx: number) => <span key={idx} className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">{i.languageCode}</span>)}</div> },
  { accessorKey: "createdAt", header: "Created At", cell: ({ row }) => row.original.createdAt ? new Date(row.original.createdAt).toLocaleDateString() : '-' },
  { id: "actions", header: "Actions", cell: ActionCell },
];
`);
  // toolbar
  fs.writeFileSync(path.join(compDir, 'data-table-toolbar.tsx'), `import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { X } from 'lucide-react'
import { useState } from 'react'

interface DataTableToolbarProps<TData> { table: Table<TData>; onFilterChanged?: any }

export function DataTableToolbar<TData>({ table, onFilterChanged }: DataTableToolbarProps<TData>) {
  const [searchValue, setSearchValue] = useState('')
  const handle = (v: string) => { setSearchValue(v); onFilterChanged?.({ search: v }, 1) }
  const clear = () => { setSearchValue(''); onFilterChanged?.({ search: '' }, 0) }
  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <Input placeholder="Filter by name..." value={searchValue} onChange={(e) => handle(e.target.value)} className="h-8 w-[150px] lg:w-[250px]" />
        {searchValue && (<Button variant="ghost" onClick={clear} className="h-8 px-2 lg:px-3">Reset <X className="ml-2 h-4 w-4" /></Button>)}
      </div>
    </div>
  )
}
`);
  // table (simplified server-backed list)
  fs.writeFileSync(path.join(compDir, `${type}-table.tsx`), `import { useEffect, useState } from 'react'
import { useReactTable, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, flexRender } from '@tanstack/react-table'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { DataTableToolbar } from './data-table-toolbar'
import { PaginationControls } from '@/components/ui/PaginationControls'
import { get${pascalCase(type)}Api } from '../api'

export function ${pascalCase(type)}sTable({ columns }: any) {
  const [filters, setFilters] = useState<any>({ search: '' })
  const [pageIndex, setPageIndex] = useState(0)
  const [pageSize, setPageSize] = useState(10)
  const { data = {}, refetch }: any = get${pascalCase(type)}Api({ page: pageIndex + 1, limit: pageSize, ...filters })
  const dataWithSerialNumbers = (data?.${type.replace(/-/g, '_')} || []).map((item: any, idx: number) => ({ ...item, serialNumber: idx + 1 + (pageIndex * pageSize) }))

  const table = useReactTable({ data: dataWithSerialNumbers, columns, getCoreRowModel: getCoreRowModel(), getPaginationRowModel: getPaginationRowModel(), getSortedRowModel: getSortedRowModel(), getFilteredRowModel: getFilteredRowModel() })
  useEffect(() => { refetch() }, [pageIndex, pageSize, filters.search])
  return (
    <div className="space-y-4">
      <DataTableToolbar table={table} onFilterChanged={setFilters} />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((hg) => (
              <TableRow key={hg.id}>
                {hg.headers.map((h) => (<TableHead key={h.id}>{h.isPlaceholder ? null : flexRender(h.column.columnDef.header, h.getContext())}</TableHead>))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map((cell) => (<TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>))}
                </TableRow>
              ))
            ) : (
              <TableRow><TableCell colSpan={columns.length} className="h-24 text-center">No results.</TableCell></TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <PaginationControls table={table} />
    </div>
  )
}
`);
}

function run() {
  console.log('Generating manage components for profile values...')
  profileValueTypes.forEach((t) => {
    console.log(' -', t)
    writeApi(t)
    writeTypes(t)
    writeListAndHelpers(t)
    writeManageComponent(t)
    writeRoutes(t)
  })
  console.log('Done.')
}

run()
