import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getDrinkingHabitsApi = (params: any = {}) => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.DRINKING_HABITS, { params })).data ?? {},
    queryKey: ["drinking-habits-list"],
});

export const addDrinkingHabitsApi = () => useMutation({
    mutationFn: async (data: any) => (await apiClient.post(API_ENDPOINTS.DRINKING_HABITS, data)).data,
});

export const updateDrinkingHabitsApi = () => useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => (await apiClient.put(`${API_ENDPOINTS.DRINKING_HABITS}/${id}`, data)).data,
});

export const deleteDrinkingHabitsApi = () => useMutation({
    mutationFn: async (id: string) => (await apiClient.delete(`${API_ENDPOINTS.DRINKING_HABITS}/${id}`)).data,
});

export const getDrinkingHabitsDetails = (id: string) => useQuery({
    queryFn: async () => (await apiClient.get(`${API_ENDPOINTS.DRINKING_HABITS}/${id}`)).data ?? {},
    queryKey: ["drinking-habits-details", id],
    enabled: !!id,
});

export const getMasterLanguagesApi = () => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES)).data ?? {},
    queryKey: ["master-languages"],
});
