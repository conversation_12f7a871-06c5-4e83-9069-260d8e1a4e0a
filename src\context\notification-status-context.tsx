import React, { createContext, useContext, useState, useCallback } from 'react';

export interface StatusItem {
  id: string;
  label: string;
  count: number;
  variant: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning' | 'info';
  className?: string;
}

interface NotificationStatusContextType {
  statusItems: StatusItem[];
  updateStatusCount: (id: string, count: number) => void;
  updateMultipleStatus: (updates: { id: string; count: number }[]) => void;
  resetAllCounts: () => void;
}

const NotificationStatusContext = createContext<NotificationStatusContextType | undefined>(undefined);

// Default status items configuration with theme-aware styling
const defaultStatusItems: StatusItem[] = [
  {
    id: 'websocket',
    label: 'Web Socket',
    count: 0,
    variant: 'info',
    className: 'status-items shadow-sm'
  },
  {
    id: 'trigger-waiting',
    label: 'Trigger Waiting',
    count: 0,
    variant: 'warning',
    className: 'status-items shadow-sm'
  },
  {
    id: 'lobby',
    label: 'Lobby',
    count: 0,
    variant: 'info',
    className: 'status-items shadow-sm'
  },
  {
    id: 'active',
    label: 'Active',
    count: 2,
    variant: 'success',
    className: 'status-items shadow-sm'
  },
  {
    id: 'hold',
    label: 'Hold',
    count: 0,
    variant: 'warning',
    className: 'status-items shadow-sm'
  },
  {
    id: 'problem',
    label: 'Problem',
    count: 0,
    variant: 'destructive',
    className: 'status-items shadow-sm'
  }
];

interface NotificationStatusProviderProps {
  children: React.ReactNode;
  initialStatusItems?: StatusItem[];
}

export function NotificationStatusProvider({
  children,
  initialStatusItems = defaultStatusItems
}: NotificationStatusProviderProps) {
  const [statusItems, setStatusItems] = useState<StatusItem[]>(initialStatusItems);

  const updateStatusCount = useCallback((id: string, count: number) => {
    setStatusItems(prev =>
      prev.map(item =>
        item.id === id ? { ...item, count } : item
      )
    );
  }, []);

  const updateMultipleStatus = useCallback((updates: { id: string; count: number }[]) => {
    setStatusItems(prev =>
      prev.map(item => {
        const update = updates.find(u => u.id === item.id);
        return update ? { ...item, count: update.count } : item;
      })
    );
  }, []);

  const resetAllCounts = useCallback(() => {
    setStatusItems(prev =>
      prev.map(item => ({ ...item, count: 0 }))
    );
  }, []);

  const value = {
    statusItems,
    updateStatusCount,
    updateMultipleStatus,
    resetAllCounts
  };

  return (
    <NotificationStatusContext.Provider value={value}>
      {children}
    </NotificationStatusContext.Provider>
  );
}

export function useNotificationStatus() {
  const context = useContext(NotificationStatusContext);
  if (context === undefined) {
    throw new Error('useNotificationStatus must be used within a NotificationStatusProvider');
  }
  return context;
}

// Dropdown component for showing status items
export function StatusDropdown() {
  const { statusItems } = useNotificationStatus();
  const [open, setOpen] = useState(false);

  return (
    <div>
      {/* Trigger button with hamburger (3 horizontal bars) */}
      <button
        onClick={() => setOpen((prev: any) => !prev)}
        className="p-2 rounded-md hover:bg-gray-200"
      >
        {/* SVG for hamburger menu */}
        <svg
          className="w-6 h-6"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>

      {/* Dropdown */}
      {open && (
        <div className="absolute right-0 mt-2 w-56 bg-white border rounded-md shadow-lg z-50">
          {statusItems.map(item => (
            <div
              key={item.id}
              className="flex justify-between items-center px-3 py-2 hover:bg-gray-100 cursor-pointer"
            >
              <span>{item.label}</span>
              <span className="font-semibold">{item.count}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}


// Export default status items for reference
export { defaultStatusItems };
