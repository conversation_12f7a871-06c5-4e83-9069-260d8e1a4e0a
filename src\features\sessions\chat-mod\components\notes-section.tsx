import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { IconHistory, IconTrash } from "@tabler/icons-react";
import { NotesComponentProps, NotesHistoryModalProps } from "../types";

function NotesHistoryModal({
  open,
  onOpenChange,
  notes,
  onDeleteNote,
  isDeleting,
  title = "Notes History",
}: NotesHistoryModalProps) {
  const handleDeleteNote = (noteId: string) => {
    onDeleteNote(noteId);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[400px] w-full">
          <div className="space-y-3 pr-4">
            {notes.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                No notes found
              </div>
            ) : (
              notes.map((note) => (
                <div key={note.id} className="bg-sidebar-accent p-3 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div className="text-sm flex-1">{note.note}</div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteNote(note.id)}
                      disabled={isDeleting}
                      className="h-6 w-6 p-0 ml-2 hover:bg-red-100 hover:text-red-600"
                    >
                      <IconTrash size={14} />
                    </Button>
                  </div>
                  <hr className="my-2" />
                  <div className="flex justify-between items-center text-xs text-muted-foreground">
                    <span>
                      Created: {new Date(note.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}

export function NotesSection({
  data,
  onAddNote,
  onDeleteNote,
  isLoading,
  title,
}: NotesComponentProps) {
  const [currentNote, setCurrentNote] = useState(data.currentNote || "");
  const [showHistory, setShowHistory] = useState(false);

  const handleAddNote = () => {
    if (currentNote.trim()) {
      onAddNote(currentNote.trim());
      setCurrentNote("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleAddNote();
    }
  };

  const handleDeleteNote = (noteId: string) => {
    onDeleteNote(noteId);
  };

  return (
    <>
      <div className="bg-sidebar p-4 rounded-2xl">
        <div className="flex justify-between items-center mb-[12px]">
          <div className="text-base font-semibold">Notes</div>
          {data?.notes?.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHistory(true)}
              className="h-8 px-3 cursor-pointer"
            >
              <IconHistory width="16px" className="mr-1" />
              View History
            </Button>
          )}
        </div>

        <div className="flex mb-4 overflow-x-auto space-x-3 flex-nowrap">
          {data.notes.slice(0, 2).map((note) => (
            <div
              key={note.id}
              className="bg-sidebar-accent p-3 rounded-lg min-w-[200px]"
            >
              <div className="flex justify-between items-start mb-2">
                <div className="text-sm flex-1">{note.note}</div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteNote(note.id)}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 ml-2 hover:bg-red-100 hover:text-red-600 cursor-pointer"
                >
                  <IconTrash size={14} />
                </Button>
              </div>
              <hr className="my-3" />
              <div className="flex gap-4 items-center justify-between">
                <div className="text-xs">
                  Created:{" "}
                  {new Date(note.createdAt).toLocaleDateString("en-US", {
                    month: "short",
                    day: "numeric",
                    year: "numeric",
                  })}
                </div>
                <div></div>
              </div>
            </div>
          ))}
          {data.notes.length === 0 && (
            <div className="p-3 rounded-lg min-w-[200px] text-center text-muted-foreground w-full">
              No notes yet
            </div>
          )}
        </div>

        <div className="flex gap-2">
          <Input
            type="text"
            placeholder="Enter Note"
            className="text-sm h-[42px] shadow-none flex-1"
            value={currentNote}
            onChange={(e) => setCurrentNote(e.target.value)}
            onKeyDown={handleKeyDown}
          />
          <Button
            onClick={handleAddNote}
            disabled={!currentNote.trim() || isLoading}
            size="sm"
            className="h-[42px] px-4"
          >
            {isLoading ? "Adding..." : "Add"}
          </Button>
        </div>
      </div>

      <NotesHistoryModal
        open={showHistory}
        onOpenChange={setShowHistory}
        notes={data.notes}
        onDeleteNote={handleDeleteNote}
        isDeleting={isLoading}
        title={title}
      />
    </>
  );
}
