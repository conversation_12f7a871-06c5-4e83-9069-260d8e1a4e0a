import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/tables'
import { DataTableColumnHeader } from '@/components/data-table-column-header'
import { formatDateToReadable } from '@/features/members/utils/utilities'

export interface CurrenciessData {
    rank: number
    currencyName: string
    code: string
    symbol: string
    isDefault: string
    exchangeRate: any
    lastUpdate: any
    displayOrder: any
    apiSource: any
    status: any
}

// Column definitions for Sales
export const currenciesColumns: ColumnDef<CurrenciessData>[] = [
    {
        accessorKey: 'id',
        header: '#',
        cell: ({ row }) => <div className='w-6 text-xs sm:w-8 sm:text-sm'>{row.getValue('id')}</div>,
        meta: { className: 'w-8 sm:w-12' },
    },
    {
        accessorKey: 'name',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Currency Name' />
        ),
        cell: ({ row }) => <div className='truncate text-xs sm:text-sm'>{row.getValue('name')}</div>,
        meta: { className: 'min-w-0' },
    },
    {
        accessorKey: 'symbol',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Symbol' />
        ),
        cell: ({ row }) => <div className='text-center text-xs sm:text-sm'>{row.getValue('symbol')}</div>,
        meta: { className: 'text-right w-16 sm:w-20' },
    },
    {
        accessorKey: 'isDefault',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='is Default?' />
        ),
        cell: ({ row }) => <div className='text-center text-xs sm:text-sm'>{row.getValue('isDefault') ? "Yes" : "No"}</div>,
        meta: { className: 'text-right w-16 sm:w-20' },
    },
    {
        accessorKey: 'exchangeRateToEuro',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Exchange Rate' />
        ),
        cell: ({ row }) => <div className='text-center text-xs sm:text-sm'>{row.getValue('exchangeRateToEuro')}</div>,
        meta: { className: 'text-right w-16 sm:w-20' },
    },
    {
        accessorKey: 'updatedAt',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Last Update' />
        ),
        cell: ({ row }) => <div className='text-center text-xs sm:text-sm'>{formatDateToReadable(row.getValue('updatedAt'))}</div>,
        meta: { className: 'text-right w-16 sm:w-20' },
    },
    {
        accessorKey: 'displayOrder',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Display Order' />
        ),
        cell: ({ row }) => <div className='text-center text-xs sm:text-sm'>{row.getValue('displayOrder')}</div>,
        meta: { className: 'text-right w-16 sm:w-20' },
    },
    {
        accessorKey: 'apiSource',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Api Source' />
        ),
        cell: ({ row }) => <div className='text-center text-xs sm:text-sm'>{row.getValue('apiSource')}</div>,
        meta: { className: 'text-right w-16 sm:w-20' },
    },
    // {
    //     accessorKey: 'status',
    //     header: ({ column }) => (
    //         <DataTableColumnHeader column={column} title='Status' />
    //     ),
    //     cell: ({ row }) => <div className='text-center text-xs sm:text-sm'>{row.getValue('status')}</div>,
    //     meta: { className: 'text-right w-16 sm:w-20' },
    // },
]

// Reusable Dashboard Table Component
interface DashboardTableProps<T> {
    data: T[]
    columns: ColumnDef<T>[]
    title: string
    searchKey?: string
    showToolbar?: boolean
    showPagination?: boolean
    pageSize?: number
}

export function DashboardTable<T>({
    data,
    columns,
    title,
    searchKey,
    showToolbar = false,
    showPagination = false,
    pageSize = 10,
}: DashboardTableProps<T>) {
    return (
        <DataTable
            columns={columns}
            data={data}
            searchKey={searchKey}
            searchPlaceholder={`Filter ${title.toLowerCase()}...`}
            enableRowSelection={false}
            enableSorting={true}
            enableFiltering={!!searchKey}
            enablePagination={showPagination}
            showToolbar={showToolbar}
            showViewOptions={false}
            showPagination={showPagination}
            pageSize={pageSize}
            className='border-0'
        />
    )
}
