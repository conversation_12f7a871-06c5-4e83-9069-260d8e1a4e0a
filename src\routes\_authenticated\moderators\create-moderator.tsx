import { END_POINTS } from '@/features/members/utils/constant'
import CreateModerator from '@/features/moderators/create-moderator'
import { createFileRoute } from '@tanstack/react-router'
import { roleGuards } from '@/utils/route-protection'

export const Route = createFileRoute<any>(
  `/_authenticated${END_POINTS.CREATE_MODERATOR}`,
)({
  beforeLoad: ({ location }) => {
    roleGuards.adminOnly(location.pathname)
  },
  component: CreateModerator,
})
