import { faker } from '@faker-js/faker'
import { Domain } from './schema'

// Sample domains
const sampleDomains = [
  'discreetdating.club',
  'passionhub.net', 
  'lovequest.org',
  'secretlovers.co',
  'adultmatch.com',
  'flirtzone.com',
  'uforpls.female.deso',
  'example.com',
  'testdomain.net',
  'sample.org'
]

// Sample countries
const countries = [
  'Denmark',
  'Norway', 
  'Sweden',
  'United Kingdom',
  'Ireland',
  'United States',
  'Canada',
  'Australia',
  'Germany',
  'France',
  'Spain',
  'Italy',
  'Japan',
  'India'
]

// Sample moderator names (matching the image data)
const moderatorNames = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>'
]

export const domains: Domain[] = Array.from({ length: 15 }, (_, index) => {
  const nativeCountries = faker.helpers.arrayElements(countries, { min: 2, max: 5 })
  const hybridCountries = faker.helpers.arrayElements(
    countries.filter(c => !nativeCountries.includes(c)), 
    { min: 1, max: 3 }
  )

  return {
    id: faker.string.uuid(),
    serialNumber: index + 1,
    moderator: faker.helpers.arrayElement(moderatorNames),
    domain: faker.helpers.arrayElement(sampleDomains),
    countriesAssignAsNative: nativeCountries,
    countriesAssignAsHybrid: hybridCountries,
    status: faker.helpers.arrayElement(['active', 'inactive'] as const),
    createdAt: faker.date.past(),
    updatedAt: faker.date.recent(),
  }
})
