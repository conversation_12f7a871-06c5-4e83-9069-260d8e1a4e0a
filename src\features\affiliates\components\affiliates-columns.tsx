import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import LongText from '@/components/long-text'
import { Affiliate } from '../types'
import { DataTableColumnHeader } from './data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'

export const columns: ColumnDef<Affiliate>[] = [
    {
        accessorKey: 'serialNumber',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='#' />
        ),
        cell: ({ row }) => (
            <div className='w-8'>{row.getValue('serialNumber')}</div>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
                'w-8'
            ),
        },
        enableHiding: false,
    },
    {
        accessorKey: 'email',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Email' />
        ),
        cell: ({ row }) => (
            <div className="text-blue-600 cursor-pointer hover:underline">
                {row.getValue('email')}
            </div>
        ),
        meta: {
            className: cn(
                'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
                'sticky left-0 md:table-cell'
            ),
        },
        enableHiding: false,
    },
    {
        accessorKey: 'username',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Username' />
        ),
        cell: ({ row }) => (
            <LongText className='max-w-36'>{row.getValue('username')}</LongText>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
            ),
        },
    },
    {
        accessorKey: 'name',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Name' />
        ),
        cell: ({ row }) => (
            <LongText className='max-w-36'>{row.getValue('name')}</LongText>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
            ),
        },
    },
    {
        accessorKey: 'location',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Country / City' />
        ),
        cell: ({ row }) => (
            <div>{`${row.original.country} / ${row.original.city}`}</div>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
            ),
        },
    },
    {
        accessorKey: 'isWhitelisted',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Is WL?' />
        ),
        cell: ({ row }) => (
            <div>{row.getValue('isWhitelisted') ? "Yes" : "No"}</div>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
            ),
        },
    },
    {
        accessorKey: 'status',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Status' />
        ),
        cell: ({ row }) => {
            const status = row.getValue('status') as string
            return (
                <Badge
                    variant="secondary"
                    className={cn(
                        status === 'Active' && "bg-green-100 text-green-800",
                        status === 'Archived' && "bg-gray-200 text-gray-800"
                    )}
                >
                    {status}
                </Badge>
            )
        },
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
            ),
        },
    },
    {
        accessorKey: 'lastLogin',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Last Login' />
        ),
        cell: ({ row }) => (
            <div>{row.getValue('lastLogin')}</div>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
            ),
        },
    },
    {
        accessorKey: 'lastLoginIP',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Last Login IP' />
        ),
        cell: ({ row }) => (
            <div>{row.getValue('lastLoginIP')}</div>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
            ),
        },
    },
    {
        accessorKey: 'sites',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Sites' />
        ),
        cell: ({ row }) => {
            const sites = row.getValue('sites') as string
            if (sites === "(not set)") {
                return <div className="text-gray-500">{sites}</div>
            }
            return (
                <div className="text-green-600 cursor-pointer hover:underline">
                    {sites}
                </div>
            )
        },
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
            ),
        },
    },
    {
        id: 'actions',
        header: '',
        cell: ({ row }) => <DataTableRowActions row={row} />,
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
                'w-8'
            ),
        },
        enableHiding: false,
    },
]
