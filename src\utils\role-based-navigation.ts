/**
 * Utility functions for role-based navigation filtering
 */

export interface NavigationItem {
  title: string;
  url?: string;
  icon?: any;
  badge?: string;
  roles?: string[];
  items?: NavigationItem[];
}

export interface NavigationGroup {
  title: string;
  items: NavigationItem[];
}

export interface SidebarData {
  user: any;
  teams: any[];
  navGroups: NavigationGroup[];
}

/**
 * Check if user has access to a navigation item based on roles
 * @param userRoles - Array of user roles
 * @param itemRoles - Array of required roles for the item
 * @returns boolean - true if user has access, false otherwise
 */
export function hasRoleAccess(userRoles: string[], itemRoles?: string[]): boolean {
  // If no roles are specified for the item, allow access to everyone
  if (!itemRoles || itemRoles.length === 0) {
    return true;
  }
  
  // If user has no roles, deny access to role-protected items
  if (!userRoles || userRoles.length === 0) {
    return false;
  }
  
  // Check if user has at least one of the required roles
  return userRoles.some((role) => itemRoles.includes(role));
}

/**
 * Filter navigation items based on user roles
 * @param items - Array of navigation items to filter
 * @param userRoles - Array of user roles
 * @returns Filtered array of navigation items
 */
export function filterNavigationItemsByRoles(
  items: NavigationItem[], 
  userRoles: string[]
): NavigationItem[] {
  return items
    .map((item) => {
      // Check if user has access to this item
      if (!hasRoleAccess(userRoles, item.roles)) {
        return null;
      }

      // If item has sub-items, filter them recursively
      if (item.items && item.items.length > 0) {
        const filteredSubItems = filterNavigationItemsByRoles(item.items, userRoles);
        
        // If no sub-items remain after filtering, hide the parent item
        if (filteredSubItems.length === 0) {
          return null;
        }
        
        // Return item with filtered sub-items
        return { ...item, items: filteredSubItems };
      }

      // Return the item as-is if it has no sub-items
      return item;
    })
    .filter((item): item is NavigationItem => item !== null);
}

/**
 * Filter entire sidebar data based on user roles
 * @param sidebarData - Complete sidebar data structure
 * @param userRoles - Array of user roles
 * @returns Filtered sidebar data
 */
export function getFilteredSidebarData(
  sidebarData: SidebarData, 
  userRoles: string[]
): SidebarData {
  return {
    ...sidebarData,
    navGroups: sidebarData.navGroups.map((group) => ({
      ...group,
      items: filterNavigationItemsByRoles(group.items, userRoles),
    })),
  };
}

/**
 * Get user roles from auth store in a safe way
 * @param authUser - User object from auth store
 * @returns Array of role strings
 */
export function getUserRoles(authUser: any): string[] {
  if (!authUser) {
    return [];
  }
  
  // Handle different role data structures
  if (Array.isArray(authUser.role)) {
    return authUser.role;
  }
  
  if (Array.isArray(authUser.roles)) {
    return authUser.roles;
  }
  
  if (typeof authUser.role === 'string') {
    return [authUser.role];
  }
  
  return [];
}
