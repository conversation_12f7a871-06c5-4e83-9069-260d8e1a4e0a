import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";


export const getBotMessageApi = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.BOT_MESSAGES, {
                params,
            });
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["bot-message-list"],
    });


export const addBotMessageApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.BOT_MESSAGES, payload);
        },
    });


export const updateBotMessageApi = () =>
    useMutation({
        mutationFn: async ({ id, ...payload }: any) => {
            return await apiClient.put(`${API_ENDPOINTS.BOT_MESSAGES}/${id}`, payload);
        },
    });

export const getBotMessageDetails = (id: any = {}) =>
    useQuery({
        queryFn: async () => {
            if (typeof id === 'string') {
                const response = await apiClient.get(`${API_ENDPOINTS.BOT_MESSAGES}/${id}`)
                return response?.data ?? {}; // return [] or {} as a fallback
            }
            return {}

        },
        queryKey: ["bot-message-details", id],
        enabled: !!id
    });

export const deleteBotMessageApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.delete(`${API_ENDPOINTS.BOT_MESSAGES}/${payload?.id}`);
        },
    });

export const uploadBotMessageCsvApi = () =>
    useMutation({
        mutationFn: async (fileOrFormData: File | FormData) => {
            let formData: FormData;
            if (fileOrFormData instanceof FormData) {
                formData = fileOrFormData;
            } else {
                formData = new FormData();
                formData.append("file", fileOrFormData);
            }
            return await apiClient.post(API_ENDPOINTS.BOT_UPLOAD_CSV, formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            });
        },
    });

export const getMastrLanguagesApi = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES);
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["get-bot-language-list"],
    });