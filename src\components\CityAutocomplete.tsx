import { useEffect, useRef, useState } from "react";
import { loadGoogleMapsAPI } from "@/utils/googleMaps";

interface CityOption {
  value: string;
  label: string;
  place_id: string;
}

interface CityAutocompleteProps {
  onPlaceSelect: (place: any | null) => void;
  countryCode?: string;
  disabled?: boolean;
  placeholder?: string;
  value?: string;
  className?: string;
  isInvalid?: boolean;
}

const CityAutocomplete = ({
  onPlaceSelect,
  countryCode,
  disabled = false,
  placeholder,
  value,
  isInvalid = false,
}: CityAutocompleteProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState(value || "");
  const [options, setOptions] = useState<CityOption[]>([]);
  const [selectedOption, setSelectedOption] = useState<CityOption | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);

  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const autocompleteService = useRef<any | null>(null);
  const placesService = useRef<any | null>(null);

  useEffect(() => {
    const initializeServices = async () => {
      try {
        await loadGoogleMapsAPI();
        if (window.google && window.google.maps && window.google.maps.places) {
          autocompleteService.current = new window.google.maps.places.AutocompleteService();
          const dummyDiv = document.createElement('div');
          placesService.current = new window.google.maps.places.PlacesService(dummyDiv);
        }
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
      }
    };

    initializeServices();
  }, []);

  const searchCities = async (query: string) => {
    if (!autocompleteService.current || !query.trim()) {
      setOptions([]);
      return;
    }

    setIsLoading(true);

    const request: any = {
      input: query,
      types: ['(cities)'],
    };

    if (countryCode) {
      request.componentRestrictions = { country: countryCode.toLowerCase() };
    }

    try {
      autocompleteService.current.getPlacePredictions(request, (predictions: any, status: any) => {
        setIsLoading(false);

        if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {
          const cityOptions: CityOption[] = predictions.map((prediction: any) => ({
            value: prediction.place_id,
            label: prediction.description,
            place_id: prediction.place_id
          }));
          setOptions(cityOptions);
        } else {
          setOptions([]);
        }
      });
    } catch (error) {
      console.error('Error fetching city predictions:', error);
      setIsLoading(false);
      setOptions([]);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm && isOpen) {
        searchCities(searchTerm);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, countryCode, isOpen]);

  const selectOption = async (option: CityOption) => {
    if (!placesService.current) return;

    setSelectedOption(option);
    setSearchTerm(option.label);
    setIsOpen(false);
    setFocusedIndex(-1);

    const request: any = {
      placeId: option.place_id,
      fields: ['geometry', 'name', 'formatted_address', 'place_id']
    };

    placesService.current.getDetails(request, (place: any, status: any) => {
      if (status === (window as any).google.maps.places.PlacesServiceStatus.OK && place) {
        onPlaceSelect(place);
      } else {
        onPlaceSelect(null);
      }
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'ArrowDown' || e.key === 'Enter') {
        setIsOpen(true);
        return;
      }
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev =>
          prev < options.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => prev > 0 ? prev - 1 : prev);
        break;
      case 'Enter':
        e.preventDefault();
        if (focusedIndex >= 0 && options[focusedIndex]) {
          selectOption(options[focusedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setFocusedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    setSelectedOption(null);

    if (newValue.trim()) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
      setOptions([]);
      onPlaceSelect(null);
    }
  };

  const handleFocus = () => {
    if (searchTerm.trim()) {
      setIsOpen(true);
    }
  };

  const handleBlur = (e: React.FocusEvent) => {
    setTimeout(() => {
      if (!containerRef.current?.contains(e.relatedTarget as Node)) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    }, 150);
  };

  const handleOptionClick = (option: CityOption) => {
    selectOption(option);
  };

  useEffect(() => {
    if (value !== undefined && value !== searchTerm) {
      setSearchTerm(value);
    }
  }, [value]);

  return (
    <div ref={containerRef} className="relative w-full">
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          placeholder={placeholder || (countryCode ? "Select a city" : "Select a country first")}
          className={`w-full h-10 rounded-md border px-3 pr-8 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${isInvalid ? 'border-red-500 focus-visible:ring-red-500' : 'border-input'
            }`}
        />
        <div className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground">
          {isLoading ? (
            <svg className="h-4 w-4 animate-spin" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z" />
            </svg>
          ) : (
            <svg width="18" height="18" viewBox="0 0 20 20" fill="currentColor">
              <path d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"></path>
            </svg>
          )}
        </div>
      </div>

      {isOpen && (
        <div className="absolute z-50 mt-1 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none">
          <div className="max-h-64 overflow-y-auto py-1 text-sm">
            {options.length > 0 ? (
              options.map((option, index) => (
                <div
                  key={option.place_id}
                  className={`cursor-pointer px-3 py-2 hover:bg-accent hover:text-accent-foreground ${index === focusedIndex ? 'bg-accent text-accent-foreground' : ''
                    } ${selectedOption?.place_id === option.place_id ? 'font-medium' : ''}`}
                  onClick={() => handleOptionClick(option)}
                  onMouseEnter={() => setFocusedIndex(index)}
                >
                  {option.label}
                </div>
              ))
            ) : (
              <div className="px-3 py-2 text-muted-foreground">No cities found</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CityAutocomplete;
