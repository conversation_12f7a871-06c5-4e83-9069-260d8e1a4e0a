import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTableColumnHeader } from '@/components/data-table-column-header'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { IconTrash } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { Domain } from '../data/schema'
import LongText from '@/components/long-text'

export const columns: ColumnDef<Domain>[] = [
  {
    accessorKey: 'serialNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='#' />
    ),
    cell: ({ row }) => (
      <div className='w-8'>{row.getValue('serialNumber')}</div>
    ),
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-0 md:table-cell w-16'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'moderator',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Moderator' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-36'>{row.getValue('moderator')}</LongText>
    ),
    meta: { className: 'w-40' },
  },
  {
    accessorKey: 'domain',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Domain' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-48'>{row.getValue('domain')}</LongText>
    ),
    meta: { className: 'w-48' },
  },
  {
    accessorKey: 'countriesAssignAsNative',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Countries Assign as Native' />
    ),
    cell: ({ row }) => {
      const countries = row.getValue('countriesAssignAsNative') as string[]
      return (
        <div className='max-w-64'>
          <LongText>{countries.join(', ')}</LongText>
        </div>
      )
    },
    meta: { className: 'w-64' },
  },
  {
    accessorKey: 'countriesAssignAsHybrid',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Countries Assign as Hybrid' />
    ),
    cell: ({ row }) => {
      const countries = row.getValue('countriesAssignAsHybrid') as string[]
      return (
        <div className='max-w-64'>
          <LongText>{countries.join(', ')}</LongText>
        </div>
      )
    },
    meta: { className: 'w-64' },
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string
      return (
        <Badge
          variant={status === 'active' ? 'default' : 'secondary'}
          className={cn(
            'capitalize',
            status === 'active'
              ? 'bg-green-100 text-green-800 hover:bg-green-100 dark:bg-green-900 dark:text-green-100'
              : 'bg-muted text-muted-foreground hover:bg-muted'
          )}
        >
          {status}
        </Badge>
      )
    },
    meta: { className: 'w-24' },
  },
  {
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => (
      <Button
        variant='ghost'
        size='sm'
        className='h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950'
        onClick={() => {
          // Handle delete action
          console.log('Delete domain:', row.original.id)
        }}
      >
        <IconTrash className='h-4 w-4' />
      </Button>
    ),
    meta: { className: 'w-20' },
    enableSorting: false,
  },
]
