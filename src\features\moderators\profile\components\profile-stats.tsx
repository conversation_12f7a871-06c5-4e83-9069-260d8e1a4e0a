import { Separator } from "@radix-ui/react-separator"

export default function ProfileStats({ data }: any) {
  const stats = [
    {
      title: 'Current Month',
      value: '€350.4',
      subtitle: 'Sent Messages: €350',
      subtitle2: 'Rum Messages: €50.4',
      icon: '€'
    },
    {
      title: 'Last Month',
      value: '€350.4',
      subtitle: 'Sent Messages: €350',
      subtitle2: 'Rum Messages: €50.4',
      icon: '$'
    },
    {
      title: 'Year Income',
      value: '€350.4',
      subtitle: 'Sent Messages: €300',
      subtitle2: 'Rum Messages: €50.4',
      icon: '$'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {stats.map((stat) => (
        <div key={stat.title} className="bg-card rounded-2xl border p-4">
          <div className="flex items-start gap-2 mb-3">
            <span className="text-2xl font-bold text-muted-foreground bg-accent rounded-full flex items-center justify-center min-w-[56px] w-[56px] h-[56px]">{stat.icon}</span>
            <div className="flex flex-col w-full">
              <div className="flex flex-col gap-1">
                <span className="text-sm text-muted-foreground">{stat.title}</span>
                <h3 className="text-xl font-bold text-foreground">{stat.value}</h3>
              </div>
              <Separator className="my-3 h-[1px] bg-accent"></Separator>
              <div className="space-y-0.5 text-xs text-muted-foreground">
                <p>{stat.subtitle}</p>
                <p>{stat.subtitle2}</p>
              </div>
            </div>

          </div>

        </div>
      ))}
    </div>
  )
}
