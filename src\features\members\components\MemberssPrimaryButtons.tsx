import { Download } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { members } from "../data/members";
import { exportAllMembers } from "../utils/csv-export";

export function MemberssPrimaryButtons() {
    const handleExportAll = () => {
        exportAllMembers(members);
    };

    return (
        <div className="flex gap-2">
            <Button
                className="space-x-1"
                onClick={handleExportAll}
            >
                <Download className="h-4 w-4" />
                <span>Export All</span>
            </Button>
        </div>
    );
}