import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTableColumnHeader } from '@/components/data-table-column-header'
import { cn } from '@/lib/utils'
import { ChatGroup } from '../data/schema'
import LongText from '@/components/long-text'
import { ChatGroupRowActions } from './chat-group-row-actions'

export const columns: ColumnDef<ChatGroup>[] = [

  {
    accessorKey: 'serialNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='#' />
    ),
    cell: ({ row }) => (
      <div>{row.getValue('serialNumber')}</div>
    ),
    enableHiding: false,
  },
  {
    accessorKey: 'moderator',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Moderator' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-36'>{row.getValue('moderator')}</LongText>
    ),
  },
  {
    accessorKey: 'group',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Group' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-48'>{row.getValue('group')}</LongText>
    ),
  },
  {
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => <ChatGroupRowActions chatGroup={row.original} />,
    enableSorting: false,
  },
]
