'use client'

import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

interface Message {
    id: string
    date: string
    recipient: string
    recipientAvatar: string
    message: string
    status: string
    type: 'sent' | 'received'
}

interface MessageHistoryListProps {
    // messages: Message[]
    messages: any
}

export default function MessageHistoryList({ messages }: MessageHistoryListProps) {
    return (
        <div className="space-y-4">
            {messages.map((message: any) => (
                <div
                    key={message.id}
                    className={`flex gap-3 p-4 border rounded-lg ${message.type === 'sent'
                        ? 'bg-blue-50 dark:bg-blue-950/20'
                        : 'bg-gray-50 dark:bg-gray-950/20'
                        }`}
                >
                    <Avatar className="h-10 w-10">
                        <AvatarImage src={message.recipientAvatar} alt={message.recipient} />
                        <AvatarFallback>
                            {message.recipient
                                .split(' ')
                                .map((n: any) => n[0])
                                .join('')
                                .toUpperCase()}
                        </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                            <span className="font-medium text-sm">
                                {message.type === 'sent' ? `To: ${message.recipient}` : `From: ${message.recipient}`}
                            </span>
                            <Badge
                                variant={message.type === 'sent' ? 'default' : 'secondary'}
                                className="text-xs"
                            >
                                {message.type}
                            </Badge>
                            <span className="text-xs text-muted-foreground">{message.date}</span>
                        </div>
                        <p className="text-sm text-foreground mb-2">{message.message}</p>
                        <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                                {message.status}
                            </Badge>
                        </div>
                    </div>
                </div>
            ))}
        </div>
    )
}
