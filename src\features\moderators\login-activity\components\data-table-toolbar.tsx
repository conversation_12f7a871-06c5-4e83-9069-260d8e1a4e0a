import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { useState } from 'react'
import { CalendarIcon } from 'lucide-react'
import { Calendar } from '@/components/ui/calendar'
import { format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Input } from '@/components/ui/input'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  onFilterChanged?: (filters: any, type: number) => void
}

export function DataTableToolbar<TData>({
  table,
  onFilterChanged
}: DataTableToolbarProps<TData>) {
  const [filters, setFilters] = useState({
    country: undefined as string | undefined,
    fromDate: undefined as Date | undefined,
    toDate: undefined as Date | undefined,
  })

  const [openFrom, setOpenFrom] = useState(false)
  const [openTo, setOpenTo] = useState(false)
  const [hasSearched, setHasSearched] = useState(false)

  // Only enable search if country is selected or both dates are selected
  const hasCompleteDateRange = filters.fromDate && filters.toDate
  const hasActiveFilters = filters.country || hasCompleteDateRange

  const handleFilterChange = (
    key: 'country' | 'fromDate' | 'toDate',
    value: any
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  const handleSearch = () => {
    // Only include dates if both are selected
    const searchFilters = {
      country: filters.country,
      fromDate: hasCompleteDateRange ? filters.fromDate : undefined,
      toDate: hasCompleteDateRange ? filters.toDate : undefined,
    }
    if (onFilterChanged) onFilterChanged(searchFilters, 1)
    setHasSearched(true)
  }

  const handleReset = () => {
    setFilters({
      country: undefined,
      fromDate: undefined,
      toDate: undefined,
    })
    if (onFilterChanged) onFilterChanged({}, 0)
    setHasSearched(false)
  }

  return (
    <div className="flex items-center gap-3 w-full py-2 flex-wrap">
      <Input
        value={filters.country || ''}
        onChange={e => handleFilterChange('country', e.target.value)}
        placeholder="Country"
        className="h-9 min-w-[160px] bg-card"
        style={{ width: "auto" }}
      />
      <div className="flex gap-2">
        <Popover open={openFrom} onOpenChange={setOpenFrom}>
          <PopoverTrigger asChild>
            <div className="relative w-[160px] bg-card">
              <Input
                readOnly
                value={filters.fromDate ? format(filters.fromDate, "yyyy-MM-dd") : ""}
                placeholder="From Date"
                className="pr-10 cursor-pointer h-9"
                onClick={() => setOpenFrom(true)}
              />
              <CalendarIcon
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"
                width={20}
                height={20}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="relative">
              <Calendar
                mode="single"
                selected={filters.fromDate}
                className="rounded-md border shadow-sm"
                captionLayout="dropdown"
                disabled={(date) => filters.toDate ? date > filters.toDate : false}
                onSelect={(date) => {
                  if (date) {
                    handleFilterChange('fromDate', date);
                    setOpenFrom(false);
                  }
                }}
              />
            </div>
          </PopoverContent>
        </Popover>
        <Popover open={openTo} onOpenChange={setOpenTo}>
          <PopoverTrigger asChild>
            <div className="relative w-[160px] bg-card">
              <Input
                readOnly
                value={filters.toDate ? format(filters.toDate, "yyyy-MM-dd") : ""}
                placeholder="To Date"
                className="pr-10 cursor-pointer h-9"
                onClick={() => setOpenTo(true)}
              />
              <CalendarIcon
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"
                width={20}
                height={20}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="relative">
              <Calendar
                mode="single"
                selected={filters.toDate}
                className="rounded-md border shadow-sm"
                captionLayout="dropdown"
                disabled={(date) => filters.fromDate ? date < filters.fromDate : false}
                onSelect={(date) => {
                  if (date) {
                    handleFilterChange('toDate', date);
                    setOpenTo(false);
                  }
                }}
              />
            </div>
          </PopoverContent>
        </Popover>
      </div>
      <Button
        onClick={handleSearch}
        disabled={!hasActiveFilters}
        className="h-9 px-4"
        type="button"
      >
        Search
      </Button>
      {hasSearched && (
        <Button
          variant="outline"
          onClick={handleReset}
          className="h-9 px-3"
        >
          Reset
        </Button>
      )}
    </div>
  )
}
