import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { useEffect, useState } from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { Input } from '@/components/ui/input'
import { CalendarIcon, X } from 'lucide-react'
import { format } from 'date-fns'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { getCountryList } from '@/features/members/api'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  readonly onFilterChanged?: any
}

// role suggestions removed per requirement; free text input only

export function DataTableToolbar<TData>({
  table: _table,
  onFilterChanged
}: DataTableToolbarProps<TData>) {
  const { data: countryList } = getCountryList()
  const [countries, setCountries] = useState<any>(countryList?.country || [])

  useEffect(() => {
    setCountries(countryList?.country || [])
  }, [countryList?.country?.length])

  const [filters, setFilters] = useState({
    role: '', // comma-separated values
    roleInput: '',
    country: '', // comma-separated country IDs
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
  })

  const [startDateOpen, setStartDateOpen] = useState(false)
  const [endDateOpen, setEndDateOpen] = useState(false)

  const [hasSearched, setHasSearched] = useState(false)
  const [roleDropdownOpen, setRoleDropdownOpen] = useState(false)

  const handleFilterChange = (
    key: 'role' | 'roleInput' | 'country' | 'startDate' | 'endDate',
    value: any
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  // Check if both dates are selected (treat as single entity)
  const hasCompleteDateRange = filters.startDate && filters.endDate

  // Check if other filters are active (excluding incomplete date range)
  const hasOtherFilters = filters.role || filters.country

  // Only consider dates if both are selected
  const hasActiveFilters = hasOtherFilters || hasCompleteDateRange

  const handleSearch = () => {
    const searchFilters: any = {};
    if (filters.role) searchFilters.role = filters.role;
    if (filters.country) searchFilters.country = filters.country;
    if (hasCompleteDateRange) {
      searchFilters.startDate = filters.startDate;
      searchFilters.endDate = filters.endDate;
    }
    onFilterChanged(searchFilters, 1);
    setHasSearched(true);
  }

  const handleReset = () => {
    // Reset all filters

    const filterValue: any = {
      role: '',
      roleInput: '',
      country: '',
    }
    setFilters(filterValue)

    onFilterChanged({}, 0)
    setHasSearched(false)
  }

  return (
    <div className="flex items-center gap-3 w-full py-2 flex-wrap">
      {/* Role multi-input using city-style chips */}
      <div className="relative w-[200px]">
        <input
          type="text"
          className="w-full border border-input bg-card text-foreground rounded-md px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground"
          placeholder={filters.role.split(',').filter(Boolean).length === 0 ? 'Add roles (comma or enter)' : 'Add more roles'}
          value={filters.roleInput || ""}
          onFocus={() => setRoleDropdownOpen(true)}
          onBlur={() => setTimeout(() => setRoleDropdownOpen(false), 150)}
          onChange={e => handleFilterChange('roleInput', e.target.value)}
          onKeyDown={e => {
            if (
              (e.key === 'Enter' || e.key === ',') &&
              filters.roleInput &&
              !filters.role.split(',').filter(Boolean).includes(filters.roleInput.trim())
            ) {
              const arr = [...filters.role.split(',').filter(Boolean), filters.roleInput.trim()]
              handleFilterChange('role', arr.join(','));
              handleFilterChange('roleInput', '');
              e.preventDefault();
            }
            if (e.key === 'Backspace' && !filters.roleInput && filters.role.split(',').filter(Boolean).length > 0) {
              const arr = filters.role.split(',').filter(Boolean).slice(0, -1)
              handleFilterChange('role', arr.join(','));
            }
          }}
        />

        {roleDropdownOpen && filters.role.split(',').filter(Boolean).length > 0 && (
          <div className="absolute left-0 right-0 mt-1 bg-popover border border-input rounded-md shadow-lg z-10 p-2 flex flex-wrap gap-1">
            {filters.role.split(',').filter(Boolean).map((val: string) => (
              <div className="flex gap-1 items-center bg-muted rounded p-1" key={val}>
                <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                  {val}
                </Badge>
                <X className="h-3 w-3 cursor-pointer"
                  onMouseDown={e => e.preventDefault()}
                  onClick={() => {
                    const arr = filters.role.split(',').filter(Boolean).filter((v: string) => v !== val)
                    handleFilterChange('role', arr.join(','))
                  }} />
              </div>
            ))}
          </div>
        )}
      </div>
      {/* Country multi-select popover (copied pattern from members) */}
      <Popover>
        <PopoverTrigger asChild>
          <div className={cn(
            'flex min-h-[36px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer',
            !filters.country.length && 'text-muted-foreground'
          )}>
            {filters.country.length === 0 && <span>Select country</span>}
            {filters.country.split(',').filter(Boolean).slice(0, 2).map((idStr: string) => {
              const id = Number(idStr)
              const country = countries.find((c: any) => c.id === id);
              return (
                <div className="flex gap-1 items-center bg-muted rounded p-1" key={id}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                    {country?.name ?? id}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer" onClick={e => {
                    e.stopPropagation();
                    const arr = filters.country.split(',').filter(Boolean).filter((v: string) => v !== idStr)
                    handleFilterChange('country', arr.join(','))
                  }} />
                </div>
              )
            })}
            {filters.country.split(',').filter(Boolean).length > 2 && (
              <span className="ml-2 text-muted-foreground">...</span>
            )}
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput placeholder="Search country..." />
            <CommandEmpty>No country found.</CommandEmpty>
            <CommandGroup>
              {(() => {
                const selectedArr = filters.country.split(',').filter(Boolean)
                const sortedCountries = [
                  ...countries.filter((c: any) => selectedArr.includes(String(c.id))),
                  ...countries.filter((c: any) => !selectedArr.includes(String(c.id))),
                ];
                return sortedCountries.map((country: any) => (
                  <CommandItem
                    key={country.id}
                    onSelect={() => {
                      const arr = filters.country.split(',').filter(Boolean)
                      const selected = arr.includes(String(country.id))
                        ? arr.filter((v: string) => v !== String(country.id))
                        : [...arr, String(country.id)]
                      handleFilterChange('country', selected.join(','))
                    }}
                    className="cursor-pointer"
                  >
                    <span>{country.name}</span>
                    {selectedArr.includes(String(country.id)) && (
                      <span className="ml-auto text-primary">✓</span>
                    )}
                  </CommandItem>
                ));
              })()}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
      <div className="flex gap-2">
        <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>
          <PopoverTrigger asChild>
            <div className="relative w-[160px] bg-card">
              <Input
                readOnly
                value={filters.startDate ? format(filters.startDate, "yyyy-MM-dd") : ""}
                placeholder="From Date"
                className="pr-10 cursor-pointer h-9"
                onClick={() => setStartDateOpen(true)}
              />
              <CalendarIcon
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"
                width={20}
                height={20}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="relative">
              <Calendar
                mode="single"
                selected={filters.startDate}
                className="rounded-md border shadow-sm"
                captionLayout="dropdown"
                disabled={(date) => filters.endDate ? date > filters.endDate : false}
                onSelect={(date) => {
                  if (date) {
                    handleFilterChange('startDate', date);
                    setStartDateOpen(false);
                  }
                }}
              />
            </div>
          </PopoverContent>
        </Popover>
        <Popover open={endDateOpen} onOpenChange={setEndDateOpen}>
          <PopoverTrigger asChild>
            <div className="relative w-[160px] bg-card">
              <Input
                readOnly
                value={filters.endDate ? format(filters.endDate, "yyyy-MM-dd") : ""}
                placeholder="To Date"
                className="pr-10 cursor-pointer h-9"
                onClick={() => setEndDateOpen(true)}
              />
              <CalendarIcon
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"
                width={20}
                height={20}
              />
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="relative">
              <Calendar
                mode="single"
                selected={filters.endDate}
                className="rounded-md border shadow-sm"
                captionLayout="dropdown"
                disabled={(date) => filters.startDate ? date < filters.startDate : false}
                onSelect={(date) => {
                  if (date) {
                    handleFilterChange('endDate', date);
                    setEndDateOpen(false);
                  }
                }}
              />
            </div>
          </PopoverContent>
        </Popover>
      </div>
      <Button
        onClick={handleSearch}
        disabled={!hasActiveFilters}
        className="h-8 px-3"
        type="button"
      >
        Search
      </Button>
      {hasSearched && (
        <Button
          variant="outline"
          onClick={handleReset}
          className="h-8 px-3"
        >
          Reset
        </Button>
      )}
    </div>
  )
}
