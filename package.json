{"name": "redsoft-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@lexical/code": "^0.32.1", "@lexical/html": "^0.32.1", "@lexical/link": "^0.32.1", "@lexical/list": "^0.32.1", "@lexical/react": "^0.32.1", "@lexical/rich-text": "^0.32.1", "@lexical/selection": "^0.32.1", "@lexical/table": "^0.32.1", "@lexical/utils": "^0.32.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.33.0", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.79.0", "@tanstack/react-router": "^1.120.11", "@tanstack/react-table": "^8.21.3", "@tinymce/tinymce-react": "^6.2.1", "@tiptap/extension-code-block-lowlight": "^2.22.0", "@tiptap/extension-color": "^2.22.0", "@tiptap/extension-heading": "^2.22.0", "@tiptap/extension-horizontal-rule": "^2.22.0", "@tiptap/extension-image": "^2.22.0", "@tiptap/extension-link": "^2.22.0", "@tiptap/extension-placeholder": "^2.22.0", "@tiptap/extension-text-style": "^2.22.0", "@tiptap/extension-typography": "^2.22.0", "@tiptap/extension-underline": "^2.22.0", "@tiptap/pm": "^2.22.0", "@tiptap/react": "^2.22.0", "@tiptap/starter-kit": "^2.22.0", "@udecode/plate": "^49.0.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "draft-js": "^0.11.7", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "lexical": "^0.32.1", "lowlight": "^3.3.0", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-idle-timer": "^5.7.2", "react-medium-image-zoom": "^5.2.14", "react-top-loading-bar": "^3.0.2", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.1", "zod": "^3.25.36", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.27.0", "@faker-js/faker": "^9.8.0", "@tailwindcss/node": "^4.1.12", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query-devtools": "^5.79.0", "@tanstack/react-router-devtools": "^1.120.11", "@tanstack/router-plugin": "^1.120.11", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/draft-js": "^0.11.18", "@types/js-cookie": "^3.0.6", "@types/node": "^22.15.24", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react-swc": "^3.10.0", "eslint": "^9.27.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.12", "tailwindcss-animate": "^1.0.7", "typescript": "~5.8.3", "typescript-eslint": "^8.33.0", "vite": "^6.3.5"}}