import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { S3_BASE_URL } from '../../utils/utilities'
import { useEffect, useState } from 'react'

interface ProfilePicturesGridProps {
  member: any
}

export default function ProfilePicturesGrid({ member }: ProfilePicturesGridProps) {
  const [pictures, setPictures] = useState(member?.images)

  useEffect(() => {
    setPictures(member?.images)
  }, [member])

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Profile Pictures</CardTitle>
        <p className="text-sm text-muted-foreground">
          {member?.username}'s photo gallery
        </p>
      </CardHeader>
      <CardContent>
        {/* 5x5 Grid of pictures */}
        <div className="grid grid-cols-5 gap-3 mt-4">
          {pictures?.map((picture: any, index: any) => (
            <div
              key={picture?.id}
              className="relative aspect-square rounded-lg overflow-hidden group cursor-pointer hover:opacity-90 transition-opacity"
            >
              <img
                src={S3_BASE_URL + picture?.image}
                alt={`${member?.profile} ${index + 1}`}
                className="w-full h-full object-cover"
                loading="lazy"
              />
              {/* Main picture indicator */}
              {picture?.isMain && (
                <div className="absolute top-2 left-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                  Main
                </div>
              )}
              {/* Hover overlay */}
              <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
          ))}
        </div>

        {/* Photo count info */}
        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-sm">Photo Gallery</h4>
              <p className="text-sm text-muted-foreground">
                {pictures?.length} photos available
              </p>
            </div>
            {/* <div className="text-sm text-muted-foreground">
              Last updated: Jul 07, 2025
            </div> */}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
