import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useEffect, useState } from "react";
import { getFieldConfigByType, getFieldTypeOptions } from "../field-config";
import { InformationFormData, InformationModalProps } from "../types";

export function InformationModal({
  open,
  onOpenChange,
  selectedField,
  onSave,
  fieldValues,
  onFieldValuesChange,
}: InformationModalProps) {
  const [formData, setFormData] = useState<InformationFormData>({
    type: getFieldTypeOptions()[0]?.value || "Name",
    content: "",
  });

  useEffect(() => {
    if (selectedField) {
      setFormData({
        type: selectedField.type,
        content: selectedField.items,
      });
    } else {
      const firstFieldType = getFieldTypeOptions()[0]?.value || "Name";
      setFormData({
        type: firstFieldType,
        content: "",
      });
    }
  }, [selectedField, open]);

  const handleSave = () => {
    if (formData.content.trim()) {
      const fieldConfig = getFieldConfigByType(formData.type);
      if (fieldConfig) {
        const updatedFieldValues = {
          ...fieldValues,
          [fieldConfig.id]: formData.content,
        };
        onFieldValuesChange(updatedFieldValues);
      }

      onSave(formData);
      onOpenChange(false);
    }
  };

  const handleTypeChange = (value: string) => {
    const currentFieldConfig = getFieldConfigByType(formData.type);
    if (currentFieldConfig && formData.content.trim()) {
      const updatedFieldValues = {
        ...fieldValues,
        [currentFieldConfig.id]: formData.content,
      };
      onFieldValuesChange(updatedFieldValues);
    }

    const newFieldConfig = getFieldConfigByType(value);
    const newContent = newFieldConfig
      ? fieldValues[newFieldConfig.id] || ""
      : "";

    setFormData({
      type: value,
      content: newContent,
    });
  };

  const currentFieldConfig = getFieldConfigByType(formData.type);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md bg-white">
        <DialogHeader>
          <DialogTitle>
            {selectedField ? `Edit ${selectedField.type}` : "Add Information"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">Type</label>
            <Select value={formData.type} onValueChange={handleTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                {getFieldTypeOptions().map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">
              {currentFieldConfig?.label || "Information"}
            </label>
            {currentFieldConfig?.inputType === "input" ? (
              <Input
                placeholder={currentFieldConfig.placeholder}
                value={formData.content}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, content: e.target.value }))
                }
              />
            ) : (
              <Textarea
                placeholder={
                  currentFieldConfig?.placeholder ||
                  `Enter ${formData.type.toLowerCase()} information`
                }
                value={formData.content}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, content: e.target.value }))
                }
                rows={4}
              />
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={!formData.content}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
