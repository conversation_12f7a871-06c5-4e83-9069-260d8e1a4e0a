import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getSmokingHabitsApi = (params: any = {}) => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.SMOKING_HABITS, { params })).data ?? {},
    queryKey: ["smoking-habits-list"],
});

export const addSmokingHabitsApi = () => useMutation({
    mutationFn: async (data: any) => (await apiClient.post(API_ENDPOINTS.SMOKING_HABITS, data)).data,
});

export const updateSmokingHabitsApi = () => useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => (await apiClient.put(`${API_ENDPOINTS.SMOKING_HABITS}/${id}`, data)).data,
});

export const deleteSmokingHabitsApi = () => useMutation({
    mutationFn: async (id: string) => (await apiClient.delete(`${API_ENDPOINTS.SMOKING_HABITS}/${id}`)).data,
});

export const getSmokingHabitsDetails = (id: string) => useQuery({
    queryFn: async () => (await apiClient.get(`${API_ENDPOINTS.SMOKING_HABITS}/${id}`)).data ?? {},
    queryKey: ["smoking-habits-details", id],
    enabled: !!id,
});

export const getMasterLanguagesApi = () => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES)).data ?? {},
    queryKey: ["master-languages"],
});
