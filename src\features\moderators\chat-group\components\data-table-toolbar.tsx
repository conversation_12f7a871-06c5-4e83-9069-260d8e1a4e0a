import { Cross2Icon } from '@radix-ui/react-icons'
import { Table } from '@tanstack/react-table'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useState } from 'react'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
}

const moderatorOptions = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>'
]

const domainOptions = [
  'discreetdating.club',
  'passionhub.net',
  'lovequest.org',
  'secretlovers.co',
  'adultmatch.com',
  'flirtzone.com',
  'uforpls.female.deso',
  'example.com',
  'testdomain.net',
  'sample.org'
]

const countryOptions = [
  'Denmark',
  'Norway',
  'Sweden',
  'United Kingdom',
  'Ireland',
  'United States',
  'Canada',
  'Australia',
  'Germany',
  'France',
  'Spain',
  'Italy',
  'Japan',
  'India'
]

export function DataTableToolbar<TData>({
  table,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  const [filters, setFilters] = useState({
    search: '',
    moderator: undefined as string | undefined,
    domain: undefined as string | undefined,
    country: undefined as string | undefined,
  })

  const [hasSearched, setHasSearched] = useState(false)

  const handleFilterChange = (
    key: 'search' | 'moderator' | 'domain' | 'country',
    value: string | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  const hasActiveFilters = filters.search.trim() !== '' ||
    filters.moderator !== undefined ||
    filters.domain !== undefined ||
    filters.country !== undefined

  const handleSearch = () => {
    if (!hasActiveFilters) return

    // Apply filters to table
    const columnFilters = []

    if (filters.search.trim()) {
      columnFilters.push({
        id: 'moderator',
        value: filters.search.trim(),
      })
    }

    if (filters.moderator) {
      columnFilters.push({
        id: 'moderator',
        value: filters.moderator,
      })
    }

    if (filters.domain) {
      columnFilters.push({
        id: 'domain',
        value: filters.domain,
      })
    }

    if (filters.country) {
      columnFilters.push({
        id: 'country',
        value: filters.country,
      })
    }

    table.setColumnFilters(columnFilters)
    setHasSearched(true)
  }

  const handleReset = () => {
    setFilters({
      search: '',
      moderator: undefined,
      domain: undefined,
      country: undefined,
    })
    table.resetColumnFilters()
    setHasSearched(false)
  }

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center gap-4 flex-wrap'>
        <FilterSelect
          value={filters.moderator}
          placeholder="Select Moderator"
          options={moderatorOptions}
          onChange={(value) => handleFilterChange('moderator', value)}
        />
        <FilterSelect
          value={filters.domain}
          placeholder="Select Domain"
          options={domainOptions}
          onChange={(value) => handleFilterChange('domain', value)}
        />
        <FilterSelect
          value={filters.country}
          placeholder="Select Country"
          options={countryOptions}
          onChange={(value) => handleFilterChange('country', value)}
        />
        <Button
          onClick={handleSearch}
          className="h-8 px-3"
          disabled={!hasActiveFilters}
        >
          Search
        </Button>
        {(isFiltered || hasSearched) && (
          <Button
            variant='outline'
            onClick={handleReset}
            className='h-8 px-2 lg:px-3'
          >
            Reset
            {/* <Cross2Icon className='ml-2 h-4 w-4' /> */}
          </Button>
        )}
      </div>
    </div>
  )
}
