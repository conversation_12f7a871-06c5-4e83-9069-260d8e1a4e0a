import { use<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import { members } from '../../data/members'
import ProfileCard from '../components/profile-card'
import ProfilePicturesGrid from '../components/profile-pictures-grid'
import { IconArrowLeft } from '@tabler/icons-react'
import { Main } from '@/components/layout/main'

export default function MemberProfilePictures() {
  const { memberId } = useParams({ from: '/_authenticated/members/profile/$memberId/pictures' })

  // Find the member by ID (in a real app, this would be an API call)
  const member = members.find(m => m.id === memberId)

  if (!member) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Member not found</p>
      </div>
    )
  }

  return (
    <Main>
      <div className=" mx-auto">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
          <Link to="/members" className="flex items-center gap-1 hover:text-foreground">
            <IconArrowLeft className="h-4 w-4" />
            Members List
          </Link>
          <span>/</span>
          <Link to="/members/profile/$memberId" params={{ memberId }} className="hover:text-foreground">
            View Profile
          </Link>
          <span>/</span>
          <span className="text-foreground">Profile Pictures</span>
        </div>

        {/* Main Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Side - Profile Card */}
          <div className="lg:col-span-1">
            <ProfileCard member={member} />
          </div>

          {/* Right Side - Profile Pictures Grid */}
          <div className="lg:col-span-3">
            <ProfilePicturesGrid member={member} />
          </div>
        </div>
      </div>
    </Main>
  )
}
