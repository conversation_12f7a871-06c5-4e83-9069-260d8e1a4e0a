import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { useEffect, useMemo, useState } from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput } from '@/components/ui/command'
import { CommandItem } from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { getCountryList } from '@/features/members/api'
import { ADD_CITIES, CITY_PLACEHOLDER } from '@/utils/constant'
import { Input } from '@/components/ui/input'

interface Props<TData> {
    readonly table: Table<TData>
    readonly onFilterChanged?: any
}

export function DataTableToolbar<TData>({ table, onFilterChanged }: Props<TData>) {
    const isFiltered = table.getState().columnFilters.length > 0
    const { data: countryList } = getCountryList()
    const [countries, setCountries] = useState<any>(countryList?.country || [])

    useEffect(() => {
        setCountries(countryList?.country || [])
    }, [countryList?.country?.length])

    const [filters, setFilters] = useState<{
        search: string;
        country: string;
        city: string;
        cityInput: string;
    }>({
        search: '',
        country: '',
        city: '',
        cityInput: '',
    })

    const [hasSearched, setHasSearched] = useState(false)
    const [cityDropdownOpen, setCityDropdownOpen] = useState(false)
    const [initialFilters, setInitialFilters] = useState(filters)

    const handleFilterChange = (
        key: 'search' | 'country' | 'city' | 'cityInput',
        value: string | string[]
    ) => {
        if (['country', 'city'].includes(key)) {
            if (Array.isArray(value)) {
                value = value.filter(Boolean).join(',');
            }
        }
        setFilters((prev) => ({ ...prev, [key]: value as string }));
    }

    const handleSearch = () => {
        if (filters.search) {
            const searchColumn = table.getColumn('email') || table.getColumn('name')
            searchColumn?.setFilterValue(filters.search)
        }
        if (filters.country) {
            const countryColumn = table.getColumn('country')
            if (countryColumn) {
                countryColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string) => {
                    return filterValue.split(',').includes(row.original.country)
                }
                countryColumn.setFilterValue(filters.country)
            }
        }
        if (filters.city) {
            const cityColumn = table.getColumn('city')
            if (cityColumn) {
                cityColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string) => {
                    return filterValue.split(',').includes(row.original.city)
                }
                cityColumn.setFilterValue(filters.city)
            }
        }

        setHasSearched(true)
        setInitialFilters({ ...filters })
        onFilterChanged({ search: filters.search, country: filters.country, city: filters.city }, 1)
    }

    const handleReset = () => {
        table.resetColumnFilters()
        const f = { search: '', country: '', city: '', cityInput: '' }
        setFilters(f)
        setInitialFilters(f)
        setHasSearched(false)
        onFilterChanged(f, 0)
    }

    const hasFilterChanges = Boolean(
        filters.search !== initialFilters.search ||
        filters.country !== initialFilters.country ||
        filters.city !== initialFilters.city
    )

    return (
        <div className='flex items-center justify-between'>
            <div className='flex flex-1 items-center gap-4 flex-wrap'>
                <Input
                    placeholder='Search by name or email'
                    value={filters.search}
                    onChange={(event) => handleFilterChange('search', event.target.value)}
                    className='h-9 w-[250px]'
                />

                {/* Country multi-select */}
                <Popover>
                    <PopoverTrigger asChild>
                        <div className={cn(
                            "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer",
                            !filters.country && "text-muted-foreground"
                        )}>
                            {filters.country === '' && <span>Select country</span>}
                            {filters.country && filters.country.split(',').slice(0, 2).map((id: string) => {
                                const country = countries.find((c: any) => String(c.id) === id);
                                return (
                                    <div className="flex gap-1 items-center bg-muted rounded p-1" key={id}>
                                        <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                                            {country?.name ?? id}
                                        </Badge>
                                        <X className="h-3 w-3 cursor-pointer" onClick={e => {
                                            e.stopPropagation();
                                            handleFilterChange('country', filters.country.split(',').filter(Boolean).filter((v: string) => v !== id))
                                        }} />
                                    </div>
                                )
                            })}
                            {filters.country && filters.country.split(',').filter(Boolean).length > 2 && (
                                <span className="ml-2 text-muted-foreground">...</span>
                            )}
                        </div>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                        <Command>
                            <CommandInput placeholder="Search country..." />
                            <CommandEmpty>No country found.</CommandEmpty>
                            <CommandGroup>
                                {(() => {
                                    const selected = filters.country.split(',').filter(Boolean)
                                    const sorted = [
                                        ...countries.filter((c: any) => selected.includes(String(c.id))),
                                        ...countries.filter((c: any) => !selected.includes(String(c.id))),
                                    ];
                                    return sorted.map((country: any) => (
                                        <CommandItem
                                            key={country.id}
                                            onSelect={() => {
                                                const current = filters.country.split(',').filter(Boolean)
                                                const isSelected = current.includes(String(country.id))
                                                const next = isSelected ? current.filter((v: string) => v !== String(country.id)) : [...current, String(country.id)]
                                                handleFilterChange('country', next)
                                            }}
                                            className="cursor-pointer"
                                        >
                                            <span>{country.name}</span>
                                            {filters.country.split(',').includes(String(country.id)) && (
                                                <span className="ml-auto text-primary">✓</span>
                                            )}
                                        </CommandItem>
                                    ));
                                })()}
                            </CommandGroup>
                        </Command>
                    </PopoverContent>
                </Popover>

                {/* Cities multi-input */}
                <div className="relative w-[200px]">
                    <input
                        type="text"
                        className="w-full border border-input bg-card text-foreground rounded-md px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground"
                        placeholder={filters.city.split(',').filter(Boolean).length === 0 ? CITY_PLACEHOLDER : ADD_CITIES}
                        value={filters.cityInput || ""}
                        onFocus={() => setCityDropdownOpen(true)}
                        onBlur={() => setTimeout(() => setCityDropdownOpen(false), 150)}
                        onChange={e => handleFilterChange('cityInput', e.target.value)}
                        onKeyDown={e => {
                            if ((e.key === 'Enter' || e.key === ',') && filters.cityInput && !filters.city.split(',').filter(Boolean).includes(filters.cityInput.trim())) {
                                handleFilterChange('city', [...filters.city.split(',').filter(Boolean), filters.cityInput.trim()])
                                handleFilterChange('cityInput', '')
                                e.preventDefault()
                            }
                            if (e.key === 'Backspace' && !filters.cityInput && filters.city) {
                                handleFilterChange('city', filters.city.split(',').filter(Boolean).slice(0, -1))
                            }
                        }}
                    />
                    {cityDropdownOpen && filters.city && (
                        <div className="absolute left-0 right-0 mt-1 bg-popover border border-input rounded-md shadow-lg z-10 p-2 flex flex-wrap gap-1">
                            {filters.city.split(',').filter(Boolean).map((city: string) => (
                                <div className="flex gap-1 items-center bg-muted rounded p-1" key={city}>
                                    <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                                        {city}
                                    </Badge>
                                    <X className="h-3 w-3 cursor-pointer"
                                        onMouseDown={e => e.preventDefault()}
                                        onClick={() => {
                                            handleFilterChange('city', filters.city.split(',').filter(Boolean).filter((v: string) => v !== city))
                                        }} />
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                <Button onClick={handleSearch} className="h-8 px-3" disabled={!hasFilterChanges}>
                    Search
                </Button>
                {(isFiltered || hasSearched) && (
                    <Button variant='outline' onClick={handleReset} className='h-8 px-2 lg:px-3'>
                        Reset
                    </Button>
                )}
            </div>
        </div>
    )
}
