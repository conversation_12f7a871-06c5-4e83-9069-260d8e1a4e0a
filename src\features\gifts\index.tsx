import { Main } from "@/components/layout/main";
import { GiftTable } from "./components/gift-table";
import { GiftPrimaryButtons } from "./components/gift-primary-button";
import { columns } from "./components/gifts-columns";

export default function List() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">
                        Gifts
                    </h2>
                </div>
                <GiftPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <GiftTable columns={columns} />
            </div>
        </Main>
    );
} 