import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getAppearancesApi = (params: any = {}) => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.APPEARANCES, { params })).data ?? {},
    queryKey: ["appearances-list"],
});

export const addAppearancesApi = () => useMutation({
    mutationFn: async (data: any) => (await apiClient.post(API_ENDPOINTS.APPEARANCES, data)).data,
});

export const updateAppearancesApi = () => useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => (await apiClient.put(`${API_ENDPOINTS.APPEARANCES}/${id}`, data)).data,
});

export const deleteAppearancesApi = () => useMutation({
    mutationFn: async (id: string) => (await apiClient.delete(`${API_ENDPOINTS.APPEARANCES}/${id}`)).data,
});

export const getAppearancesDetails = (id: string) => useQuery({
    queryFn: async () => (await apiClient.get(`${API_ENDPOINTS.APPEARANCES}/${id}`)).data ?? {},
    queryKey: ["appearances-details", id],
    enabled: !!id,
});

export const getMasterLanguagesApi = () => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES)).data ?? {},
    queryKey: ["master-languages"],
});
