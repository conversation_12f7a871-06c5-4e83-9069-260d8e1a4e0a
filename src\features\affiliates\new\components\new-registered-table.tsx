import { useEffect, useState } from 'react'
import {
    ColumnFiltersState,
    RowData,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from '@tanstack/react-table'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { PaginationControls } from '@/components/ui/PaginationControls'
import { DataTableToolbar } from './toolbar'

export function NewRegisteredTable({ columns }: any) {
    const [rowSelection, setRowSelection] = useState({})
    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [sorting, setSorting] = useState<SortingState>([])
    const [filters, setFilters] = useState<any>({ search: '', country: "", city: "" })
    const [pageIndex, setPageIndex] = useState(0)
    const [pageSize, setPageSize] = useState(10)

    // Mock data - replace with actual API call
    const mockData = {
        affiliates: [],
        meta: {
            total: 0,
            page: 1,
            pages: 1,
            limit: 10
        }
    }

    const { data = mockData } = { data: mockData } // Replace with actual API hook

    // Inject serial numbers into the data for the current page
    const dataWithSerialNumbers = (data?.affiliates || []).map((item: any, idx: number) => ({
        ...item,
        serialNumber: idx + 1 + (pageIndex * pageSize),
    }));

    const table = useReactTable({
        data: dataWithSerialNumbers,
        columns,
        pageCount: data?.meta?.pages ?? -1,
        manualPagination: true,
        state: {
            pagination: { pageIndex, pageSize },
            sorting,
            columnVisibility,
            rowSelection,
            columnFilters,
        },
        onPaginationChange: (updater) => {
            const newState =
                typeof updater === 'function' ? updater({ pageIndex, pageSize }) : updater
            setPageIndex(newState.pageIndex)
            setPageSize(newState.pageSize)
        },
        enableRowSelection: true,
        onRowSelectionChange: setRowSelection,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
    })

    useEffect(() => {
        // Replace with actual refetch call
        console.log('Refetching data with filters:', filters, 'page:', pageIndex + 1)
    }, [filters, pageIndex, pageSize])

    const handleFilterChange = (newFilters: any, page: number = 1) => {
        setFilters(newFilters)
        setPageIndex(0) // Reset to first page when filters change
    }

    const handlePageChange = (newPageIndex: number) => {
        setPageIndex(newPageIndex)
    }

    const handlePageSizeChange = (newPageSize: number) => {
        setPageSize(newPageSize)
        setPageIndex(0) // Reset to first page when page size changes
    }

    return (
        <div className='space-y-4'>
            <DataTableToolbar
                table={table}
                onFilterChanged={handleFilterChange}
            />

            <div className='rounded-md border'>
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    return (
                                        <TableHead key={header.id} className={header.column.columnDef.meta?.className}>
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                        </TableHead>
                                    )
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && "selected"}
                                    className="group/row"
                                >
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id} className={cell.column.columnDef.meta?.className}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className="h-24 text-center"
                                >
                                    No results found.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>

            <PaginationControls
                table={table}
                meta={data?.meta}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
            />
        </div>
    )
}
