import { IconEdit } from "@tabler/icons-react";
import { PersonalInfo } from "./types";

interface PersonalInfoSectionProps {
  personalInfo: PersonalInfo[];
}

export function PersonalInfoSection({ personalInfo }: PersonalInfoSectionProps) {
  return (
    <div className="flex flex-col gap-3 bg-sidebar p-4 rounded-2xl">
      {personalInfo.map((info) => (
        <div key={info.id} className="bg-sidebar-accent p-3 rounded-lg">
          <div className="flex gap-4 items-center justify-between">
            <div className="flex items-center gap-1 text-sm">
              <info.icon width="18px" />
              {info.title}
            </div>
            <button 
              className="rounded-full flex justify-center w-[24px] h-[24px] bg-background"
              onClick={info.onEdit}
            >
              <IconEdit width="14px" />
            </button>
          </div>
          <hr className="my-3" />
          <ul className="list-disc ps-5">
            {info.items.map((item, index) => (
              <li key={index} className="text-xs">{item}</li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
}
