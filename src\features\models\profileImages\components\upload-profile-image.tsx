import { useState, useRef } from 'react'
import { IconPlus } from '@tabler/icons-react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'
import { useQueryClient } from '@tanstack/react-query'
import {
    getPresignedUrl,
    uploadFileToS3,
    addModelProfileImageApi
} from '../../api'
import { validateImageFile } from '@/features/members/utils/utilities'

interface UploadProfileImageProps {
    modelId: string
    modelName?: string
}

export default function UploadProfileImage({ modelId, modelName }: UploadProfileImageProps) {
    const [isUploading, setIsUploading] = useState(false)
    const fileInputRef = useRef<HTMLInputElement>(null)
    const queryClient = useQueryClient()

    // API mutations
    const { mutateAsync: getPreSignedUrlMutation } = getPresignedUrl()
    const { mutateAsync: uploadFileToS3Mutation } = uploadFileToS3()
    const { mutateAsync: addModelProfileImageMutation } = addModelProfileImageApi()

    const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]
        if (!file) return

        const error = validateImageFile(file)
        if (error) {
            toast.error(error)
            return
        }

        setIsUploading(true)
        try {
            const ext = file.name.split(".").pop()?.toLowerCase() || "jpg"
            const presignedRes: any = await getPreSignedUrlMutation({
                location: "models",
                type: ext,
                count: 1,
            })
            const fileData = presignedRes?.data?.files?.[0]
            if (!fileData) {
                toast.error("Failed to get S3 upload URL.")
                return
            }
            await uploadFileToS3Mutation({
                url: fileData.url,
                file,
            })

            // Add the image to the model's profile images
            await addModelProfileImageMutation({
                modelId: modelId,
                imagePath: fileData.filename
            })

            toast.success("Image uploaded successfully!")
            queryClient.invalidateQueries({ queryKey: ['model-details'] })

        } catch (err: any) {
            console.log(err)
            toast.error("Failed to upload image")
        } finally {
            setIsUploading(false)
            e.target.value = ""
        }
    }

    const handleUploadClick = () => {
        fileInputRef.current?.click()
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg">Upload Image</CardTitle>

            </CardHeader>
            <CardContent>
                <div className="flex items-center gap-4">
                    {/* Upload Button */}
                    <div
                        className="w-20 h-20 flex justify-center items-center border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors bg-gray-50"
                        onClick={handleUploadClick}
                    >
                        {isUploading ? (
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                        ) : (
                            <IconPlus className="h-6 w-6 text-gray-400" />
                        )}
                    </div>
                    <div>
                        <p className="text-sm font-medium">Upload to Image gallery</p>
                        <p className="text-xs text-muted-foreground">Click the + icon to upload an image</p>
                    </div>
                    <Input
                        type="file"
                        ref={fileInputRef}
                        className="hidden"
                        onChange={handleImageUpload}
                        accept="image/*"
                        disabled={isUploading}
                    />
                </div>
            </CardContent>
        </Card>
    )
}
