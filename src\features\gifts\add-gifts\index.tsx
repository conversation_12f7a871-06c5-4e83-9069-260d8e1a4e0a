import { Main } from "@/components/layout/main";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useState, useRef, useEffect } from "react";
import { toast } from "sonner";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { X, Plus } from "lucide-react";
import { useAuthStore } from "@/stores/authStore";
import { validateImageFile } from "@/features/members/utils/utilities";
import { addGiftApi, getGiftDetails, getPresignedUrl, updateGiftApi, uploadFileToS3 } from "../api";
import { END_POINTS } from "@/features/members/utils/constant";

const giftSchema = z.object({
    image: z.string().min(1, "Image is required"),
    group: z.array(z.string()).min(1, "Atleast one group is required."),
});

type GiftFormValues = z.infer<typeof giftSchema>;

const groupOptions = [
    { label: "Adult/Casual", value: "Adult/Casual" },
    { label: "Love", value: "Love" },
    { label: "Sexy", value: "Sexy" },
];

export default function AddGift() {
    const navigate = useNavigate()

    const [fileName, setFileName] = useState<string>("")
    const [profileImage, setProfileImage] = useState<string>("")

    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const imageInputRef = useRef<HTMLInputElement>(null);

    const { auth: { setShowSpinner } } = useAuthStore((state) => state)

    const { mutateAsync: getPreSignedUrlMutation } = getPresignedUrl()
    const { mutateAsync: uploadFileToS3Mutation } = uploadFileToS3()
    const { mutateAsync: addGiftMutation } = addGiftApi()
    const { mutateAsync: updateGiftMutation } = updateGiftApi()

    const { msgId } = useParams({ strict: false });
    const { data = {} } = getGiftDetails(msgId);

    const form = useForm<GiftFormValues>({
        resolver: zodResolver(giftSchema),
        defaultValues: {
            image: undefined,
            group: [],
        },
    });

    const { control, handleSubmit, setValue, watch, reset } = form;
    const selectedGroups = watch("group") || [];

    useEffect(() => {
        if (data && data.image) {
            setValue("image", data.image);
            setProfileImage(import.meta.env.VITE_S3_BASE_URL + data.image);
        }
        if (data && data.group) {
            setValue("group", data.group);
        }
    }, [data, setValue]);

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;
        const error = validateImageFile(file);
        if (error) {
            toast.error(error);
            return;
        }
        try {
            const ext = file.name.split(".").pop()?.toLowerCase() || "jpg";
            const presignedRes: any = await getPreSignedUrlMutation({
                location: "gifts",
                type: ext,
                count: 1,
            });
            const fileData = presignedRes?.data?.files?.[0];
            if (!fileData) {
                toast.error("Failed to get S3 upload URL.");
                return;
            }
            await uploadFileToS3Mutation({
                url: fileData.url,
                file,
            });
            setValue("image", fileData.filename)
            setFileName(fileData.filename)
            setProfileImage(import.meta.env.VITE_S3_BASE_URL + fileData.filename)
        } catch (err: any) {
            console.log(err);
        } finally {
            e.target.value = ""
        }
    }

    const onSubmit = async (values: GiftFormValues) => {
        // setShowSpinner(true);

        if (typeof msgId === "string") {
            const response: any = await updateGiftMutation({
                ...values,
                id: data?.id
            })
            if (response?.success) {
                toast.success("Gift has been updated!")
            }
        } else {
            const response: any = addGiftMutation(values)
            // TODO: Implement API call to upload image and create gift
            toast.success("Gift created successfully!");
            navigate({ to: END_POINTS.GIFTS });
        }
    };

    return (
        <Main>
            <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
                    {typeof msgId === "string" ? "Update Gift" : "Add Gift"}
                </h1>
                <p className="text-muted-foreground">
                    {typeof msgId === "string" ? "Update gift to the store." : "Add a new gift to the store."}
                </p>
            </div>
            <Separator className="my-4 lg:my-3" />
            <div className="flex flex-1">
                <Form {...form}>
                    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full">
                        <Card>
                            <CardContent className="grid grid-cols-1 gap-4 mt-6">
                                <FormField
                                    control={control}
                                    name="image"
                                    render={() => (
                                        <FormItem>
                                            <FormLabel>Gift Image File</FormLabel>
                                            <FormControl>
                                                <div
                                                    className="w-16 h-16 flex justify-center items-center border-2 border-dashed rounded-lg cursor-pointer relative overflow-hidden bg-muted"
                                                    onClick={() => imageInputRef.current?.click()}
                                                >
                                                    {profileImage ? (
                                                        <img src={profileImage} alt="Image Preview" className="h-full w-full object-cover" />
                                                    ) : (
                                                        <Plus className="h-6 w-6 text-muted-foreground" />
                                                    )}
                                                    <Input
                                                        type="file"
                                                        ref={imageInputRef}
                                                        className="hidden"
                                                        onChange={handleFileChange}
                                                        accept="image/*"
                                                    />
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <Card>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                                        <FormField
                                            control={control}
                                            name="group"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Groups</FormLabel>
                                                    <Popover>
                                                        <PopoverTrigger asChild>
                                                            <FormControl>
                                                                <div className="relative flex min-h-[36px] items-center justify-end rounded-md border data-[state=open]:border-ring">
                                                                    <div className="relative flex flex-wrap gap-1 p-2">
                                                                        {selectedGroups.length === 0 && (
                                                                            <p className="text-sm text-muted-foreground">Select Groups</p>
                                                                        )}
                                                                        {selectedGroups.length > 0 &&
                                                                            groupOptions
                                                                                .filter((option) => selectedGroups.includes(option.value))
                                                                                .map((option) => (
                                                                                    <Badge
                                                                                        key={option.value}
                                                                                        className="flex items-center gap-1"
                                                                                        variant="outline"
                                                                                    >
                                                                                        {option.label}
                                                                                        <button
                                                                                            onClick={(e) => {
                                                                                                e.preventDefault();
                                                                                                const newValue = selectedGroups.filter((v) => v !== option.value);
                                                                                                field.onChange(newValue);
                                                                                            }}
                                                                                        >
                                                                                            <X className="h-3 w-3" />
                                                                                        </button>
                                                                                    </Badge>
                                                                                ))}
                                                                    </div>

                                                                </div>
                                                            </FormControl>
                                                        </PopoverTrigger>
                                                        <PopoverContent className="w-[300px] p-0">
                                                            <Command>
                                                                <CommandInput placeholder="Search groups..." />
                                                                <CommandEmpty>No group found.</CommandEmpty>
                                                                <CommandGroup>
                                                                    {groupOptions.map((option) => (
                                                                        <CommandItem
                                                                            key={option.value}
                                                                            onSelect={() => {
                                                                                const newValue = selectedGroups.includes(option.value)
                                                                                    ? selectedGroups.filter((v) => v !== option.value)
                                                                                    : [...selectedGroups, option.value];
                                                                                field.onChange(newValue);
                                                                            }}
                                                                        >
                                                                            {option.label}
                                                                        </CommandItem>
                                                                    ))}
                                                                </CommandGroup>
                                                            </Command>
                                                        </PopoverContent>
                                                    </Popover>
                                                    <div className="mt-2 flex gap-2">
                                                        <Button
                                                            style={{ cursor: 'pointer' }}
                                                            type="button"
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => field.onChange(groupOptions.map(o => o.value))}
                                                        >
                                                            Select All
                                                        </Button>
                                                        <Button
                                                            style={{ cursor: 'pointer' }}
                                                            type="button"
                                                            size="sm"
                                                            onClick={() => field.onChange([])}
                                                        >
                                                            Remove All
                                                        </Button>
                                                    </div>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />
                                    </CardContent>
                                </Card>
                            </CardContent>
                        </Card>
                        <div className="flex mt-4 justify-end">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => navigate({ to: END_POINTS.GIFTS })}
                            >
                                Cancel
                            </Button>
                            <Button type="submit">
                                {typeof msgId === "string" ? "Update" : "Save"}
                            </Button>
                        </div>
                    </form>
                </Form>
            </div>
        </Main>
    );
} 