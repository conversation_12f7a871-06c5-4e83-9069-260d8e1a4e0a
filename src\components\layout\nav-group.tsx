import { ReactNode, useState } from 'react'
import { Link } from '@tanstack/react-router'
import { ChevronRight } from 'lucide-react'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { Badge } from '../ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
} from '../ui/dropdown-menu'
import { NavCollapsible, NavLink, type NavGroup as NavGroupType, type NavItem } from './types'

export function NavGroup({ title, items }: NavGroupType) {
  const { state } = useSidebar()
  return (
    <SidebarGroup>
      {title && <SidebarGroupLabel>{title}</SidebarGroupLabel>}
      <SidebarMenu>
        {items.map((item) => {
          const key = `${item.title}-${'url' in item ? item.url : ''}`
          if (!item.items) {
            return <SidebarMenuLink key={key} item={item as NavLink} />
          }
          if (state === 'collapsed') {
            return <SidebarMenuCollapsedDropdown key={key} item={item} />
          }
          return <SidebarMenuCollapsible key={key} item={item} />
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}

const NavBadge = ({ children }: { children: ReactNode }) => (
  <Badge className="rounded-full px-1 py-0 text-xs">{children}</Badge>
)

const SidebarMenuLink = ({ item }: { item: NavLink }) => {
  const { setOpenMobile } = useSidebar()
  const activeUrl = getStoredActiveUrl()
  const isActive = activeUrl === item.url
  return (
    <SidebarMenuItem className={isActive ? "selected-sidebar-item" : ""} >
      <SidebarMenuButton
        asChild
        isActive={item.url === activeUrl}
        tooltip={item.title}
        onClick={() => {
          setStoredActiveUrl(item.url)
        }}
        className={isActive ? "selected-sidebar-item" : ""}
      >
        {item.url ? (
          <Link to={item.url} onClick={() => setOpenMobile(false)}>
            {item.icon && <item.icon />}
            <span>{item.title}</span>
            {item.badge && <NavBadge>{item.badge}</NavBadge>}
          </Link>
        ) : (
          <span style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            {item.icon && <item.icon />}
            <span>{item.title}</span>
            {item.badge && <NavBadge>{item.badge}</NavBadge>}
          </span>
        )}
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}

const SidebarMenuCollapsible = ({
  item,
  path,
}: {
  item: NavCollapsible
  path?: string
}) => {
  const { setOpenMobile } = useSidebar()
  const activeUrl = getStoredActiveUrl()
  const groupKey = `${path ?? ''}/${item.title}`
  const [open, setOpen] = useState<boolean>(() => {
    const stored = getStoredOpenGroups()
    return stored.includes(groupKey) || hasActiveDescendant(item, activeUrl)
  })

  return (
    <Collapsible
      asChild
      open={open}
      onOpenChange={(next) => {
        setOpen(next)
        const groups = getStoredOpenGroups()
        const exists = groups.includes(groupKey)
        if (next && !exists) {
          groups.push(groupKey)
        } else if (!next && exists) {
          const idx = groups.indexOf(groupKey)
          if (idx > -1) groups.splice(idx, 1)
        }
        setStoredOpenGroups(groups)
      }}
      className="group/collapsible"
    >
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton tooltip={item.title}>
            {item.icon && <item.icon />}
            <span>{item.title}</span>
            {item.badge && <NavBadge>{item.badge}</NavBadge>}
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent className="CollapsibleContent">
          <SidebarMenuSub>
            {item.items.map((subItem) => {
              const isSubCollapsible = 'items' in subItem && Array.isArray(subItem.items) && subItem.items.length > 0
              const key = `${subItem.title}-${'url' in subItem ? subItem.url : ''}`
              const isActive = activeUrl === (subItem as any).url
              if (isSubCollapsible) {
                return (
                  <SidebarMenuCollapsible
                    key={key}
                    item={subItem as NavCollapsible}
                    path={groupKey}
                  />
                )
              }

              return (
                <SidebarMenuSubItem key={key} className={isActive ? "selected-sidebar-item" : ""}>
                  <SidebarMenuSubButton
                    asChild
                    isActive={isActive}
                    className={isActive ? "selected-sidebar-item" : ""}
                  >
                    {('url' in subItem && subItem.url) ? (
                      <Link
                        to={(subItem as NavLink).url}
                        onClick={() => {
                          setStoredActiveUrl((subItem as any).url)
                          setOpenMobile(false)
                        }}
                      >
                        {subItem.icon && <subItem.icon className={isActive ? "selected-sidebar-item" : ""} />}
                        <span style={{ fontWeight: isActive ? 600 : "" }}>
                          {subItem.title}
                        </span>
                        {subItem.badge && <NavBadge>{subItem.badge}</NavBadge>}
                      </Link>
                    ) : (
                      <span style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        {subItem.icon && <subItem.icon />}
                        <span>{subItem.title}</span>
                        {subItem.badge && <NavBadge>{subItem.badge}</NavBadge>}
                      </span>
                    )}
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              )
            })}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible >
  )
}

const SidebarMenuCollapsedDropdown = ({
  item,
}: {
  item: NavCollapsible
}) => {
  const activeUrl = getStoredActiveUrl()
  const isActive = hasActiveDescendant(item, activeUrl)
  return (
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuButton
            tooltip={item.title}
            isActive={isActive}
          >
            {item.icon && <item.icon />}
            <span>{item.title}</span>
            {item.badge && <NavBadge>{item.badge}</NavBadge>}
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent side="right" align="start" sideOffset={4}>
          <DropdownMenuLabel>
            {item.title} {item.badge ? `(${item.badge})` : ''}
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <CollapsedDropdownItems items={item.items} />
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  )
}

const CollapsedDropdownItems = ({ items }: { items: NavItem[] }) => {
  const activeUrl = getStoredActiveUrl()
  return (
    <>
      {items.map((sub) => {
        const key = `${sub.title}-${'url' in sub ? sub.url : ''}`
        const hasChildren = 'items' in sub && Array.isArray(sub.items) && sub.items.length > 0
        if (hasChildren) {
          const subgroup = sub as NavCollapsible
          const subgroupActive = hasActiveDescendant(subgroup, activeUrl)
          return (
            <DropdownMenuSub key={key}>
              <DropdownMenuSubTrigger className={`${subgroupActive ? 'bg-accent text-accent-foreground' : ''}`}>
                {sub.icon && <sub.icon />}
                <span className="max-w-52 text-wrap">{sub.title}</span>
                {'badge' in sub && (sub as any).badge && (
                  <span className="ml-auto text-xs">{(sub as any).badge}</span>
                )}
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent sideOffset={8}>
                <CollapsedDropdownItems items={subgroup.items} />
              </DropdownMenuSubContent>
            </DropdownMenuSub>
          )
        }

        const isActive = 'url' in sub && (sub as NavLink).url === activeUrl
        return (
          <DropdownMenuItem key={key} asChild>
            {'url' in sub && sub.url ? (
              <Link
                to={(sub as NavLink).url}
                className={`${isActive ? 'bg-secondary' : ''}`}
                onClick={() => setStoredActiveUrl((sub as NavLink).url)}
              >
                {sub.icon && <sub.icon />}
                <span className="max-w-52 text-wrap">{sub.title}</span>
                {'badge' in sub && (sub as any).badge && (
                  <span className="ml-auto text-xs">{(sub as any).badge}</span>
                )}
              </Link>
            ) : (
              <span style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                {sub.icon && <sub.icon />}
                <span className="max-w-52 text-wrap">{sub.title}</span>
                {'badge' in sub && (sub as any).badge && (
                  <span className="ml-auto text-xs">{(sub as any).badge}</span>
                )}
              </span>
            )}
          </DropdownMenuItem>
        )
      })}
    </>
  )
}

// removed unused checkIsActive

// localStorage helpers for safe persistence
const OPEN_GROUPS_KEY = 'openSidebarGroups'

function getStoredActiveUrl(): string | undefined {
  try {
    const raw = localStorage.getItem('activeSidebarItem')
    if (!raw) return undefined
    const parsed = JSON.parse(raw)
    return typeof parsed === 'string' ? parsed.trim() : undefined
  } catch {
    return undefined
  }
}

function setStoredActiveUrl(url?: string) {
  try {
    if (url) localStorage.setItem('activeSidebarItem', JSON.stringify(url))
    else localStorage.removeItem('activeSidebarItem')
  } catch {
    // ignore
  }
}

function getStoredOpenGroups(): string[] {
  try {
    const raw = localStorage.getItem(OPEN_GROUPS_KEY)
    if (!raw) return []
    const parsed = JSON.parse(raw)
    return Array.isArray(parsed) ? parsed : []
  } catch {
    return []
  }
}

function setStoredOpenGroups(groups: string[]) {
  try {
    localStorage.setItem(OPEN_GROUPS_KEY, JSON.stringify(groups))
  } catch {
    // ignore
  }
}

function hasActiveDescendant(collapsible: NavCollapsible, activeUrl?: string): boolean {
  if (!activeUrl) return false
  return collapsible.items.some((child: any) => {
    if ('items' in child && Array.isArray(child.items) && child.items.length > 0) {
      return hasActiveDescendant(child as NavCollapsible, activeUrl)
    }
    return 'url' in child && child.url === activeUrl
  })
}