interface ProfileDetailsProps {
  moderator: {
    id: string
    name: string
    moderatorType: string
    phoneNumber: string
    nickname: string
  }
}

export default function ProfileDetails({ moderator }: ProfileDetailsProps) {
  const details = [
    { label: 'Minimum Payout:', value: '0.00' },
    { label: 'Payment Currency:', value: 'EUR' },
    { label: 'Payment Method:', value: 'You can pay here' },
    { label: 'Beneficiary Account Name:', value: 'print4_mod' },
    { label: 'Beneficiary Account Address:', value: 'Address will be here' },
    { label: 'Beneficiary Country:', value: 'India' },
    { label: 'Beneficiary City:', value: '24 Parganas North' },
    { label: 'Beneficiary Bank:', value: 'Test' },
    { label: 'Beneficiary Bank Address:', value: 'Test' },
    { label: 'Account / Wallet Number:', value: '**********' },
    { label: 'Bank Code:', value: '7802' },
    { label: 'Personal ID / Photo:', value: 'photo.png' },
  ]

  const stats = [
    { label: 'Reply Percentage', value: '50%' },
    { label: 'Average Character / Message', value: '150' },
    { label: 'Total Hole Messages', value: '123' },
  ]

  return (
    <div className="space-y-4">
      {/* Stats Cards */}
      <div className="space-y-4">
        {stats.map((stat, index) => (
          <div key={stat.label} className="bg-white rounded-lg border p-4">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg font-bold text-gray-600">$</span>
              <span className="text-sm font-medium text-gray-700">{stat.label}</span>
            </div>
            <p className="text-xl font-bold text-gray-900">{stat.value}</p>
          </div>
        ))}
      </div>

      {/* Profile Details */}
      <div className="bg-white rounded-lg border p-4">
        <div className="space-y-3">
          {details.map((detail, index) => (
            <div key={detail.label} className="space-y-1">
              <div className="text-xs text-gray-500">{detail.label}</div>
              <div className="text-sm font-medium text-gray-900">{detail.value}</div>
              {index < details.length - 1 && (
                <div className="border-b border-gray-100 pt-2"></div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
