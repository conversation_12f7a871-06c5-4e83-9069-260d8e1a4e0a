import { z } from "zod";

const memberStatusSchema = z.union([
  z.literal("active"),
  z.literal("inactive"),
  z.literal("invited"),
  z.literal("suspended"),
]);
export type MemberStatus = z.infer<typeof memberStatusSchema>;

const memberSchema = z.object({
  id: z.string(),
  profile: z.string(),
  username: z.string(),
  email: z.string(),
  domain: z.string(),
  countryCity: z.string(),
  joinedOn: z.string(),
  affiliate: z.string(),
  credits: z.number(),
  lastActive: z.string(),
  status: memberStatusSchema,
  emailVerified: z.string(),
  botOn: z.string(),
  // Extended profile fields
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  age: z.string().optional(),
  phoneNumber: z.string().optional(),
  emailVerifiedStatus: z.string().optional(),
  password: z.string().optional(),
  confirmPassword: z.string().optional(),
  gender: z.string().optional(),
  seekingFor: z.string().optional(),
  dob: z.string().optional(),
  country: z.string().optional(),
  state: z.string().optional(),
  city: z.string().optional(),
  // Personal Attributes
  personality: z.string().optional(),
  relationshipStatus: z.string().optional(),
  ethnicity: z.string().optional(),
  religion: z.string().optional(),
  hairColor: z.string().optional(),
  appearance: z.string().optional(),
  eyeColor: z.string().optional(),
  bodyType: z.string().optional(),
  starSign: z.string().optional(),
  smokingHabit: z.string().optional(),
  drinkingHabit: z.string().optional(),
  bestFeature: z.string().optional(),
  bodyArt: z.string().optional(),
  sexualOrientation: z.string().optional(),
  height: z.string().optional(),
  weight: z.string().optional(),
  kids: z.string().optional(),
  interests: z.string().optional(),
  aboutMe: z.string().optional(),
});

export type Member = z.infer<typeof memberSchema>;

export const memberListSchema = z.array(memberSchema);
