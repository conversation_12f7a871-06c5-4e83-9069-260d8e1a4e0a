import { SidebarTrigger } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import React from "react";

interface HeaderProps extends React.HTMLAttributes<HTMLElement> {
  fixed?: boolean;
  ref?: React.Ref<HTMLElement>;
}

export const Header = ({
  className,
  fixed,
  children,
  ...props
}: HeaderProps) => {
  const [offset, setOffset] = React.useState(0);

  React.useEffect(() => {
    const onScroll = () => {
      setOffset(document.body.scrollTop || document.documentElement.scrollTop);
    };
    document.addEventListener("scroll", onScroll, { passive: true });
    return () => document.removeEventListener("scroll", onScroll);
  }, []);

  return (
    <header
      className={cn(
        "bg-sidebar flex h-14 items-center gap-2 p-3 sm:h-16 sm:gap-4 sm:p-4",
        fixed && "header-fixed peer/header fixed z-50 w-[inherit]",
        offset > 10 && fixed ? "shadow-sm" : "shadow-none",
        className
      )}
      {...props}
    >
      <div className="flex items-center gap-2">
        <SidebarTrigger variant="outline" className="scale-110 sm:scale-100" />
      </div>
      {/* <Separator orientation='vertical' className='h-4 sm:h-6' /> */}
      <div className="flex min-w-0 flex-1 items-center justify-between overflow-hidden">
        {children}
      </div>
    </header>
  );
};

Header.displayName = "Header";
