import { useNavigate, useParams } from "@tanstack/react-router";
import ChatProfileView from "./chat-profile-view";

export default function ChatProfile() {
    const navigate = useNavigate()
    const { memberId } = useParams({ from: '/_authenticated/members/profile/$memberId/messages/$conversationId' });
    const backToChat = () => {
        navigate({
            to: "/members/profile/$memberId/messages",
            params: { memberId }
        });
    }
    return (
        <div>
            <ChatProfileView />
        </div>
    );
}
