import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { Member } from '../../data/schema'
import { CreditAddDialog } from './credit-add-dialog'
import { useMemo, useState, useEffect } from 'react'
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { PaginationControls } from '@/components/ui/PaginationControls'
import { getCreditList, manageCoinsAPI } from '@/features/members/api'
import { useQueryClient } from '@tanstack/react-query'
import { formatTimestampToLocal } from '../../utils/utilities'

interface CreditHistoryTableProps {
  member: Member
}

// API data will be loaded via getCreditList; keep type for table rows

type CreditHistory = {
  id: string
  dateTime: string
  action: string
  credits: string
}

type CreditHistoryRow = CreditHistory & { serialNumber: number }

const columns: ColumnDef<CreditHistoryRow>[] = [
  {
    accessorKey: 'serialNumber',
    header: '#',
    cell: ({ row }) => {
      return <span className="text-sm text-muted-foreground">{row.original.serialNumber}</span>
    },
    meta: {
      className: 'w-16'
    }
  },
  {
    accessorKey: 'dateTime',
    header: 'Date/Time',
    cell: ({ row }) => {
      return <span className="text-sm">{formatTimestampToLocal(row.getValue('dateTime'))}</span>
    }
  },
  {
    accessorKey: 'action',
    header: 'Action',
    cell: ({ row }) => {
      console.log(row.original)
      return <span className="text-sm">{row.getValue('action')}</span>
    }
  },
  {
    accessorKey: 'packagesPurchased',
    header: 'Packages purchased',
    cell: ({ row }) => {
      return <span className="text-sm">{row.getValue('packagesPurchased')}</span>
    }
  },
  {
    accessorKey: 'eurosSpent',
    header: 'Euros spent',
    cell: ({ row }) => {
      return <span className="text-sm">€{` ${row.getValue('coins')}`}</span>
    }
  },
  {
    accessorKey: 'credits',
    header: 'Credits',
    cell: ({ row }) => {
      const credits = row.getValue('credits') as string
      return (
        <span className={`text-sm ${row?.original?.action === "CREDIT" ? 'text-green-600' : 'text-red-600'
          } font- medium text - right`}>
          {credits}
        </span>
      )
    },
    meta: {
      className: 'text-right'
    }
  }
]

export default function CreditHistoryTable({ member }: CreditHistoryTableProps) {
  const [open, setOpen] = useState(false)
  const [dialogMode, setDialogMode] = useState<'add' | 'deduct'>('add')
  const [pageIndex, setPageIndex] = useState(0)
  const [pageSize, setPageSize] = useState(10)
  const { mutateAsync: manageCreditsAPI } = manageCoinsAPI()
  const queryClient = useQueryClient()

  const { data: apiData = {}, refetch }: any = getCreditList(member?.id, {
    page: pageIndex + 1,
    limit: pageSize,
  })


  const rawRows: any[] = apiData?.transactions || []

  const pageData: CreditHistoryRow[] = useMemo(() => {
    return (rawRows || []).map((item: any, idx: number) => ({
      id: item.id ?? String(idx),
      dateTime: item.createdAt ?? '',
      action: item.creditType ?? '',
      credits: item.coins ?? '',
      packagesPurchased: item?.package?.packageName ?? '',
      eurosSpent: item?.package?.coins ?? '',
      serialNumber: idx + 1 + (pageIndex * pageSize),
    }))
  }, [rawRows, pageIndex, pageSize])

  console.log(pageData)
  const table = useReactTable({
    data: pageData,
    columns,
    pageCount: apiData?.meta?.pages ?? -1,
    manualPagination: true,
    state: {
      pagination: { pageIndex, pageSize },
    },
    onPaginationChange: (updater) => {
      const newState =
        typeof updater === 'function' ? updater({ pageIndex, pageSize }) : updater
      setPageIndex(newState.pageIndex)
      setPageSize(newState.pageSize)
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  })

  useEffect(() => {
    refetch()
  }, [pageIndex, pageSize, refetch])

  const onPageChange = (nextPageIndex: number) => {
    setPageIndex(nextPageIndex)
  }

  const handleAddSubmit = async (data: any) => {
    const payload: any = {
      userId: member?.id,
      creditType: 'CREDIT',
      coins: Number(data?.amount)
    }
    const response: any = await manageCreditsAPI(payload)
    if (response?.success) {
      queryClient.invalidateQueries({ queryKey: ["credit-list"] });
    }
  }

  const handleDeductSubmit = async (data: any) => {
    const payload: any = {
      userId: member?.id,
      creditType: 'DEBIT',
      coins: Number(data?.amount)
    }
    const response: any = await manageCreditsAPI(payload)
    if (response?.success) {
      queryClient.invalidateQueries({ queryKey: ["credit-list"] });
    }
  }

  return (
    <Card className='p-4 gap-3'>
      <CardHeader className="p-0">
        <div className="flex items-center justify-between">
          <div className='flex gap-2 items-center'>
            <CardTitle className="text-base text-muted-foreground">Credit / Coin Balance:</CardTitle>
            <span className="text-sm font-semibold bg-[#E7E7E7] p-[12px] rounded-full flex gap-1 items-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 25 25" fill="none">
                <path d="M21.5 17.0815V18.4385C21.5 19.2525 21.119 19.9485 20.59 20.4955C20.067 21.0375 19.357 21.4795 18.558 21.8295C16.956 22.5295 14.814 22.9385 12.5 22.9385C10.186 22.9385 8.044 22.5305 6.442 21.8295C5.643 21.4795 4.933 21.0375 4.41 20.4955C3.925 19.9955 3.565 19.3675 3.508 18.6395L3.5 18.4385V17.0815C3.96467 17.3468 4.46267 17.5785 4.994 17.7765C7.024 18.5275 9.679 18.9465 12.5 18.9465C15.321 18.9465 17.976 18.5275 20.006 17.7765C20.4047 17.6285 20.7843 17.4608 21.145 17.2735L21.5 17.0815ZM3.5 11.5815C3.96467 11.8468 4.46267 12.0785 4.994 12.2765C7.024 13.0275 9.679 13.4465 12.5 13.4465C15.321 13.4465 17.976 13.0275 20.006 12.2765C20.5223 12.0863 21.022 11.8538 21.5 11.5815V14.6865C20.8362 15.1994 20.0985 15.6087 19.312 15.9005C17.557 16.5505 15.148 16.9475 12.5 16.9475C9.853 16.9475 7.444 16.5505 5.688 15.9005C4.90151 15.6087 4.16379 15.1994 3.5 14.6865V11.5815ZM12.5 3.93848C14.814 3.93848 16.956 4.34648 18.558 5.04748C19.357 5.39748 20.067 5.83948 20.59 6.38148C21.075 6.88148 21.435 7.50948 21.492 8.23748L21.5 8.43848V9.18648C20.8362 9.69939 20.0985 10.1087 19.312 10.4005C17.557 11.0505 15.148 11.4475 12.5 11.4475C9.853 11.4475 7.444 11.0505 5.688 10.4005C5.01199 10.1493 4.3716 9.81115 3.783 9.39448L3.5 9.18648V8.43848C3.5 7.62448 3.881 6.92848 4.41 6.38148C4.933 5.83948 5.643 5.39748 6.442 5.04748C8.044 4.34748 10.186 3.93848 12.5 3.93848Z" fill="#4F4F4F" />
              </svg>
              {apiData?.remainingCoins}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <select
              className="bg-[#4F4F4F] text-white p-2 rounded text-sm cursor-pointer"
              onChange={(e) => {
                const value = e.target.value
                if (value === 'add') {
                  setDialogMode('add')
                  setOpen(true)
                } else if (value === 'deduct') {
                  setDialogMode('deduct')
                  setOpen(true)
                }
                e.target.value = ''
              }}
            >
              <option value="" disabled selected>Credit</option>
              <option value="add">Add Credits</option>
              <option value="deduct">Deduct Credit</option>
            </select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id} className="group/row">
                  {headerGroup.headers.map((header) => (
                    <TableHead
                      key={header.id}
                      colSpan={header.colSpan}
                      className={(header.column.columnDef as any).meta?.className ?? ''}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} className="group/row">
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className={(cell.column.columnDef as any).meta?.className ?? ''}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {apiData?.meta?.pages > 1 &&
          <PaginationControls
            table={table}
            meta={apiData?.meta}
            onPageChange={onPageChange}
            onPageSizeChange={setPageSize}
          />}

        {/* Table Footer with Summary */}
        {/* <div className="p-4 bg-muted/30 border-t">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">
              Showing {Math.min(apiData?.meta?.total ?? 0, pageIndex * pageSize + 1)}–{Math.min(apiData?.meta?.total ?? 0, (pageIndex + 1) * pageSize)} of {apiData?.meta?.total ?? 0} transactions
            </span>
          </div>
        </div> */}
      </CardContent>

      {/* add credit Modal */}
      <CreditAddDialog
        open={open}
        onOpenChange={setOpen}
        defaultUsername={member?.username}
        currentBalance={apiData?.remainingCoins}
        title={dialogMode === 'add' ? 'Add Credits' : 'Deduct Credits'}
        actionLabel={dialogMode === 'add' ? 'Add' : 'Deduct'}
        onSubmit={dialogMode === 'add' ? handleAddSubmit : handleDeductSubmit}
      />
    </Card>
  )
}