import { useMutation, useQuery, useInfiniteQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const useGetModels = (params = {}) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.MODELS, {
        params,
      });
      return response?.data ?? {};
    },
    queryKey: ["models-list"],
  });

export const addModelApi = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      return await apiClient.post(API_ENDPOINTS.MODELS, payload);
    },
  });

export const updateModelApi = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      return await apiClient.put(
        `${API_ENDPOINTS.MODELS}/${payload?.id}`,
        payload
      );
    },
  });

export const deleteModelApi = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      return await apiClient.delete(`${API_ENDPOINTS.MODELS}/${payload?.id}`);
    },
  });

export const modelStatusChange = () =>
  useMutation({
    mutationFn: async (id: any) => {
      return await apiClient.get(
        `${API_ENDPOINTS.MODELS}/${id}${API_ENDPOINTS.MODELS_STATUS}`
      );
    },
  });

export const getModelDetails = (id: any = {}) =>
  useQuery({
    queryFn: async () => {
      if (typeof id === "string") {
        const response = await apiClient.get(`${API_ENDPOINTS.MODELS}/${id}`);
        return response?.data ?? {}; // return [] or {} as a fallback
      }
      return {};
    },
    queryKey: ["model-details", id],
    enabled: !!id,
  });

export const useGetConversationProfile = (id: any = {}) =>
  useQuery({
    queryFn: async () => {
      if (typeof id === "string") {
        const response = await apiClient.get(
          `${API_ENDPOINTS.CONVERSATION}/${id}`
        );
        return response?.data ?? {};
      }
      return {};
    },
    queryKey: ["conversation-profile", id],
    enabled: !!id,
  });

export const useGetConversationMessages = (
  id: any = {},
  params: { page?: number; limit?: number } = {}
) =>
  useQuery({
    queryFn: async () => {
      if (typeof id === "string") {
        const response = await apiClient.get(
          API_ENDPOINTS.CONVERSATION_MESSAGES(id),
          { params }
        );
        return response?.data ?? {};
      }
      return {};
    },
    queryKey: ["conversation-messages", id, params],
    enabled: !!id,
  });

export const useGetConversationMessagesInfinite = (
  id: any = {},
  limit: number = 10
) =>
  useInfiniteQuery({
    queryFn: async ({ pageParam = 1 }) => {
      if (typeof id === "string") {
        console.log("Fetching page", pageParam);
        const response = await apiClient.get(
          API_ENDPOINTS.CONVERSATION_MESSAGES(id),
          { params: { page: pageParam, limit } }
        );
        return response?.data ?? {};
      }
    },
    queryKey: ["conversation-messages-infinite", id, limit],
    enabled: !!id,
    getNextPageParam: (lastPage) => {
      const { meta } = lastPage;
      if (meta && meta.page < meta.pages) {
        return meta.page + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });

export const useSaveConversationProblem = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      return await apiClient.post(
        API_ENDPOINTS.SAVE_CONVERSATION_PROBLEM,
        payload
      );
    },
  });

export const useUpdateUserConversationInfo = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      return await apiClient.post(
        API_ENDPOINTS.UPDATE_USER_CONVERSATION_INFO,
        payload
      );
    },
  });

export const useSaveConversationNote = () =>
  useMutation({
    mutationFn: async (payload: {
      userId: number;
      conversationId: number;
      note: string;
    }) => {
      return await apiClient.post(API_ENDPOINTS.CONVERSATION_NOTES, payload);
    },
  });

export const useDeleteConversationNote = () =>
  useMutation({
    mutationFn: async (noteId: string) => {
      return await apiClient.delete(
        API_ENDPOINTS.DELETE_CONVERSATION_NOTE(noteId)
      );
    },
  });
