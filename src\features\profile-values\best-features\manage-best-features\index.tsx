import { Main } from "@/components/layout/main";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { addBestFeaturesApi, updateBestFeaturesApi, getBestFeaturesDetails, getMasterLanguagesApi } from "../api";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import { END_POINTS } from "@/features/members/utils/constant";

export default function ManageBestFeatures() {
    const navigate = useNavigate()
    const { bestfeaturesId } = useParams({ strict: false })
    const { data: { languages = [] } = {} } = getMasterLanguagesApi()
    const { mutateAsync: addMutation } = addBestFeaturesApi();
    const { mutateAsync: updateMutation } = updateBestFeaturesApi();
    const { data = {} } = getBestFeaturesDetails(bestfeaturesId || "")
    const [isSubmitting, setIsSubmitting] = useState(false)

    const Schema = useMemo(() => {
        const shape: Record<string, z.ZodString> = {}
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => { const code = lang.code || lang.languageCode || lang.id; if (code) shape[code] = z.string().min(1, "Best feature is required") })
        } else { ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].forEach(code => { shape[code] = z.string().min(1, "Best feature is required") }) }
        return z.object(shape)
    }, [languages])
    type FormValues = z.infer<typeof Schema>

    const defaultValues = useMemo(() => {
        const d: Record<string, string> = {}
        const items = (data as any)?.items || []
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => { const code = lang.code || lang.languageCode || lang.id; if (code) d[code] = (items.find((it: any) => it.languageCode === code)?.message) || "" })
        } else { ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].forEach(code => { d[code] = (items.find((it: any) => it.languageCode === code)?.message) || "" }) }
        return d
    }, [languages, data])

    const form = useForm<FormValues>({ resolver: zodResolver(Schema), defaultValues: defaultValues as FormValues })
    const { control, handleSubmit, reset } = form
    const initializedRef = useRef(false)
    useEffect(() => {
        if (initializedRef.current) return
        if ((languages && languages.length > 0) || bestfeaturesId) {
            reset(defaultValues as FormValues)
            initializedRef.current = true
        }
    }, [languages, bestfeaturesId, defaultValues, reset])

    const onSubmit = async (values: FormValues) => {
        try {
            setIsSubmitting(true)
            const items = Object.entries(values).map(([languageCode, message]) => ({ languageCode, message }))
            const payload = { items }
            if (bestfeaturesId) { await updateMutation({ id: bestfeaturesId, data: payload }); toast.success("Best feature updated successfully!") }
            else { await addMutation(payload); toast.success("Best feature added successfully!") }
            navigate({ to: END_POINTS.BEST_FEATURES })
        } catch (e: any) { toast.error(e?.response?.data?.message || "Action failed") } finally { setIsSubmitting(false) }
    }

    const isEditing = typeof bestfeaturesId === "string" && bestfeaturesId.length > 0

    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">{isEditing ? "Update Best Feature" : "Add Best Feature"}</h2>
                </div>
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <Card>
                    <CardHeader>
                        <CardTitle>Best Feature Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                                <div className="grid grid-cols-2 gap-4">
                                    {(languages && languages.length > 0 ? languages.map((lang: any) => {
                                        const code = lang.code || lang.languageCode || lang.id; const name = lang.name || (code ? String(code).toUpperCase() : '')
                                        return (
                                            <FormField key={code} control={control} name={code as any} render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="text-sm font-medium">{name}</FormLabel>
                                                    <FormControl><Input placeholder={"Enter best feature in " + name} {...field} /></FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                        )
                                    }) : ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].map(code => (
                                        <FormField key={code} control={control} name={code as any} render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-sm font-medium">{String(code).toUpperCase()}</FormLabel>
                                                <FormControl><Input placeholder={"Enter best feature in " + String(code).toUpperCase()} {...field} /></FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />
                                    )))}
                                </div>
                                <Separator />
                                <div className="flex justify-end space-x-2">
                                    <Button type="button" variant="outline" onClick={() => navigate({ to: END_POINTS.BEST_FEATURES })}>Cancel</Button>
                                    <Button type="submit" disabled={isSubmitting}>{isEditing ? "Update Best Feature" : "Add Best Feature"}</Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </Main>
    )
}
