import { useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getCurrencies = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.GET_CURRENCIES);
            return response?.data ?? {}; // return [] or {} as a fallback

        },
        queryKey: ["currencies"],
        staleTime: 0
    });
