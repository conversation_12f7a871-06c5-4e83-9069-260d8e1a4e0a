import { use<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import ProfileCard from './components/profile-card'
import { IconArrowLeft } from '@tabler/icons-react'
import ProfileStats from './components/profile-stats'
import ActivityChart from './components/activity-chart'
import ModelImagesGallery from './components/model-images-gallery'
import { getModelDetails } from '../api'
import { Main } from '@/components/layout/main'

export default function ModelProfile() {
  const { modelId } = useParams({ from: '/_authenticated/models/profile/$modelId' })

  // Fetch model details from API
  const { data: model, isLoading, error } = getModelDetails(modelId)

  if (isLoading) {
    return (
      <Main>
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Loading model profile...</p>
        </div>
      </Main>
    )
  }

  if (error || !model) {
    return (
      <Main>
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Model not found</p>
        </div>
      </Main>
    )
  }

  return (
    <Main>
      <div className="mx-auto">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-gray-600 mb-6">
          <Link to="/models" className="flex items-center gap-1 hover:text-gray-900">
            <IconArrowLeft className="h-4 w-4" />
            Models
          </Link>
          <span>/</span>
          <span className="text-gray-900">Model Profile</span>
        </div>

        {/* Main Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 ">
          {/* Left Side - Profile Card */}
          <div className="lg:col-span-1">
            <ProfileCard model={model} />
          </div>

          {/* Right Side - Stats, Images and Chart */}
          <div className="lg:col-span-3 space-y-6">
            {/* Model Images Gallery */}
            {/* <ModelImagesGallery modelId={modelId} /> */}

            {/* Stats Cards */}
            <ProfileStats />

            {/* Activity Chart */}
            <ActivityChart />
          </div>
        </div>
      </div>
    </Main>
  )
}
