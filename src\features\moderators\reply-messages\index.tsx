import { Main } from "@/components/layout/main";
import { ReplyMessagesTable } from "./components/reply-messages-table";
import { columns } from "./components/reply-messages-columns";
import { replyMessages } from "./data/reply-messages";

export default function ReplyMessages() {
  return (
    <Main>
      <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            All Reply Messages
          </h2>
        </div>
      </div>
      <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
        <ReplyMessagesTable data={replyMessages} columns={columns} />
      </div>
    </Main>
  );
}
