import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import LongText from '@/components/long-text'
import { DataTableColumnHeader } from '@/components/data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'
import { S3_BASE_URL } from '@/features/members/utils/utilities'

export const columns: ColumnDef<any>[] = [
    {
        accessorKey: 'serialNumber',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='#' />
        ),
        cell: ({ row }) => (
            <div className='w-8'>{row.getValue('serialNumber')}</div>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
                'w-8'
            ),
        },
        enableHiding: false,
    },
    {
        accessorKey: 'image',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Gift Image' />
        ),
        cell: ({ row }) => <div className='flex items-center gap-3'>
            <Avatar className='h-8 w-8'>
                <AvatarImage src={S3_BASE_URL + row.original.image} alt="smiley" />
                <AvatarFallback className='text-xs bg-gray-200'>
                </AvatarFallback>
            </Avatar>
        </div>,
        meta: { className: 'w-36' },
    },
    {
        accessorKey: 'groups',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Groups' />
        ),
        cell: ({ row }) => {

            return (
                <LongText className='max-w-60'>{row.original.group.join(", ")}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        id: 'actions',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Action' />
        ),
        enableSorting: false,
        cell: DataTableRowActions,
        meta: { className: 'w-16' },
    },
]
