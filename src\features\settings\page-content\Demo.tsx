"use client"

import { useState, useEffect } from "react"
import {
    InitialConfigType,
    LexicalComposer,
} from "@lexical/react/LexicalComposer"
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext"
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary"
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin"
import { TabIndentationPlugin } from "@lexical/react/LexicalTabIndentationPlugin"
import { HeadingNode, QuoteNode } from "@lexical/rich-text"
import { ParagraphNode, TextNode } from "lexical"
import { AutoLinkNode, LinkNode } from "@lexical/link"

import { TooltipProvider } from "@/components/ui/tooltip"
import { ContentEditable } from "@/components/editor/editor-ui/content-editable"
import { ToolbarPlugin } from "@/components/editor/plugins/toolbar/toolbar-plugin"
import { ElementFormatToolbarPlugin } from "@/components/editor/plugins/toolbar/element-format-toolbar-plugin"
import { editorTheme } from "@/components/editor/themes/editor-theme"
import { ClearFormattingToolbarPlugin } from "@/components/editor/plugins/toolbar/clear-formatting-toolbar-plugin"
import { ListItemNode, ListNode } from "@lexical/list"
import { CheckListPlugin } from "@lexical/react/LexicalCheckListPlugin"

import { ListPlugin } from "@lexical/react/LexicalListPlugin"
import { BlockFormatDropDown } from "@/components/editor/plugins/toolbar/block-format-toolbar-plugin"
import { FormatParagraph } from "@/components/editor/plugins/toolbar/block-format/format-paragraph"
import { FormatHeading } from "@/components/editor/plugins/toolbar/block-format/format-heading"
import { FormatNumberedList } from "@/components/editor/plugins/toolbar/block-format/format-numbered-list"
import { FormatBulletedList } from "@/components/editor/plugins/toolbar/block-format/format-bulleted-list"
import { FormatCheckList } from "@/components/editor/plugins/toolbar/block-format/format-check-list"
import { FormatQuote } from "@/components/editor/plugins/toolbar/block-format/format-quote"
import { FontColorToolbarPlugin } from "@/components/editor/plugins/toolbar/font-color-toolbar-plugin"
import { FontBackgroundToolbarPlugin } from "@/components/editor/plugins/toolbar/font-background-toolbar-plugin"
import { FontFamilyToolbarPlugin } from "@/components/editor/plugins/toolbar/font-family-toolbar-plugin"
import { FontFormatToolbarPlugin } from "@/components/editor/plugins/toolbar/font-format-toolbar-plugin"
import { FontSizeToolbarPlugin } from "@/components/editor/plugins/toolbar/font-size-toolbar-plugin"
import { HistoryToolbarPlugin } from "@/components/editor/plugins/toolbar/history-toolbar-plugin"
import { LinkToolbarPlugin } from "@/components/editor/plugins/toolbar/link-toolbar-plugin"
import { FloatingLinkContext } from "@/components/editor/context/floating-link-context"
import { ClickableLinkPlugin } from "@lexical/react/LexicalClickableLinkPlugin"
import { $generateHtmlFromNodes } from "@lexical/html"
import { $generateNodesFromDOM } from "@lexical/html";
import { $getRoot } from "lexical"

const editorConfig: InitialConfigType = {
    namespace: "Editor",
    theme: editorTheme,
    nodes: [
        HeadingNode,
        ParagraphNode,
        TextNode,
        QuoteNode,
        ListNode,
        ListItemNode,
        LinkNode,
        AutoLinkNode,
    ],
    onError: (error: Error) => {
        console.error(error)
    },
}

export function RichTextEditorDemo({
    value,
    htmlRef,
}: {
    value?: string;
    htmlRef?: React.MutableRefObject<string | null>;
}) {

    return (
        <div className="bg-card w-full overflow-hidden rounded-lg border" >
            <LexicalComposer initialConfig={editorConfig}>
                <TooltipProvider>
                    <FloatingLinkContext>
                        <Plugins htmlRef={htmlRef} initialValue={value} />
                    </FloatingLinkContext>
                </TooltipProvider>
            </LexicalComposer>
        </div>
    )
}
const placeholder = "Start typing..."

function Plugins({
    htmlRef,
    initialValue,
}: {
    htmlRef?: React.MutableRefObject<string | null>;
    initialValue?: string;
}) {
    const [editor] = useLexicalComposerContext();
    const [floatingAnchorElem, setFloatingAnchorElem] = useState<HTMLDivElement | null>(null);
    const onRef = (_floatingAnchorElem: HTMLDivElement) => {
        if (_floatingAnchorElem !== null) {
            setFloatingAnchorElem(_floatingAnchorElem);
        }
    };

    // Set initial HTML content
    useEffect(() => {
        if (initialValue) {
            editor.update(() => {
                const parser = new DOMParser();
                const dom = parser.parseFromString(initialValue, "text/html");
                const nodes = $generateNodesFromDOM(editor, dom);
                const root = $getRoot();
                root.clear();
                root.append(...nodes);
            });
        }
    }, [editor, initialValue]);


    // Watch for changes and write HTML to ref
    useEffect(() => {
        return editor.registerUpdateListener(({ editorState }) => {
            editorState.read(() => {
                const html = $generateHtmlFromNodes(editor, null);
                if (htmlRef) {
                    htmlRef.current = html;
                }
            });
        });
    }, [editor, htmlRef]);

    return (
        <div className="relative">
            <ToolbarPlugin>
                {() => (
                    <div className="vertical-align-middle sticky top-0 z-10 flex gap-2 flex-wrap overflow-auto border-b p-2">
                        <HistoryToolbarPlugin />
                        <ClickableLinkPlugin />
                        <LinkToolbarPlugin />
                        <ElementFormatToolbarPlugin />
                        <ClearFormattingToolbarPlugin />
                        <FontFormatToolbarPlugin format="bold" />
                        <FontFormatToolbarPlugin format="italic" />
                        <FontFormatToolbarPlugin format="underline" />
                        <FontFormatToolbarPlugin format="strikethrough" />
                        <FontSizeToolbarPlugin />
                        <BlockFormatDropDown>
                            <FormatParagraph />
                            <FormatHeading levels={["h1", "h2", "h3"]} />
                            <FormatNumberedList />
                            <FormatBulletedList />
                            <FormatCheckList />
                            <FormatQuote />
                        </BlockFormatDropDown>
                        <FontFamilyToolbarPlugin />
                        <FontColorToolbarPlugin />
                        <FontBackgroundToolbarPlugin />
                    </div>
                )}
            </ToolbarPlugin>

            <div className="relative">
                <RichTextPlugin
                    contentEditable={
                        <div>
                            <div ref={onRef}>
                                <ContentEditable
                                    placeholder={placeholder}
                                    className="ContentEditable__root relative block h-72 min-h-72 min-h-full overflow-auto px-8 py-4 focus:outline-none"
                                />
                            </div>
                        </div>
                    }
                    ErrorBoundary={LexicalErrorBoundary}
                />
                <TabIndentationPlugin />
                <ListPlugin />
                <CheckListPlugin />
            </div>
        </div>
    );
}


// 🧩 Plugin to watch editor state changes
function OnChangePlugin() {
    const [editor] = useLexicalComposerContext()

    useEffect(() => {
        return editor.registerUpdateListener(({ editorState }: any) => {
            editorState.read(() => {
                const root = editor.getRootElement()
                const selection = editor.getEditorState().read(() => {
                    return window.getSelection()?.toString()
                })
                const text = root?.textContent ?? ""
            })
        })
    }, [editor])

    return null
}
