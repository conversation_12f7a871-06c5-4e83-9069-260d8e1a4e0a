import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { X } from 'lucide-react'
import { useState } from 'react'

interface DataTableToolbarProps<TData> {
    readonly table: Table<TData>
    readonly onFilterChanged?: any
}

export function DataTableToolbar<TData>({
    table,
    onFilterChanged
}: DataTableToolbarProps<TData>) {
    const isFiltered = table.getState().columnFilters.length > 0
    const [searchValue, setSearchValue] = useState('')

    const handleSearch = (value: string) => {
        setSearchValue(value)
        onFilterChanged({ search: value }, 1)
    }

    const handleClear = () => {
        setSearchValue('')
        onFilterChanged({ search: '' }, 0)
    }

    return (
        <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center space-x-2">
                <Input
                    placeholder="Filter by name..."
                    value={searchValue}
                    onChange={(event) => handleSearch(event.target.value)}
                    className="h-8 w-[150px] lg:w-[250px]"
                />
                {isFiltered && (
                    <Button
                        variant="ghost"
                        onClick={handleClear}
                        className="h-8 px-2 lg:px-3"
                    >
                        Reset
                        <X className="ml-2 h-4 w-4" />
                    </Button>
                )}
            </div>
        </div>
    )
}
