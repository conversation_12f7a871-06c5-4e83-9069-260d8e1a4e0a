import { Main } from "@/components/layout/main";
import { BodyTypesPrimaryButtons } from "./components/body-types-primary-buttons";
import { BodyTypesTable } from "./components/body-types-table";
import { columns } from "./components/body-types-columns";

export default function BodyTypesList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Body Types List</h2>
                </div>
                <BodyTypesPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <BodyTypesTable columns={columns} />
            </div>
        </Main>
    )
}
