import { Main } from "@/components/layout/main";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { addFlirtMessageApi, getFlirtMessageDetails, getMastrLanguagesApi, updateFlirtMessageApi } from "../api";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { END_POINTS } from "@/features/members/utils/constant";

// Language mapping for display names
const LANGUAGE_NAMES: Record<string, string> = {
    en: "English",
    fr: "French",
    de: "German",
    nl: "Dutch",
    da: "Danish",
    fi: "Finnish",
    it: "Italian",
    no: "Norwegian",
    pl: "Polish",
    pt: "Portuguese",
    el: "Greek",
    es: "Spanish",
    sv: "Swedish",
    // Add more languages as needed
};

export default function AddFlirtMessage() {
    const navigate = useNavigate()
    const { data: { languages = [] } = {} } = getMastrLanguagesApi()
    const { mutateAsync: addFlirtMessageMutation } = addFlirtMessageApi();
    const { mutateAsync: updateFlirtMessageMutation } = updateFlirtMessageApi()
    const { msgId } = useParams({ strict: false });
    const { data = {} } = getFlirtMessageDetails(msgId);
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Create dynamic schema based on available languages
    const FlirtMessageSchema = useMemo(() => {
        const schemaObject: Record<string, z.ZodString> = {};

        // If languages are available from API, use them
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                const languageCode = lang.code || lang.languageCode || lang.id;
                if (languageCode) {
                    schemaObject[languageCode] = z.string().min(1, "Message is required");
                }
            });
        } else {
            // Use default languages if API doesn't provide them
            const defaultLanguages = ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'];
            defaultLanguages.forEach(lang => {
                schemaObject[lang] = z.string().min(1, "Message is required");
            });
        }

        return z.object(schemaObject);
    }, [languages]);

    type FlirtMessageFormValues = z.infer<typeof FlirtMessageSchema>;

    // Create dynamic default values
    const defaultValues = useMemo(() => {
        const defaults: Record<string, string> = {};

        // If languages are available from API, use them
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => {
                const languageCode = lang.code || lang.languageCode || lang.id;
                if (languageCode) {
                    defaults[languageCode] = "";
                }
            });
        } else {
            // Use default languages if API doesn't provide them
            const defaultLanguages = ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'];
            defaultLanguages.forEach(lang => {
                defaults[lang] = "";
            });
        }

        return defaults;
    }, [languages]);

    const form = useForm<FlirtMessageFormValues>({
        resolver: zodResolver(FlirtMessageSchema),
        defaultValues: defaultValues as FlirtMessageFormValues,
    });

    // Prefill form when data.items is available
    useEffect(() => {
        if (data.items) {
            const itemsObject = (data.items || []).reduce((acc: any, item: any) => {
                acc[item.languageCode] = item.message;
                return acc;
            }, {});
            form.reset({
                ...form.getValues(),
                ...itemsObject
            });
        }
    }, [data, form]);

    const { control, handleSubmit } = form;

    const onSubmit = async (values: FlirtMessageFormValues) => {
        setIsSubmitting(true);

        try {
            // Dynamically create items array based on form values
            const items = Object.entries(values).map(([languageCode, message]) => ({
                languageCode,
                message: message as string
            }));

            const payload = {
                items
            };

            if (typeof msgId === "string") {
                const response: any = await updateFlirtMessageMutation({
                    ...payload,
                    id: data?.id
                })
                if (response?.success) {
                    toast.success("Flirt Message has been updated!")
                    navigate({ to: END_POINTS.FLIRT_MESSAGES });
                }
            } else {
                const response: any = await addFlirtMessageMutation(payload);
                if (response?.success) {
                    toast.success("Flirt Message has been created!")
                    navigate({ to: END_POINTS.FLIRT_MESSAGES });
                }
            }
        } catch (error) {
            toast.error("An error occurred while saving the flirt message")
        } finally {
            setIsSubmitting(false);
        }
    }

    return (
        <Main>
            <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
                    Create Flirt Message
                </h1>
                <p className="text-muted-foreground">Creating a new flirt message.</p>
            </div>
            <Separator className="my-4 lg:my-3" />
            <div className="flex flex-1 flex-col space-y-2 overflow-hidden md:space-y-2 xl:flex-row lg:space-y-0 lg:space-x-12">
                <div className="flex w-full overflow-y-hidden p-1">
                    <Form {...form}>
                        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Flirt message Details</CardTitle>
                                </CardHeader>
                                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                                    {Object.keys(defaultValues).length === 0 ? (
                                        <div className="col-span-full text-center py-8 text-muted-foreground">
                                            Loading languages...
                                        </div>
                                    ) : (
                                        Object.keys(defaultValues).map((languageCode) => {
                                            const languageName = LANGUAGE_NAMES[languageCode] || languageCode.toUpperCase();

                                            return (
                                                <FormField
                                                    key={languageCode}
                                                    name={languageCode as keyof FlirtMessageFormValues}
                                                    control={control}
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel className="flex items-center gap-2">
                                                                {languageName}
                                                                <span className="text-xs text-muted-foreground">({languageCode})</span>
                                                            </FormLabel>
                                                            <FormControl>
                                                                <Input
                                                                    {...field}
                                                                    placeholder={`Enter flirt message in ${languageName}`}
                                                                    className="min-h-[40px]"
                                                                />
                                                            </FormControl>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                            );
                                        })
                                    )}
                                </CardContent>
                            </Card>

                            <div className="md:col-span-2 flex mt-4 justify-end gap-3">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => navigate({ to: END_POINTS.FLIRT_MESSAGES })}
                                    disabled={isSubmitting}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={isSubmitting || Object.keys(defaultValues).length === 0}
                                >
                                    {isSubmitting ? "Saving..." : (typeof msgId === "string" ? "Update" : "Save")}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </div>
            </div>
        </Main>
    );
} 