import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { StatusDropdown, useNotificationStatus } from '@/context/notification-status-context';
import { useResponsive } from '@/hooks/use-responsive';

interface NotificationStatusBarProps {
  className?: string;
}

export function NotificationStatusBar({ className }: NotificationStatusBarProps) {
  const { statusItems } = useNotificationStatus();
  const { windowSize } = useResponsive();

  if (windowSize.width < 1100) {
    return (
      <StatusDropdown />
    )
  }

  return (
    <div className={cn(
      "flex items-center space-x-1 px-2 py-1 rounded-md border bg-background/50 backdrop-blur-sm",
      className
    )}>
      {statusItems.map((item) => (
        <div
          key={item.id}
          className={cn(
            "flex items-center space-x-1 px-2 py-1 rounded-sm text-xs font-medium cursor-pointer transition-all duration-200 hover:scale-105",
            // item.className
          )}
        >
          <span className="whitespace-nowrap">{item.label}</span>
          <Badge
            variant="secondary"
            className="status-badge  text-xs min-w-[18px] h-4 flex items-center justify-center font-semibold shadow-sm"
          >
            {item.count}
          </Badge>
        </div>
      ))}
    </div>
  );
}
