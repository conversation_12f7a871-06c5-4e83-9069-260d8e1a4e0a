import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useState } from 'react'

interface Props<TData> {
    readonly table: Table<TData>
    readonly onFilterChanged?: any
}

const statusOptions = ['Active', 'Archived']
const domainTypeOptions = ['discreetdating.club', 'passionhub.net', 'lovequest.org']

export function DataTableToolbar<TData>({ table, onFilterChanged }: Props<TData>) {
    const isFiltered = table.getState().columnFilters.length > 0
    const [filters, setFilters] = useState<{ search: string; status: string; domains: string }>({ search: '', status: '', domains: '' })
    const [hasSearched, setHasSearched] = useState(false)
    const [initialFilters, setInitialFilters] = useState(filters)

    const handleFilterChange = (key: 'search' | 'status' | 'domains', value: string | string[]) => {
        if (key === 'domains' && Array.isArray(value)) value = value.filter(Boolean).join(',')
        setFilters(prev => ({ ...prev, [key]: value as string }))
    }

    const handleSearch = () => {
        if (filters.search) table.getColumn('title')?.setFilterValue(filters.search)
        if (filters.status) table.getColumn('status')?.setFilterValue(filters.status)
        if (filters.domains) {
            const col = table.getColumn('domain')
            if (col) {
                col.columnDef.filterFn = (row: any, _id: string, vals: string[]) => vals.includes(row.original.domain)
                col.setFilterValue(filters.domains.split(',').filter(Boolean))
            }
        }
        setHasSearched(true); setInitialFilters({ ...filters }); onFilterChanged?.(filters, 1)
    }

    const handleReset = () => {
        table.resetColumnFilters(); const f = { search: '', status: '', domains: '' }
        setFilters(f); setInitialFilters(f); setHasSearched(false); onFilterChanged?.(f, 0)
    }

    const hasFilterChanges = Boolean(filters.search !== initialFilters.search || filters.status !== initialFilters.status || filters.domains !== initialFilters.domains)

    return (
        <div className='flex items-center justify-between'>
            <div className='flex flex-1 items-center gap-4 flex-wrap'>
                <Input placeholder='Search by title' value={filters.search} onChange={(e) => handleFilterChange('search', e.target.value)} className='h-9 w-[250px]' />
                <FilterSelect value={filters.status || undefined} placeholder='Select status' options={statusOptions} onChange={(v) => handleFilterChange('status', v || '')} className='w-[200px] bg-card' />
                <Popover>
                    <PopoverTrigger asChild>
                        <div className={cn("flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer", !filters.domains && 'text-muted-foreground')}>
                            {!filters.domains && <span>Select domain</span>}
                            {filters.domains && filters.domains.split(',').filter(Boolean).slice(0, 2).map((val: string) => (
                                <div className="flex gap-1 items-center bg-muted rounded p-1" key={val}>
                                    <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">{val}</Badge>
                                    <X className="h-3 w-3 cursor-pointer" onClick={(e) => { e.stopPropagation(); const arr = filters.domains.split(',').filter(Boolean).filter((v: string) => v !== val); handleFilterChange('domains', arr) }} />
                                </div>
                            ))}
                            {filters.domains && filters.domains.split(',').filter(Boolean).length > 2 && (<span className="ml-2 text-muted-foreground">...</span>)}
                        </div>
                    </PopoverTrigger>
                    <PopoverContent className='w-full p-0'>
                        <Command>
                            <CommandInput placeholder='Search domain...' />
                            <CommandEmpty>No domain found.</CommandEmpty>
                            <CommandGroup>
                                {(() => {
                                    const selectedArr = filters.domains.split(',').filter(Boolean)
                                    const sorted = [...domainTypeOptions.filter(t => selectedArr.includes(t)), ...domainTypeOptions.filter(t => !selectedArr.includes(t))]
                                    return sorted.map((type) => (
                                        <CommandItem key={type} onSelect={() => { const arr = filters.domains.split(',').filter(Boolean); const selected = arr.includes(type) ? arr.filter((v: string) => v !== type) : [...arr, type]; handleFilterChange('domains', selected) }} className='cursor-pointer'>
                                            <span>{type}</span>
                                            {selectedArr.includes(type) && (<span className='ml-auto text-primary'>✓</span>)}
                                        </CommandItem>
                                    ))
                                })()}
                            </CommandGroup>
                        </Command>
                    </PopoverContent>
                </Popover>
                <Button onClick={handleSearch} className='h-8 px-3' disabled={!hasFilterChanges}>Search</Button>
                {(isFiltered || hasSearched) && (<Button variant='outline' onClick={handleReset} className='h-8 px-2 lg:px-3'>Reset</Button>)}
            </div>
        </div>
    )
}


