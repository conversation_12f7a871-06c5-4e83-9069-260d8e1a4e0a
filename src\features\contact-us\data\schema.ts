import { z } from 'zod'

const adminTypeSchema = z.union([
  z.literal('Admin'),
  z.literal('Super Admin'),
  z.literal('Moderator'),
])
export type AdminType = z.infer<typeof adminTypeSchema>

const adminLoginActivitySchema = z.object({
  id: z.string(),
  adminUsername: z.string(),
  adminType: adminTypeSchema,
  loginIP: z.string(),
  country: z.string(),
  loginTime: z.coerce.date(),
})
export type AdminLoginActivity = z.infer<typeof adminLoginActivitySchema>

export const adminLoginActivityListSchema = z.array(adminLoginActivitySchema)
