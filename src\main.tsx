import "./index.css";
import ReactDOM from "react-dom/client";
import { useAuthStore } from "@/stores/authStore";
import { handleServerError } from "@/utils/handle-server-error";
import { QueryCache, QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { RouterProvider, createRouter } from "@tanstack/react-router";
import { AxiosError } from "axios";
import { toast } from "sonner";
import { FontProvider } from "./context/font-context";
import { ThemeProvider } from "./context/theme-context";
import { NotificationStatusProvider } from "./context/notification-status-context";
// Generated Routes
import { routeTree } from "./routeTree.gen";
import { TooltipProvider } from "./components/ui/tooltip";
import { END_POINTS } from "./features/members/utils/constant";
import { Spinner } from "./components/Spinner";
import redsoftFaviconUrl from "@/assets/redsoft-favicon.ico";
import { useIdleTimer } from 'react-idle-timer'
import { useEffect, useState } from "react";

function setFavicon(href: string) {
  const existingIcons = Array.from(
    document.querySelectorAll<HTMLLinkElement>('link[rel*="icon"]')
  );

  if (existingIcons.length === 0) {
    const link = document.createElement("link");
    link.rel = "icon";
    link.type = "image/x-icon";
    link.href = href;
    document.head.appendChild(link);
    return;
  }

  existingIcons.forEach((link) => {
    link.rel = "icon";
    link.type = "image/x-icon";
    link.removeAttribute("sizes");
    link.removeAttribute("media");
    link.href = href;
  });
}

setFavicon(redsoftFaviconUrl);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // eslint-disable-next-line no-console
        if (import.meta.env.DEV) console.log({ failureCount, error });

        if (failureCount >= 0 && import.meta.env.DEV) return false;
        if (failureCount > 3 && import.meta.env.PROD) return false;

        return !(
          error instanceof AxiosError &&
          [401, 403].includes(error.response?.status ?? 0)
        );
      },
      refetchOnWindowFocus: import.meta.env.PROD,
      staleTime: 10 * 1000, // 10s
    },
    mutations: {
      onError: (error) => {
        handleServerError(error);

        if (error instanceof AxiosError) {
          if (error.response?.status === 304) {
            toast.error("Content not modified!");
          }
        }
      },
    },
  },
  queryCache: new QueryCache({
    onError: (error) => {
      if (error instanceof AxiosError) {
        if (error.response?.status === 401) {
          toast.error("Session expired!");
          useAuthStore.getState().auth.reset();
          // const redirect = `${router.history.location.href}`;
          // router.navigate({ to: END_POINTS.SIGN_IN, search: { redirect } });
          router.navigate({ to: END_POINTS.SIGN_IN });
        }
        if (error.response?.status === 500) {
          toast.error("Internal Server Error!");
          window.location.href = '/not-found';
        }
        if (error.response?.status === 403) {
          // router.navigate("/forbidden", { replace: true });
        }
      }
    },
  }),
});

// Create a new router instance
const router = createRouter({
  routeTree,
  context: { queryClient },
  defaultPreload: "intent",
  defaultPreloadStaleTime: 0,
  basepath: import.meta.env.VITE_BASE_URL,
});

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

// App component to handle hooks
function App() {
  const [isOpen, setIsOpen] = useState(false);
  const [remaining, setRemaining] = useState(0);
  const { reset } = useAuthStore((state) => state.auth)


  const onAction = (_event?: Event, idleTimer?: any) => {
    if (idleTimer?.isPrompted()) {
      idleTimer.activate()
    }
  }

  const onActive = (_event?: Event, idleTimer?: any) => {
    if (idleTimer?.isPrompted()) {
      setIsOpen(false)
    }
  }

  const onPrompt = () => {
    setIsOpen(true)
  }

  const onIdle = () => {
    setIsOpen(false)
    handleLogout()
  }

  const handleLogout = async () => {
    localStorage.removeItem('rememberedEmail')
    localStorage.removeItem('rememberMe')

    reset()
    window.location.href = location.pathname
  }

  const { getRemainingTime } = useIdleTimer({
    timeout: 1000 * 60 * 30,
    promptBeforeIdle: 1000 * 60 * 28,
    onAction,
    onActive,
    onPrompt,
    onIdle
  })

  useEffect(() => {
    const interval = setInterval(() => {
      setRemaining(Math.ceil(getRemainingTime() / ((remaining * 0 + 1) * 1)));
    }, 1);
    return () => {
      clearInterval(interval);
    };
  }, [getRemainingTime, remaining]);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
        <FontProvider>
          <NotificationStatusProvider>
            <TooltipProvider >
              <Spinner />
              <RouterProvider router={router} />
            </TooltipProvider>
          </NotificationStatusProvider>
        </FontProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

// Render the app
const rootElement = document.getElementById("root")!;
if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(<App />);
}