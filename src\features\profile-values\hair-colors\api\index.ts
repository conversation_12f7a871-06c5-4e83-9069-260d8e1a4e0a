import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getHairColorsApi = (params: any = {}) => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.HAIR_COLORS, { params })).data ?? {},
    queryKey: ["hair-colors-list"],
});

export const addHairColorsApi = () => useMutation({
    mutationFn: async (data: any) => (await apiClient.post(API_ENDPOINTS.HAIR_COLORS, data)).data,
});

export const updateHairColorsApi = () => useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => (await apiClient.put(`${API_ENDPOINTS.HAIR_COLORS}/${id}`, data)).data,
});

export const deleteHairColorsApi = () => useMutation({
    mutationFn: async (id: string) => (await apiClient.delete(`${API_ENDPOINTS.HAIR_COLORS}/${id}`)).data,
});

export const getHairColorsDetails = (id: string) => useQuery({
    queryFn: async () => (await apiClient.get(`${API_ENDPOINTS.HAIR_COLORS}/${id}`)).data ?? {},
    queryKey: ["hair-colors-details", id],
    enabled: !!id,
});

export const getMasterLanguagesApi = () => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES)).data ?? {},
    queryKey: ["master-languages"],
});
