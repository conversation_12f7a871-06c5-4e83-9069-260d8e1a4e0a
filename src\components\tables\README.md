# Common Data Table Components

This directory contains reusable data table components built on top of TanStack Table (React Table) and the project's UI library. These components provide a consistent and feature-rich table experience across the application.

## Components

### `DataTable`

The main reusable data table component that provides:

- Sorting and filtering
- Pagination
- Row selection (optional)
- Responsive design
- Theme support
- Customizable toolbar

### `DataTableToolbar`

A reusable toolbar component that includes:

- Search input
- Filter controls
- View options
- Reset filters button
- Custom children support

### `DataTablePagination`

A pagination component with:

- Page navigation
- Rows per page selection
- Selected rows count
- First/last page buttons

## Usage

### Basic Usage

```tsx
import { DataTable } from "@/components/tables";
import { ColumnDef } from "@tanstack/react-table";

interface User {
  id: string;
  name: string;
  email: string;
}

const columns: ColumnDef<User>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
];

const data: User[] = [
  { id: "1", name: "<PERSON>", email: "<EMAIL>" },
  { id: "2", name: "<PERSON>", email: "<EMAIL>" },
];

export function UsersTable() {
  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="name"
      searchPlaceholder="Filter users..."
    />
  );
}
```

### Advanced Usage with Filters

```tsx
import { DataTable, DataTableFilter } from "@/components/tables";

const filters: DataTableFilter[] = [
  {
    column: "status",
    title: "Status",
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
    ],
  },
];

export function AdvancedUsersTable() {
  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="name"
      searchPlaceholder="Filter users..."
      filters={filters}
      enableRowSelection={true}
      pageSize={20}
    />
  );
}
```

### Dashboard Table Usage

```tsx
import {
  DashboardTable,
  countryDomainColumns,
} from "@/features/dashboard/components/dashboard-table";

export function CountryStatsCard() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Country Statistics</CardTitle>
      </CardHeader>
      <CardContent>
        <DashboardTable
          data={countryData}
          columns={countryDomainColumns}
          title="Country Statistics"
          showToolbar={false}
          showPagination={false}
        />
      </CardContent>
    </Card>
  );
}
```

## Props

### DataTable Props

| Prop                 | Type                | Default       | Description                         |
| -------------------- | ------------------- | ------------- | ----------------------------------- |
| `columns`            | `ColumnDef[]`       | -             | Table column definitions            |
| `data`               | `TData[]`           | -             | Table data                          |
| `searchKey`          | `string`            | -             | Column key for search functionality |
| `searchPlaceholder`  | `string`            | `"Filter..."` | Search input placeholder            |
| `filters`            | `DataTableFilter[]` | `[]`          | Filter configurations               |
| `enableRowSelection` | `boolean`           | `false`       | Enable row selection                |
| `enableSorting`      | `boolean`           | `true`        | Enable column sorting               |
| `enableFiltering`    | `boolean`           | `true`        | Enable filtering                    |
| `enablePagination`   | `boolean`           | `true`        | Enable pagination                   |
| `showToolbar`        | `boolean`           | `true`        | Show toolbar                        |
| `showViewOptions`    | `boolean`           | `true`        | Show view options in toolbar        |
| `showPagination`     | `boolean`           | `true`        | Show pagination controls            |
| `pageSize`           | `number`            | `10`          | Default page size                   |
| `className`          | `string`            | -             | Additional CSS classes              |
| `toolbarChildren`    | `ReactNode`         | -             | Custom toolbar content              |

## Best Practices

1. **Column Definitions**: Use the `DataTableColumnHeader` component for sortable headers
2. **Responsive Design**:
   - Use the `meta.className` property to set column widths and responsive behavior
   - Implement responsive text sizes: `text-xs sm:text-sm` for mobile-first approach
   - Use `truncate` class for long text content
   - Set minimum table width for horizontal scroll on mobile: `min-w-[600px] sm:min-w-0`
3. **Performance**: For large datasets, consider implementing server-side pagination and filtering
4. **Accessibility**: Ensure proper ARIA labels and keyboard navigation
5. **Theming**: The components automatically adapt to light/dark themes
6. **Mobile Optimization**:
   - Reduce cell padding on mobile: `p-2 sm:p-4`
   - Use smaller text on mobile: `text-xs sm:text-sm`
   - Implement horizontal scroll for wide tables
   - Stack toolbar elements vertically on mobile

## Examples

See the dashboard implementation in `src/features/dashboard/components/dashboard-table.tsx` for complete examples of how to use these components in different scenarios.
