import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";

export function ReligionsPrimaryButtons() {
    const navigate = useNavigate();
    return (
        <div className="flex items-center space-x-2">
            <Button onClick={() => navigate({ to: END_POINTS.RELIGIONS + "/add" })} className="flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span>Add Religion</span>
            </Button>
        </div>
    );
}
