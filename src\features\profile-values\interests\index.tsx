import { Main } from "@/components/layout/main";
import { InterestsPrimaryButtons } from "./components/interests-primary-buttons";
import { InterestsTable } from "./components/interests-table";
import { columns } from "./components/interests-columns";

export default function InterestsList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Interests List</h2>
                </div>
                <InterestsPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <InterestsTable columns={columns} />
            </div>
        </Main>
    )
}
