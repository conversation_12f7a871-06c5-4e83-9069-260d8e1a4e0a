import { Main } from '@/components/layout/main'
// import { OffersPrimaryButtons } from './components/offers-primary-buttons'
import { OffersTable } from './components/offers-table'
import { columns } from './components/offers-columns'

export default function List() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Affiliate Offers</h2>
                </div>
                {/* <OffersPrimaryButtons /> */}
            </div>
            <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
                <OffersTable columns={columns} />
            </div>
        </Main>
    )
}


