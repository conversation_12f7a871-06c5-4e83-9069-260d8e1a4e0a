export default function ProfileStats() {
  const stats = [
    {
      title: 'Current Month',
      value: '€350.4',
      subtitle: 'Sent Messages: €350',
      subtitle2: 'Rum Messages: €50.4',
      icon: '€'
    },
    {
      title: 'Last Month',
      value: '€350.4',
      subtitle: 'Sent Messages: €350',
      subtitle2: 'Rum Messages: €50.4',
      icon: '$'
    },
    {
      title: 'Year Income',
      value: '€350.4',
      subtitle: 'Sent Messages: €300',
      subtitle2: 'Rum Messages: €50.4',
      icon: '$'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {stats.map((stat, index) => (
        <div key={index} className="bg-white rounded-lg border p-4">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-lg font-bold text-gray-600">{stat.icon}</span>
            <span className="text-sm text-gray-600">{stat.title}</span>
          </div>

          <div className="space-y-1">
            <h3 className="text-xl font-bold text-gray-900">{stat.value}</h3>
            <div className="space-y-0.5 text-xs text-gray-500">
              <p>{stat.subtitle}</p>
              <p>{stat.subtitle2}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
