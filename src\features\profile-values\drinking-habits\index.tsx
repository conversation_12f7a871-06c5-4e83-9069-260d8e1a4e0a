import { Main } from "@/components/layout/main";
import { DrinkingHabitsPrimaryButtons } from "./components/drinking-habits-primary-buttons";
import { DrinkingHabitsTable } from "./components/drinking-habits-table";
import { columns } from "./components/drinking-habits-columns";

export default function DrinkingHabitsList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Drinking Habits List</h2>
                </div>
                <DrinkingHabitsPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <DrinkingHabitsTable columns={columns} />
            </div>
        </Main>
    )
}
