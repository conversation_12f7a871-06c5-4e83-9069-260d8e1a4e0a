import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import { Row } from '@tanstack/react-table'
import { But<PERSON> } from '@/components/ui/button'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Affiliate } from '../types'
import {
    Eye,
    Edit,
    BarChart3,
    Euro,
    Users,
    FileText,
    User,
    MoreHorizontal
} from "lucide-react"

interface DataTableRowActionsProps {
    readonly row: Row<Affiliate>
}

export function DataTableRowActions({ row }: DataTableRowActionsProps) {
    const handleAction = (action: string) => {
        console.log(`Action ${action} for affiliate:`, row.original)
        // Implement actual actions here
    }

    return (
        <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
                <Button
                    variant='ghost'
                    className='data-[state=open]:bg-muted flex h-8 w-8 p-0 cursor-pointer'
                >
                    <DotsHorizontalIcon className='h-4 w-4' />
                    <span className='sr-only'>Open menu</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end' className='w-[200px]'>
                <DropdownMenuItem onClick={() => handleAction('view')}>
                    <Eye className="mr-2 h-4 w-4" />
                    View
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAction('edit')}>
                    <Edit className="mr-2 h-4 w-4" />
                    Update
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAction('statistics')}>
                    <BarChart3 className="mr-2 h-4 w-4" />
                    Transaction history WL
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAction('revenue')}>
                    <Euro className="mr-2 h-4 w-4" />
                    Add Payment WL
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAction('customers')}>
                    <Users className="mr-2 h-4 w-4" />
                    Team
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAction('reports')}>
                    <FileText className="mr-2 h-4 w-4" />
                    Performances
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAction('profile')}>
                    <User className="mr-2 h-4 w-4" />
                    Client
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAction('analytics')}>
                    <BarChart3 className="mr-2 h-4 w-4" />
                    Transaction history of other Commission
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAction('payments')}>
                    <Euro className="mr-2 h-4 w-4" />
                    Add Offer Payments
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
