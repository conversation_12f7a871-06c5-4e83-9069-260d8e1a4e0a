import { UserProfileCard } from "./user-profile-card";
import { AffiliateSelector } from "./affiliate-selector";
import { NotesSection } from "./notes-section";
import { PersonalInfoSection } from "./personal-info-section";
import { UserPanelProps } from "./types";

export function UserPanel({ 
  user, 
  notes, 
  personalInfo, 
  onAddNote, 
  onAffiliateChange 
}: UserPanelProps) {
  return (
    <div className="flex flex-col gap-[16px]">
      <UserProfileCard user={user} />
      
      <AffiliateSelector 
        isAffiliate={user.isAffiliate}
        onChange={onAffiliateChange}
      />
      
      <NotesSection 
        notes={notes}
        onAddNote={onAddNote}
      />
      
      <PersonalInfoSection personalInfo={personalInfo} />
    </div>
  );
}
