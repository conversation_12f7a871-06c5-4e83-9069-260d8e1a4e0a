import { useState } from 'react'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
// import { SearchIcon } from 'lucide-react'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { Input } from '@/components/ui/input'
import { CalendarIcon } from 'lucide-react'
import { format } from 'date-fns'

interface DataTableToolbarProps<TData> {
  readonly table: Table<TData>
}

const domainOptions = [
  'uforpls.female.deso',
  'example.com',
  'testdomain.net',
  'sample.org',
  'demo.site',
]

const countryOptions = [
  'USA',
  'UK',
  'Canada',
  'Australia',
  'Germany',
  'France',
  'Spain',
  'Italy',
  'Japan',
  'India',
]

const yesterdayOptions = [
  'Yesterday',
  'Today',
  'Last 7 days',
  'Last 30 days',
]

export function DataTableToolbar<TData>({
  table,
}: DataTableToolbarProps<TData>) {
  const [filters, setFilters] = useState({
    domain: undefined as string | undefined,
    country: undefined as string | undefined,
    yesterday: undefined as string | undefined,
    fromDate: undefined as Date | undefined,
    toDate: undefined as Date | undefined,
  })

  const [hasSearched, setHasSearched] = useState(false)
  const [fromDateOpen, setFromDateOpen] = useState(false)
  const [toDateOpen, setToDateOpen] = useState(false)

  const handleFilterChange = (
    key: 'domain' | 'country' | 'yesterday' | 'fromDate' | 'toDate',
    value: string | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  const handleDateChange = (
    key: 'fromDate' | 'toDate',
    date: Date | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [key]: date }))
  }

  const handleSearch = () => {
    // Apply filters to table
    if (filters.domain) {
      table.getColumn('domain')?.setFilterValue(filters.domain)
    }
    if (filters.country) {
      table.getColumn('country')?.setFilterValue(filters.country)
    }
    setHasSearched(true)
  }

  const handleReset = () => {
    setFilters({
      domain: undefined,
      country: undefined,
      yesterday: undefined,
      fromDate: undefined,
      toDate: undefined,
    })
    table.resetColumnFilters()
    setHasSearched(false)
  }


  const isFiltered = table.getState().columnFilters.length > 0
  // const isFiltered = Object.values(filters).some(value => value !== undefined)

  const hasActiveFilters = Boolean(
    (filters.domain && filters.domain.length > 0) ||
    (filters.country && filters.country.length > 0) ||
    (filters.yesterday && filters.yesterday.length > 0) ||
    (filters.fromDate) ||
    (filters.toDate)
  )

  return (
    <div className='flex flex-col gap-4'>
      {/* First row - 3 filter dropdowns */}
      <div className='flex flex-wrap gap-4'>
        <FilterSelect
          value={filters.domain}
          placeholder='Select Domain'
          options={domainOptions}
          onChange={(value) => handleFilterChange('domain', value)}
          className='w-[200px] bg-card'
        />

        <FilterSelect
          value={filters.country}
          placeholder='Select Country'
          options={countryOptions}
          onChange={(value) => handleFilterChange('country', value)}
          className='w-[200px] bg-card'
        />

        <FilterSelect
          value={filters.yesterday}
          placeholder='Yesterday'
          options={yesterdayOptions}
          onChange={(value) => handleFilterChange('yesterday', value)}
          className='w-[200px] bg-card'
        />

        <div className='flex items-center gap-2'>
          <Popover open={fromDateOpen} onOpenChange={setFromDateOpen}>
            <PopoverTrigger asChild>
              <div className="relative w-[200px] bg-card">
                <Input
                  readOnly
                  value={filters.fromDate ? format(filters.fromDate, 'yyyy-MM-dd') : ''}
                  placeholder="From Date"
                  className="pr-10 cursor-pointer h-9"
                  onClick={() => setFromDateOpen(true)}
                />
                <CalendarIcon
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"
                  width={20}
                  height={20}
                />
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <div className="relative">
                <Calendar
                  mode="single"
                  selected={filters.fromDate}
                  className="rounded-md border shadow-sm"
                  captionLayout="dropdown"
                  disabled={(date) => filters.toDate ? date > filters.toDate : false}
                  onSelect={(date) => {
                    if (date) {
                      handleDateChange('fromDate', date)
                      // If toDate is earlier than new fromDate, clear toDate
                      if (filters.toDate && date > filters.toDate) {
                        handleDateChange('toDate', undefined)
                      }
                      setFromDateOpen(false)
                    }
                  }}
                />
              </div>
            </PopoverContent>
          </Popover>

          <Popover open={toDateOpen} onOpenChange={setToDateOpen}>
            <PopoverTrigger asChild>
              <div className="relative w-[200px] bg-card">
                <Input
                  readOnly
                  value={filters.toDate ? format(filters.toDate, 'yyyy-MM-dd') : ''}
                  placeholder="To Date"
                  className="pr-10 cursor-pointer h-9"
                  onClick={() => setToDateOpen(true)}
                />
                <CalendarIcon
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"
                  width={20}
                  height={20}
                />
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <div className="relative">
                <Calendar
                  mode="single"
                  selected={filters.toDate}
                  className="rounded-md border shadow-sm"
                  captionLayout="dropdown"
                  disabled={(date) => filters.fromDate ? date < filters.fromDate : false}
                  onSelect={(date) => {
                    if (!filters.fromDate || !date || date >= filters.fromDate) {
                      handleDateChange('toDate', date)
                      setToDateOpen(false)
                    }
                  }}
                />
              </div>
            </PopoverContent>
          </Popover>
        </div>

        <Button onClick={handleSearch} disabled={!hasActiveFilters} >
          {/* <SearchIcon className='mr-2 h-4 w-4' /> */}
          Search
        </Button>

        {(isFiltered || hasSearched) && (
          <Button variant='outline' onClick={handleReset}>
            Reset
          </Button>
        )}
      </div>
    </div >
  )
}
