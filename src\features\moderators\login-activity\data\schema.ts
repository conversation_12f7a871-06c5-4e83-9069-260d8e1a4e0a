import { z } from 'zod'

const moderatorLoginActivitySchema = z.object({
  id: z.string(),
  moderator: z.string(),
  login: z.coerce.date(),
  logout: z.coerce.date(),
  loginSession: z.string(),
  lastActivityTime: z.coerce.date(),
  ipCountry: z.string(),
  deviceInfo: z.string(),
  totalMessageSent: z.number(),
})

export type ModeratorLoginActivity = z.infer<typeof moderatorLoginActivitySchema>

export const moderatorLoginActivityListSchema = z.array(moderatorLoginActivitySchema)
