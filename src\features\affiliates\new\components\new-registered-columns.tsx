import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import LongText from '@/components/long-text'
import { DataTableColumnHeader } from '../../components/data-table-column-header'

export type NewAffiliate = {
    serialNumber: number
    name: string
    country: string
    city: string
    email: string
    skype?: string
    phone?: string
    registerIp?: string
}

export const columns: ColumnDef<NewAffiliate>[] = [
    {
        accessorKey: 'serialNumber',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='#' />
        ),
        cell: ({ row }) => (
            <div className='w-8'>{row.getValue('serialNumber')}</div>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
                'w-8'
            ),
        },
        enableHiding: false,
    },
    {
        accessorKey: 'name',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Name' />
        ),
        cell: ({ row }) => (
            <LongText className='max-w-48'>{row.getValue('name')}</LongText>
        ),
        meta: { className: cn('bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted') },
    },
    {
        accessorKey: 'country',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Country' />
        ),
        cell: ({ row }) => <div>{row.getValue('country')}</div>,
        meta: { className: cn('bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted') },
    },
    {
        accessorKey: 'city',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='City' />
        ),
        cell: ({ row }) => <div>{row.getValue('city')}</div>,
        meta: { className: cn('bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted') },
    },
    {
        accessorKey: 'email',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Email Address' />
        ),
        cell: ({ row }) => (
            <div className='text-blue-600 cursor-pointer hover:underline'>{row.getValue('email')}</div>
        ),
        meta: { className: cn('bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted') },
    },
    {
        accessorKey: 'skype',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Skype' />
        ),
        cell: ({ row }) => <div>{row.getValue('skype') || '-'}</div>,
        meta: { className: cn('bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted') },
    },
    {
        accessorKey: 'phone',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Phone Number' />
        ),
        cell: ({ row }) => <div>{row.getValue('phone') || '-'}</div>,
        meta: { className: cn('bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted') },
    },
    {
        accessorKey: 'registerIp',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Register IP' />
        ),
        cell: ({ row }) => <div>{row.getValue('registerIp') || '-'}</div>,
        meta: { className: cn('bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted') },
    },
]
