import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import LongText from '@/components/long-text'
import { toTitleCase } from '@/features/members/utils/utilities'
import { DataTableColumnHeader } from '@/components/data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'

export const columns: ColumnDef<any>[] = [
    {
        accessorKey: 'serialNumber',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='#' />
        ),
        cell: ({ row }) => (
            <div className='w-8'>{row.getValue('serialNumber')}</div>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
                'w-8'
            ),
        },
        enableHiding: false,
    },
    {
        accessorKey: 'message',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Username' />
        ),
        cell: ({ row }) => {

            return (
                <LongText className='max-w-36'>{row.getValue('message')}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        accessorKey: 'type',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Username' />
        ),
        cell: ({ row }) => {

            return (
                <LongText className='max-w-36'>{row.getValue('type') || "N/A"}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        accessorKey: 'preference',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Preference' />
        ),
        cell: ({ row }) => {
            return (
                <LongText className='max-w-36'>{row.getValue('preference') || "N/A"}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        accessorKey: 'order',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Order' />
        ),
        cell: ({ row }) => {

            return (
                <LongText className='max-w-36'>{row.getValue('order')}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        id: 'actions',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Action' />
        ),
        enableSorting: false,
        cell: DataTableRowActions,
        meta: { className: 'w-16' },
    },
]
