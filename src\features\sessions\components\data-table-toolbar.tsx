import { FilterSelect } from "@/components/select-dropdown-popover";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useState } from "react";

const domainOptions = [
  "Fashion",
  "Fitness",
  "Adult",
  "Beauty",
  "Travel",
  "Other",
];

const sessions = [
  { label: "Live Active Chat", count: 5, className: "border border-gray-300 bg-gray-100" },
  { label: "Hold Messages", count: 5, className: "border border-green-400 bg-green-50" },
  { label: "Push Messages", count: 5, className: "border border-purple-400 bg-purple-50" },
];

export function DataTableToolbar() {
  const [filters, setFilters] = useState({
    domain: undefined as string | undefined,
    country: undefined as string | undefined,
    ageRange: undefined as string | undefined,
    search: undefined as string | undefined,
  });

  const handleFilterChange = (
    key: "domain" | "country" | "ageRange" | "search",
    value: string | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  return (
    <div className="flex justify-between w-full items-center">
      <div className="flex gap-4 flex-wrap">
        <div className="flex gap-4">
          {sessions.map((session) => (
            <Card
              key={session.label}
              className={`rounded-md ${session.className} p-2.5`}
            >
              <CardContent className="flex gap-2 items-center">
                <span className="text-sm font-medium">{session.label}</span>
                <span className="text-xl font-bold">
                  {String(session.count).padStart(2, "0")}
                </span>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
      {/* <div className="flex gap-4 flex-wrap">
        <Input
          placeholder="Search by name"
          value={filters.search}
          onChange={(event) => handleFilterChange("search", event.target.value)}
          className="h-9 w-[250px]"
        />
        <FilterSelect
          value={filters.domain}
          placeholder="Select Filter"
          options={domainOptions}
          onChange={(value) => handleFilterChange("domain", value)}
        />
      </div> */}
    </div>
  );
}
