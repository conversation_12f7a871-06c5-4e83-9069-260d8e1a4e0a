import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { LineC<PERSON>, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from 'recharts'
import { IconDotsVertical } from '@tabler/icons-react'

const data = [
  { month: 'Jan', sentMessages: 1125, receivedMessages: 1135, rumMessages: 1135, sentTriggers: 1125 },
  { month: 'Feb', sentMessages: 1200, receivedMessages: 1180, rumMessages: 1150, sentTriggers: 1100 },
  { month: 'Mar', sentMessages: 1100, receivedMessages: 1220, rumMessages: 1180, sentTriggers: 1150 },
  { month: 'Apr', sentMessages: 1050, receivedMessages: 1100, rumMessages: 1120, sentTriggers: 1080 },
  { month: 'May', sentMessages: 980, receivedMessages: 1050, rumMessages: 1080, sentTriggers: 1020 },
  { month: 'Jun', sentMessages: 1150, receivedMessages: 1180, rumMessages: 1200, sentTriggers: 1160 },
  { month: 'Jul', sentMessages: 1250, receivedMessages: 1220, rumMessages: 1180, sentTriggers: 1200 },
  { month: 'Aug', sentMessages: 1180, receivedMessages: 1250, rumMessages: 1220, sentTriggers: 1180 },
  { month: 'Sep', sentMessages: 1120, receivedMessages: 1180, rumMessages: 1200, sentTriggers: 1150 },
  { month: 'Oct', sentMessages: 1200, receivedMessages: 1150, rumMessages: 1180, sentTriggers: 1170 },
  { month: 'Nov', sentMessages: 1180, receivedMessages: 1200, rumMessages: 1160, sentTriggers: 1190 },
  { month: 'Dec', sentMessages: 1220, receivedMessages: 1180, rumMessages: 1200, sentTriggers: 1210 },
]

export default function ActivityChart() {
  return (
    <div className="bg-white rounded-lg border overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Activity</h3>
          <div className="flex items-center gap-4">
            <Select defaultValue="january-2024">
              <SelectTrigger className="w-36 h-9">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="january-2024">January 2024</SelectItem>
                <SelectItem value="february-2024">February 2024</SelectItem>
                <SelectItem value="march-2024">March 2024</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>Start Date</span>
              <span>End Date</span>
            </div>
            <Button variant="ghost" size="icon">
              <IconDotsVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="p-4">
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="1 1" stroke="#e0e0e0" />
              <XAxis
                dataKey="month"
                axisLine={false}
                tickLine={false}
                fontSize={12}
                tick={{ fill: '#666' }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                fontSize={12}
                tick={{ fill: '#666' }}
                domain={[0, 800]}
              />
              {/* All lines in black with thick strokes */}
              <Line
                type="monotone"
                dataKey="sentMessages"
                stroke="#000"
                strokeWidth={2}
                dot={false}
              />
              <Line
                type="monotone"
                dataKey="receivedMessages"
                stroke="#000"
                strokeWidth={2}
                dot={false}
              />
              <Line
                type="monotone"
                dataKey="rumMessages"
                stroke="#000"
                strokeWidth={2}
                dot={false}
              />
              <Line
                type="monotone"
                dataKey="sentTriggers"
                stroke="#000"
                strokeWidth={2}
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Legend */}
        <div className="grid grid-cols-4 gap-4 mt-4 pt-4 border-t border-gray-200">
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <div className="w-2 h-2 bg-black rounded-full"></div>
              <span className="text-xs text-gray-600">Sent Messages</span>
            </div>
            <p className="text-sm font-semibold">1135</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <div className="w-2 h-2 bg-black rounded-full"></div>
              <span className="text-xs text-gray-600">Received Messages</span>
            </div>
            <p className="text-sm font-semibold">1135</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <div className="w-2 h-2 bg-black rounded-full"></div>
              <span className="text-xs text-gray-600">Rum Messages</span>
            </div>
            <p className="text-sm font-semibold">1135</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <div className="w-2 h-2 bg-black rounded-full"></div>
              <span className="text-xs text-gray-600">Sent Triggers</span>
            </div>
            <p className="text-sm font-semibold">1135</p>
          </div>
        </div>
      </div>
    </div>
  )
}
