import { z } from "zod";

const anouncementTypeOf = z.union([
    z.literal("active"),
    z.literal("inactive"),
]);

export type AnnouncementNew = z.infer<typeof anouncementTypeOf>;

export const announcementSchema = z.object({
    id: z.string(),
    serialNumber: z.number(),
    moderator: z.string(),
    group: z.string(),
    status: anouncementTypeOf,
    createdAt: z.coerce.date(),
    updatedAt: z.coerce.date().optional(),
});

export type ChatGroup = z.infer<typeof announcementSchema>;
