import { AuthenticatedLayout } from "@/components/layout/authenticated-layout";
import { END_POINTS } from "@/features/members/utils/constant";
import { useAuthStore } from "@/stores/authStore";
import { createFileRoute, redirect } from "@tanstack/react-router";

export const Route = createFileRoute("/_authenticated")({
  beforeLoad: ({ location }) => {
    const { accessToken } = useAuthStore.getState().auth;
    if (!accessToken) {
      throw redirect({
        to: END_POINTS.SIGN_IN,
        search: {
          redirect: location.href,
        },
        replace: true,
      });
    }
  },
  component: AuthenticatedLayout,
});
