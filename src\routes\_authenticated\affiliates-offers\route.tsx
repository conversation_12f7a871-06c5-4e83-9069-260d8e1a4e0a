import { END_POINTS } from '@/features/members/utils/constant'
import CommonLayout from '@/components/layout/shared-common-layout'
import { createFileRoute } from '@tanstack/react-router'
import { roleGuards } from '@/utils/route-protection'

export const Route = createFileRoute<any>(`/_authenticated${END_POINTS.AFFILIATE_OFFERS}`)({
    beforeLoad: ({ location }) => { roleGuards.managerAndAbove(location.pathname) },
    component: CommonLayout,
})


