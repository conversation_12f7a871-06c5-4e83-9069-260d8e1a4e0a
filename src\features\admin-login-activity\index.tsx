import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { ThemeSwitch } from '@/components/theme-switch'
import { NotificationStatusBar } from '@/components/notification-status-bar'
import { columns } from './components/admin-login-activity-columns'
import { adminLoginActivities } from './data/admin-login-activity'
import { AdminLoginActivityTable } from './components/admin-login-activity-table'

export default function AdminLoginActivity() {
  return (
    <>
      <Header fixed>
        <div className='ml-auto flex items-center space-x-4'>
          <NotificationStatusBar />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Admin Login Activity</h2>
          </div>
        </div>

        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          <AdminLoginActivityTable columns={columns} />
        </div>
      </Main>
    </>
  )
}
