import { Link, useSearch } from '@tanstack/react-router'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import AuthLayout from '../auth-layout'
import { OtpForm } from './components/otp-form'
import { getOtp } from '../sign-in/api'
import { toast } from 'sonner'

export default function Otp() {
  const { mutateAsync: getOtpMutation } = getOtp()
  const { email, type }: any = useSearch({ strict: false })

  const resendOtp = async () => {
    const payload: any = {
      type, email
    }

    const response: any = await getOtpMutation(payload)
    if (response?.success) {
      toast.success("OTP has been sent!")
    }
  }
  return (
    <AuthLayout>
      <Card className='gap-4'>
        <CardHeader>
          <CardTitle className='text-base tracking-tight'>
            Two-factor Authentication
          </CardTitle>
          <CardDescription>
            Please enter the authentication code. <br /> We have sent the
            authentication code to your email.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <OtpForm />
        </CardContent>
        <CardFooter>
          <p className='text-muted-foreground px-8 text-center text-sm'>
            Haven't received it?{' '}
            <button
              type="button"
              onClick={resendOtp}
              className='hover:text-primary underline underline-offset-4 bg-transparent border-none p-0 m-0 cursor-pointer'
            >
              Resend a new code.
            </button>
            .
          </p>
        </CardFooter>
      </Card>
    </AuthLayout>
  )
}
