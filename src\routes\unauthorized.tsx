import { createFile<PERSON>out<PERSON>, useNavigate, useRouter } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ShieldX, ArrowLeft, Home, LogOut } from 'lucide-react'
import { useAuthStore } from '@/stores/authStore'
import { getCurrentUserRoles, getUnauthorizedMessage } from '@/utils/route-protection'
import { END_POINTS } from '@/features/members/utils/constant'

function UnauthorizedPage() {
  const navigate = useNavigate()
  const router = useRouter()
  const { user, reset } = useAuthStore((state) => state.auth)
  
  const userRoles = getCurrentUserRoles()
  const previousPath = router.history.location.pathname
  const errorMessage = getUnauthorizedMessage(previousPath, userRoles)

  const handleGoBack = () => {
    window.history.back()
  }

  const handleGoHome = () => {
    navigate({ to: END_POINTS.DASHBOARD })
  }

  const handleLogout = () => {
    reset()
    navigate({ to: END_POINTS.SIGN_IN })
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
            <ShieldX className="h-8 w-8 text-destructive" />
          </div>
          <CardTitle className="text-2xl font-bold">Access Denied</CardTitle>
          <CardDescription className="text-base">
            You don't have permission to access this page
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Error Message */}
          <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
            <p className="text-sm text-muted-foreground">
              {errorMessage}
            </p>
          </div>

          {/* User Info */}
          <div className="space-y-3">
            <div className="text-sm">
              <span className="font-medium">Current User:</span>
              <div className="mt-1 text-muted-foreground">
                {user?.email || 'Not logged in'}
              </div>
            </div>
            
            <div className="text-sm">
              <span className="font-medium">Your Roles:</span>
              <div className="mt-2 flex flex-wrap gap-1">
                {userRoles.length > 0 ? (
                  userRoles.map((role) => (
                    <Badge key={role} variant="secondary" className="text-xs">
                      {role}
                    </Badge>
                  ))
                ) : (
                  <Badge variant="outline" className="text-xs">
                    No roles assigned
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button 
              onClick={handleGoBack} 
              variant="outline" 
              className="w-full"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
            
            <Button 
              onClick={handleGoHome} 
              className="w-full"
            >
              <Home className="mr-2 h-4 w-4" />
              Go to Dashboard
            </Button>
            
            <Button 
              onClick={handleLogout} 
              variant="ghost" 
              className="w-full text-muted-foreground"
            >
              <LogOut className="mr-2 h-4 w-4" />
              Sign Out
            </Button>
          </div>

          {/* Help Text */}
          <div className="text-center text-xs text-muted-foreground">
            If you believe this is an error, please contact your administrator
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export const Route = createFileRoute('/unauthorized')({
  component: UnauthorizedPage,
})
