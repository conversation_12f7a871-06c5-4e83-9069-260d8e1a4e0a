import React, { useState } from 'react'
import { ChatGroup } from '../data/schema'
import useDialogState from '@/hooks/use-dialog-state'

type ChatGroupDialogType = 'add' | 'edit' | 'delete'

interface ChatGroupContextType {
  open: ChatGroupDialogType | null
  setOpen: (str: ChatGroupDialogType | null) => void
  currentRow: ChatGroup | null
  setCurrentRow: React.Dispatch<React.SetStateAction<ChatGroup | null>>
}

const ChatGroupContext = React.createContext<ChatGroupContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function ChatGroupProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<ChatGroupDialogType>(null)
  const [currentRow, setCurrentRow] = useState<ChatGroup | null>(null)

  return (
    <ChatGroupContext value={{ open, setOpen, currentRow, setCurrentRow }}>
      {children}
    </ChatGroupContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useChatGroup = () => {
  const chatGroupContext = React.useContext(ChatGroupContext)

  if (!chatGroupContext) {
    throw new Error('useChatGroup has to be used within <ChatGroupContext>')
  }

  return chatGroupContext
}
