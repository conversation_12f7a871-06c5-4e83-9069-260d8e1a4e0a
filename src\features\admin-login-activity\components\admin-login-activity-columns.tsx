import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { AdminLoginActivity } from '../data/schema'
import { Button } from '@/components/ui/button'
import { IconTrash } from '@tabler/icons-react'
import { DataTableColumnHeader } from './data-table-column-header'
import LongText from '@/components/long-text'
import { formatTimestampToLocal } from '@/features/members/utils/utilities'
import { useState } from "react";
import { ConfirmDialog } from '@/components/confirm-dialog';
import { deleteAdminLoginDetailslApi } from '../api'
import { toast } from 'sonner'
import { useQueryClient } from '@tanstack/react-query'

export const columns: ColumnDef<AdminLoginActivity>[] = [
  {
    accessorKey: 'serialNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="#" />
    ),
    cell: ({ row }) => <div className="w-[40px]">{row.getValue('serialNumber')}</div>,
    enableSorting: false,
    enableHiding: false,
    meta: {
      className: cn(
        'sticky md:table-cell left-0 z-10 rounded-tl',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted'
      ),
    },
  },
  {
    accessorKey: 'user.username',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Admin Username" />
    ),
    cell: ({ row }: any) => (
      <LongText className='max-w-36'>{row?.original?.user?.username}</LongText>
    ),
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-6 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'role',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Admin Type" />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-36'>{row.getValue('role')}</LongText>
    ),
    filterFn: (row, id, value) => {
      return value === row.getValue(id)
    },
    meta: { className: 'w-36' },
  },
  {
    accessorKey: 'country',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Login IP (Country)" />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-48'>{row.getValue('country')}</LongText>
    ),
    enableSorting: false,
  },
  {
    accessorKey: 'loginAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Login Time" />
    ),
    cell: ({ row }: any) => {
      return <div>{formatTimestampToLocal(row?.original?.loginAt)}</div>
    },
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-6 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  // {
  //   accessorKey: 'actions',
  //   header: ({ column }) => (
  //     <DataTableColumnHeader column={column} title='Actions' />
  //   ),
  //   enableSorting: false,
  //   id: 'actions',
  //   cell: ({ row }) => {
  //     const [show, setShow] = useState(false);
  //     const { mutateAsync: deleteLoginDetailsMutation }: any = deleteAdminLoginDetailslApi()
  //     const queryClient = useQueryClient()
  //     // Placeholder for delete logic
  //     const handleDelete = async () => {
  //       const response: any = await deleteLoginDetailsMutation(row?.original?.id)
  //       if (response?.success) {
  //         setShow(false);
  //         queryClient.invalidateQueries(['login-activity-list']);
  //         toast.success("Activity Removed Successfully")
  //       }
  //       // TODO: implement actual delete logic here
  //     };
  //     return (
  //       <div className="flex items-center space-x-2">
  //         <Button
  //           variant="ghost"
  //           className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
  //           onClick={() => setShow(true)}
  //         >
  //           <IconTrash className="h-4 w-4" />
  //           <span className="sr-only">Delete</span>
  //         </Button>
  //         <ConfirmDialog
  //           open={show}
  //           onOpenChange={setShow}
  //           title="Are you sure?"
  //           desc="This action cannot be undone. This will permanently delete the record."
  //           confirmText="Delete"
  //           cancelBtnText="Cancel"
  //           destructive
  //           isLoading={false}
  //           handleConfirm={handleDelete}
  //         />
  //       </div>
  //     )
  //   },
  // },
]
