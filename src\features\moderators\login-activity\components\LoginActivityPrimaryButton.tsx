import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { moderatorLoginActivities } from "../data/moderator-login-activity";
import { exportAllData } from "../utils/csv-export";

export function LoginActivityPrimaryButton() {
    const handleExportAll = () => {
        exportAllData(moderatorLoginActivities);
    };

    return (
        <div className="flex gap-2">
            <Button
                className="space-x-1"
                onClick={handleExportAll}
            >
                <Download className="h-4 w-4" />
                <span>Export All</span>
            </Button>
        </div>
    );
}