import { Main } from "@/components/layout/main";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { addSexualOrientationsApi, updateSexualOrientationsApi, getSexualOrientationsDetails, getMasterLanguagesApi } from "../api";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import { END_POINTS } from "@/features/members/utils/constant";

export default function ManageSexualOrientations() {
    const navigate = useNavigate()
    const { sexualorientationsId } = useParams({ strict: false })
    const { data: { languages = [] } = {} } = getMasterLanguagesApi()
    const { mutateAsync: addMutation } = addSexualOrientationsApi();
    const { mutateAsync: updateMutation } = updateSexualOrientationsApi();
    const { data = {} } = getSexualOrientationsDetails(sexualorientationsId || "")
    const [isSubmitting, setIsSubmitting] = useState(false)

    const Schema = useMemo(() => {
        const shape: Record<string, z.ZodString> = {}
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => { const code = lang.code || lang.languageCode || lang.id; if (code) shape[code] = z.string().min(1, "Sexual orientation is required") })
        } else { ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].forEach(code => { shape[code] = z.string().min(1, "Sexual orientation is required") }) }
        return z.object(shape)
    }, [languages])
    type FormValues = z.infer<typeof Schema>

    const defaultValues = useMemo(() => {
        const d: Record<string, string> = {}
        const items = (data as any)?.items || []
        if (languages && languages.length > 0) {
            languages.forEach((lang: any) => { const code = lang.code || lang.languageCode || lang.id; if (code) d[code] = (items.find((it: any) => it.languageCode === code)?.message) || "" })
        } else { ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].forEach(code => { d[code] = (items.find((it: any) => it.languageCode === code)?.message) || "" }) }
        return d
    }, [languages, data])

    const form = useForm<FormValues>({ resolver: zodResolver(Schema), defaultValues: defaultValues as FormValues })
    const { control, handleSubmit, reset } = form
    const initializedRef = useRef(false)
    useEffect(() => {
        if (initializedRef.current) return
        if ((languages && languages.length > 0) || sexualorientationsId) {
            reset(defaultValues as FormValues)
            initializedRef.current = true
        }
    }, [languages, sexualorientationsId, defaultValues, reset])

    const onSubmit = async (values: FormValues) => {
        try {
            setIsSubmitting(true)
            const items = Object.entries(values).map(([languageCode, message]) => ({ languageCode, message }))
            const payload = { items }
            if (sexualorientationsId) { await updateMutation({ id: sexualorientationsId, data: payload }); toast.success("Sexual orientation updated successfully!") }
            else { await addMutation(payload); toast.success("Sexual orientation added successfully!") }
            navigate({ to: END_POINTS.SEXUAL_ORIENTATIONS })
        } catch (e: any) { toast.error(e?.response?.data?.message || "Action failed") } finally { setIsSubmitting(false) }
    }

    const isEditing = typeof sexualorientationsId === "string" && sexualorientationsId.length > 0

    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">{isEditing ? "Update Sexual Orientation" : "Add Sexual Orientation"}</h2>
                </div>
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <Card>
                    <CardHeader>
                        <CardTitle>Sexual Orientation Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                                <div className="grid grid-cols-2 gap-4">
                                    {(languages && languages.length > 0 ? languages.map((lang: any) => {
                                        const code = lang.code || lang.languageCode || lang.id; const name = lang.name || (code ? String(code).toUpperCase() : '')
                                        return (
                                            <FormField key={code} control={control} name={code as any} render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel className="text-sm font-medium">{name}</FormLabel>
                                                    <FormControl><Input placeholder={"Enter sexual orientation in " + name} {...field} /></FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )} />
                                        )
                                    }) : ['en', 'fr', 'de', 'nl', 'da', 'fi', 'it', 'no', 'pl', 'pt', 'el', 'es', 'sv'].map(code => (
                                        <FormField key={code} control={control} name={code as any} render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="text-sm font-medium">{String(code).toUpperCase()}</FormLabel>
                                                <FormControl><Input placeholder={"Enter sexual orientation in " + String(code).toUpperCase()} {...field} /></FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )} />
                                    )))}
                                </div>
                                <Separator />
                                <div className="flex justify-end space-x-2">
                                    <Button type="button" variant="outline" onClick={() => navigate({ to: END_POINTS.SEXUAL_ORIENTATIONS })}>Cancel</Button>
                                    <Button type="submit" disabled={isSubmitting}>{isEditing ? "Update Sexual Orientation" : "Add Sexual Orientation"}</Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </Main>
    )
}
