import React, { useState } from 'react'
import { Moderator } from '../data/schema'
import useDialogState from '@/hooks/use-dialog-state'

type UsersDialogType = 'invite' | 'add' | 'edit' | 'delete' | 'view' | 'login' | 'reply' | 'transactions' | 'deactivate'

interface ModeratorsContextType {
  open: UsersDialogType | null
  setOpen: (str: UsersDialogType | null) => void
  currentRow: Moderator | null
  setCurrentRow: React.Dispatch<React.SetStateAction<Moderator | null>>
}

const ModeratorsContext = React.createContext<ModeratorsContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function ModeratorsProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<UsersDialogType>(null)
  const [currentRow, setCurrentRow] = useState<Moderator | null>(null)

  return (
    <ModeratorsContext value={{ open, setOpen, currentRow, setCurrentRow }}>
      {children}
    </ModeratorsContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useModerators = () => {
  const moderatorsContext = React.useContext(ModeratorsContext)

  if (!moderatorsContext) {
    throw new Error('useModerators has to be used within <UsersContext>')
  }

  return moderatorsContext
}
