import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";
import axios from "axios";


export const getPackagesApi = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.PACKAGES, {
                params,
            });
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["package-list"],
    });


export const addPackageApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.PACKAGES, payload);
        },
    });


export const updatePackageApi = () =>
    useMutation({
        mutationFn: async ({ id, ...payload }: any) => {
            return await apiClient.put(`${API_ENDPOINTS.PACKAGES}/${id}`, payload);
        },
    });

export const getPackageDetails = (id: any = {}) =>
    useQuery({
        queryFn: async () => {
            if (typeof id === 'string') {
                const response = await apiClient.get(`${API_ENDPOINTS.PACKAGES}/${id}`)
                return response?.data ?? {}; // return [] or {} as a fallback
            }
            return {}
        },
        queryKey: ["package-details", id],
        enabled: !!id
    });

export const updatepackageStatusApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.patch(`${API_ENDPOINTS.PACKAGES}/${payload?.id}/status`);
        },
    });

export const getPresignedUrl = () =>
    useMutation({
        mutationFn: async (params: any) => {
            return await apiClient.get(`${API_ENDPOINTS.S3_PRESIGNED_URL}`, { params });
        },
    });

export const uploadFileToS3 = () =>
    useMutation({
        mutationFn: async ({ url, file }: any) => {
            return await axios.put(url, file);
        },
    });