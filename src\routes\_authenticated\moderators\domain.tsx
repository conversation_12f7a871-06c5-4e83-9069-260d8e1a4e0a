import { END_POINTS } from '@/features/members/utils/constant'
import Domain from '@/features/moderators/domain'
import { createFileRoute } from '@tanstack/react-router'
import { roleGuards } from '@/utils/route-protection'

export const Route = createFileRoute<any>(
  `/_authenticated${END_POINTS.MODERATOR_DOMAINS}`,
)({
  beforeLoad: ({ location }) => {
    roleGuards.adminOnly(location.pathname)
  },
  component: Domain,
})
