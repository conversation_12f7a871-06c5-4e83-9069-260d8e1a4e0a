import { useEffect, useState } from 'react'
import {
    ColumnDef,
    ColumnFiltersState,
    RowData,
    SortingState,
    VisibilityState,
    flexRender,
    getCoreRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    useReactTable,
} from '@tanstack/react-table'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { PaginationControls } from '@/components/ui/PaginationControls'
import { getPersonalitiesApi } from '../api'
import { DataTableToolbar } from './data-table-toolbar'

declare module '@tanstack/react-table' {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    interface ColumnMeta<TData extends RowData, TValue> {
        className?: string
    }
}

export function PersonalitiesTable({ columns }: any) {
    const [rowSelection, setRowSelection] = useState({})
    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [sorting, setSorting] = useState<SortingState>([])
    const [filters, setFilters] = useState<any>({ search: '' })
    const [pageIndex, setPageIndex] = useState(0)
    const [pageSize, setPageSize] = useState(10)

    const { data = {}, refetch }: any = getPersonalitiesApi({
        page: pageIndex + 1,
        limit: pageSize,
        ...filters
    })

    // Inject serial numbers into the data for the current page
    const dataWithSerialNumbers = (data?.personalities || []).map((item: any, idx: number) => ({
        ...item,
        serialNumber: idx + 1 + (pageIndex * pageSize),
    }));

    const table = useReactTable({
        data: dataWithSerialNumbers,
        columns,
        pageCount: data?.meta?.pages ?? -1,
        manualPagination: true,
        state: {
            pagination: { pageIndex, pageSize },
            sorting,
            columnVisibility,
            rowSelection,
            columnFilters,
        },
        onPaginationChange: (updater) => {
            const newState =
                typeof updater === 'function' ? updater({ pageIndex, pageSize }) : updater
            setPageIndex(newState.pageIndex)
            setPageSize(newState.pageSize)
        },
        enableRowSelection: true,
        onRowSelectionChange: setRowSelection,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
    })

    useEffect(() => {
        refetch()
    }, [pageIndex, filters.search, pageSize])

    const onPageChange = (pageIndex: any) => {
        setPageIndex(pageIndex)
    }

    const onFilterChanged = (filterValues: any, type: any) => {
        if (type === 0) {
            setPageIndex(0)
        }
        setFilters(filterValues)
    }

    return (
        <div className="space-y-4">
            <DataTableToolbar table={table} onFilterChanged={onFilterChanged} />
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header: any) => {
                                    return (
                                        <TableHead
                                            key={header.id}
                                            className={header.column.columnMeta?.className}
                                        >
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                        </TableHead>
                                    )
                                })}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow
                                    key={row.id}
                                    data-state={row.getIsSelected() && "selected"}
                                >
                                    {row.getVisibleCells().map((cell: any) => (
                                        <TableCell
                                            key={cell.id}
                                            className={cell.column.columnMeta?.className}
                                        >
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell
                                    colSpan={columns.length}
                                    className="h-24 text-center"
                                >
                                    No results.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
            <PaginationControls
                table={table}
                onPageChange={onPageChange}
            />
        </div>
    )
}
