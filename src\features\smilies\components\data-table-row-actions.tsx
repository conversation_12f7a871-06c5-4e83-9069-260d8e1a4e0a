import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useNavigate } from '@tanstack/react-router'
// import { deleteModelApi, modelStatusChange } from '../api'
import { useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { END_POINTS } from '@/features/members/utils/constant'
import { deleteSmileyApi } from '../api'


export function DataTableRowActions({ row }: any) {
  const navigate = useNavigate()
  const { mutateAsync: deleteSmileyMutation } = deleteSmileyApi()
  const queryClient = useQueryClient();
  const [show, setShow] = useState(false)

  const handleAction = async (action: any) => {
    switch (action) {
      case 'edit':
        navigate({
          to: `${END_POINTS.UPDATE_SMILEY}/${row.original.id}`,
        });
        break;
      case 'delete':
        setShow(true)
        break;
      default:
        break;
    }
  };

  const handleDelete = async () => {
    const res: any = await deleteSmileyMutation(row.original);
    if (res?.success) {
      setShow(false)
      queryClient.invalidateQueries({ queryKey: ['smiley-list'] });
    }
  }


  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          className='data-[state=open]:bg-muted flex h-8 w-8 p-0 cursor-pointer'
        >
          <DotsHorizontalIcon className='h-4 w-4' />
          <span className='sr-only'>Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[200px]'>

        <DropdownMenuItem onClick={() => handleAction('edit')}>
          Edit Smiley
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleAction('delete')}>
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>

      <ConfirmDialog
        open={show}
        onOpenChange={() => { setShow(false) }}
        title="Are you sure?"
        desc="This action cannot be undone. This will permanently delete the Flirt Message."
        confirmText="Delete"
        cancelBtnText="Cancel"
        destructive
        isLoading={false}
        handleConfirm={handleDelete}
      />
    </DropdownMenu>
  )
}
