import * as React from 'react'
import { cn } from '@/lib/utils'

interface ResponsiveContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  center?: boolean
}

const maxWidthClasses = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  full: 'max-w-full',
}

const paddingClasses = {
  none: '',
  sm: 'px-2 sm:px-4',
  md: 'px-4 sm:px-6',
  lg: 'px-6 sm:px-8',
}

export function ResponsiveContainer({
  className,
  maxWidth = 'full',
  padding = 'md',
  center = false,
  children,
  ...props
}: ResponsiveContainerProps) {
  return (
    <div
      className={cn(
        'w-full',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        center && 'mx-auto',
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Responsive Grid Component
interface ResponsiveGridProps extends React.HTMLAttributes<HTMLDivElement> {
  cols?: {
    mobile?: number
    tablet?: number
    desktop?: number
    default: number
  }
  gap?: 'sm' | 'md' | 'lg' | 'xl'
}

const gapClasses = {
  sm: 'gap-2',
  md: 'gap-4',
  lg: 'gap-6',
  xl: 'gap-8',
}

export function ResponsiveGrid({
  className,
  cols = { default: 1 },
  gap = 'md',
  children,
  ...props
}: ResponsiveGridProps) {
  const gridCols = React.useMemo(() => {
    const classes = ['grid']
    
    if (cols.mobile) {
      classes.push(`grid-cols-${cols.mobile}`)
    } else {
      classes.push(`grid-cols-${cols.default}`)
    }
    
    if (cols.tablet) {
      classes.push(`sm:grid-cols-${cols.tablet}`)
    }
    
    if (cols.desktop) {
      classes.push(`lg:grid-cols-${cols.desktop}`)
    }
    
    return classes.join(' ')
  }, [cols])

  return (
    <div
      className={cn(
        gridCols,
        gapClasses[gap],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Responsive Stack Component
interface ResponsiveStackProps extends React.HTMLAttributes<HTMLDivElement> {
  direction?: {
    mobile?: 'row' | 'col'
    tablet?: 'row' | 'col'
    desktop?: 'row' | 'col'
    default: 'row' | 'col'
  }
  spacing?: 'sm' | 'md' | 'lg' | 'xl'
  align?: 'start' | 'center' | 'end' | 'stretch'
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly'
}

const spacingClasses = {
  sm: 'space-x-2 space-y-2',
  md: 'space-x-4 space-y-4',
  lg: 'space-x-6 space-y-6',
  xl: 'space-x-8 space-y-8',
}

const alignClasses = {
  start: 'items-start',
  center: 'items-center',
  end: 'items-end',
  stretch: 'items-stretch',
}

const justifyClasses = {
  start: 'justify-start',
  center: 'justify-center',
  end: 'justify-end',
  between: 'justify-between',
  around: 'justify-around',
  evenly: 'justify-evenly',
}

export function ResponsiveStack({
  className,
  direction = { default: 'col' },
  spacing = 'md',
  align = 'start',
  justify = 'start',
  children,
  ...props
}: ResponsiveStackProps) {
  const flexDirection = React.useMemo(() => {
    const classes = ['flex']
    
    if (direction.mobile) {
      classes.push(`flex-${direction.mobile}`)
    } else {
      classes.push(`flex-${direction.default}`)
    }
    
    if (direction.tablet) {
      classes.push(`sm:flex-${direction.tablet}`)
    }
    
    if (direction.desktop) {
      classes.push(`lg:flex-${direction.desktop}`)
    }
    
    return classes.join(' ')
  }, [direction])

  return (
    <div
      className={cn(
        flexDirection,
        spacingClasses[spacing],
        alignClasses[align],
        justifyClasses[justify],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}
