"use client";
import { useRef } from "react";
import { getPageContent, updatePageContent } from "../api";
import { RichTextEditorDemo } from "./Demo";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";

export default function PageContentEditor() {
    const { data: { pageContent } = {} } = getPageContent();
    const { mutateAsync: updatePageContentMutation } = updatePageContent()

    const introRef = useRef<string | null>(null);
    const billingRef = useRef<string | null>(null);
    const affiliateRef = useRef<string | null>(null);
    const subAffiliateRef = useRef<string | null>(null);
    const moderatorRef = useRef<string | null>(null);

    const handleSave = async () => {

        const payload: any = {
            id: pageContent?.id,
            wlIntroductionText: introRef.current,
            wlBillingPageText: billingRef.current,
            affiliateOfferBillingPageText: affiliateRef.current,
            subAffiliateBillingPageText: subAffiliateRef.current,
            moderatorBillingPageText: moderatorRef.current,
        }

        const response: any = await updatePageContentMutation(payload)
        if (response?.success) {
            toast.success("Content has been updated!")
        }
    };

    return (
        <div className="p-4 space-y-6">
            <h2 className="text-xl font-semibold">Some page area text Contents</h2>

            <div className="space-y-2">
                <label className="font-medium mb-4">WL Introduction Text</label>
                <div className="mt-4">

                    <RichTextEditorDemo
                        value={pageContent?.wlIntroductionText}
                        htmlRef={introRef}
                    />
                </div>
            </div>

            <div className="space-y-2">
                <label className="font-medium">WL Billing Page Text</label>
                <div className="mt-4">

                    <RichTextEditorDemo
                        value={pageContent?.wlBillingPageText}
                        htmlRef={billingRef}
                    />
                </div>
            </div>

            <div className="space-y-2">
                <label className="font-medium">Affiliate Offer Billing Page Text</label>
                <div className="mt-4">

                    <RichTextEditorDemo
                        value={pageContent?.affiliateOfferBillingPageText}
                        htmlRef={affiliateRef}
                    />
                </div>
            </div>

            <div className="space-y-2">
                <label className="font-medium">Sub Affiliate Billing Page Text</label>
                <div className="mt-4">

                    <RichTextEditorDemo
                        value={pageContent?.subAffiliateBillingPageText}
                        htmlRef={subAffiliateRef}
                    />
                </div>
            </div>

            <div className="space-y-2">
                <label className="font-medium">Moderator Billing Page Text</label>
                <div className="mt-4">
                    <RichTextEditorDemo
                        value={pageContent?.moderatorBillingPageText}
                        htmlRef={moderatorRef}
                    />
                </div>
            </div>

            <div className="md:col-span-2 flex mt-4 justify-end">
                <Button onClick={handleSave}>
                    Save Content
                </Button>
            </div>
        </div>
    );
}
