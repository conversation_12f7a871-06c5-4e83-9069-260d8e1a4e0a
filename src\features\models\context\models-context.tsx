import React, { useState } from 'react'
import { Model } from '../data/models'
import useDialogState from '@/hooks/use-dialog-state'

type ModelsDialogType = 'invite' | 'add' | 'edit' | 'delete' | 'view' | 'login' | 'reply' | 'transactions' | 'deactivate'

interface ModelsContextType {
  open: ModelsDialogType | null
  setOpen: (str: ModelsDialogType | null) => void
  currentRow: Model | null
  setCurrentRow: React.Dispatch<React.SetStateAction<Model | null>>
}

const ModelsContext = React.createContext<ModelsContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function ModelsProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<ModelsDialogType>(null)
  const [currentRow, setCurrentRow] = useState<Model | null>(null)

  return (
    <ModelsContext value={{ open, setOpen, currentRow, setCurrentRow }}>
      {children}
    </ModelsContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useModels = () => {
  const modelsContext = React.useContext(ModelsContext)

  if (!modelsContext) {
    throw new Error('useModels has to be used within <ModelsContext>')
  }

  return modelsContext
}
