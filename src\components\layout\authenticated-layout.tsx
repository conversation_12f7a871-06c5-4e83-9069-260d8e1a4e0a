import { AppSidebar } from '@/components/layout/app-sidebar'
import SkipToMain from '@/components/skip-to-main'
import { SidebarProvider } from '@/components/ui/sidebar'
import { cn } from '@/lib/utils'
import { Outlet, useNavigate, useLocation } from '@tanstack/react-router'
import Cookies from 'js-cookie'
import { useEffect } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { END_POINTS } from '@/features/members/utils/constant'

interface Props {
  children?: React.ReactNode
}

export function AuthenticatedLayout({ children }: Props) {
  const navigate = useNavigate()
  const location = useLocation()
  const { accessToken, roles } = useAuthStore((state) => state.auth)
  const defaultOpen = roles.includes('chat-mod') || Cookies.get('sidebar_state') !== 'false'

  // 👇 Redirect to home if on /sign-in while already authenticated
  useEffect(() => {
    if (accessToken && location.pathname === END_POINTS.SIGN_IN) {
      navigate({ to: END_POINTS.DASHBOARD, replace: true })
    }
  }, [accessToken, location.pathname, navigate])

  return (
    <SidebarProvider defaultOpen={defaultOpen}>
      <SkipToMain />
      <AppSidebar />
      <div
        id='content'
        className={cn(
          'ml-auto w-full max-w-full',
          'peer-data-[state=collapsed]:w-[calc(100%-var(--sidebar-width-icon)-1rem)]',
          'peer-data-[state=expanded]:w-[calc(100%-var(--sidebar-width))]',
          'sm:transition-[width] sm:duration-200 sm:ease-linear',
          'flex h-svh flex-col',
          'group-data-[scroll-locked=1]/body:h-full',
          'has-[main.fixed-main]:group-data-[scroll-locked=1]/body:h-svh',
          'h-auto'
        )}
      >
        {children ? children : <Outlet />}
      </div>
    </SidebarProvider>
  )
}
