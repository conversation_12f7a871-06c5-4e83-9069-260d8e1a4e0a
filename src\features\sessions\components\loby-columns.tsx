import LongText from "@/components/long-text";
import { ColumnDef } from "@tanstack/react-table";
import { Model } from "../data/models";
import { DataTableColumnHeader } from "./data-table-column-header";
import { DataTableRowActions } from "./data-table-row-actions";

export const columns: ColumnDef<Model>[] = [
  {
    accessorKey: "client",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Profile" />
    ),
    cell: ({ row }) => {
      return <LongText className="max-w-36">{row.getValue("client")}</LongText>;
    },
    meta: { className: "w-36" },
  },
  {
    accessorKey: "profile",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Client" />
    ),
    cell: ({ row }) => {
      return (
        <LongText className="max-w-36">{row.getValue("profile")}</LongText>
      );
    },
    meta: { className: "w-36" },
  },
  {
    accessorKey: "country",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Country / City" />
    ),
    cell: ({ row }) => {
      return (
        <LongText className="max-w-36">
          {row.getValue("country")}/{row.original.city}
        </LongText>
      );
    },
    meta: { className: "w-36" },
  },
  {
    accessorKey: "domainname",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Domain" />
    ),
    cell: ({ row }) => {
      return (
        <LongText className="max-w-36">{row.getValue("domainname")}</LongText>
      );
    },
    meta: { className: "w-32" },
  },
  {
    accessorKey: "lastactivity",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Last Activity" />
    ),
    cell: ({ row }) => {
      return (
        <LongText className="max-w-36">
          {new Date(row.getValue("lastactivity")).toLocaleDateString("en-US", {
            weekday: "long",
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </LongText>
      );
    },
    meta: { className: "w-32" },
  },
  {
    id: "actions",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Action" />
    ),
    enableSorting: false,
    cell: DataTableRowActions,
    meta: { className: "w-16" },
  },
];
