import { But<PERSON> } from '@/components/ui/button'
import { IconEdit, IconTrash } from '@tabler/icons-react'
import { ChatGroup } from '../data/schema'
import { useChatGroup } from '../context/chat-group-context'

interface ChatGroupRowActionsProps {
  chatGroup: ChatGroup
}

export function ChatGroupRowActions({ chatGroup }: ChatGroupRowActionsProps) {
  const { setOpen, setCurrentRow } = useChatGroup()

  const handleEdit = () => {
    setCurrentRow(chatGroup)
    setOpen('edit')
  }

  const handleDelete = () => {
    setCurrentRow(chatGroup)
    setOpen('delete')
  }

  return (
    <div className='flex items-center gap-2'>
      <Button
        variant='ghost'
        size='sm'
        className='h-8 w-8 p-0 hover:bg-muted cursor-pointer'
        onClick={handleEdit}
      >
        <IconEdit className='h-4 w-4' />
      </Button>
      <Button
        variant='ghost'
        size='sm'
        className='h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 cursor-pointer'
        onClick={handleDelete}
      >
        <IconTrash className='h-4 w-4' />
      </Button>
    </div>
  )
}
