/**
 * Component to test route protection functionality
 * This can be temporarily added to any page to test route access
 */

import React from 'react';
import { useNavigate } from '@tanstack/react-router';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getCurrentUserRoles, hasRouteAccess, ROUTE_PERMISSIONS } from '@/utils/route-protection';
import { END_POINTS } from '@/features/members/utils/constant';

export function RouteProtectionTest() {
  const navigate = useNavigate();
  const { user, roles } = useAuthStore((state) => state.auth);
  const userRoles = getCurrentUserRoles();

  // Test routes with different permission levels
  const testRoutes = [
    { path: END_POINTS.SETTINGS, label: 'Settings (Admin Only)', expectedRoles: ['superadmin', 'admin'] },
    { path: END_POINTS.MEMBERS, label: 'Members (Manager+)', expectedRoles: ['superadmin', 'admin', 'manager'] },
    { path: END_POINTS.SMILIES, label: 'Smilies (Manager+)', expectedRoles: ['superadmin', 'admin', 'manager'] },
    { path: END_POINTS.LOBY, label: 'Lobby (Chat-Mod+)', expectedRoles: ['superadmin', 'admin', 'manager', 'chat-mod'] },
    { path: '/admin-login-activity', label: 'Admin Login (Admin Only)', expectedRoles: ['superadmin', 'admin'] },
    { path: '/moderators', label: 'Moderators (Admin Only)', expectedRoles: ['superadmin', 'admin'] },
  ];

  const handleTestRoute = (path: string) => {
    try {
      navigate({ to: path });
    } catch (error) {
      console.log('Navigation blocked:', error);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto mt-4">
      <CardHeader>
        <CardTitle className="text-lg">🔐 Route Protection Test</CardTitle>
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-muted-foreground">Current Roles:</span>
          {userRoles.length > 0 ? (
            userRoles.map((role) => (
              <Badge key={role} variant="secondary" className="text-xs">
                {role}
              </Badge>
            ))
          ) : (
            <Badge variant="outline" className="text-xs">
              No roles
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="text-sm text-muted-foreground">
          Click the buttons below to test route access. Routes you don't have permission for should redirect to the unauthorized page.
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {testRoutes.map((route) => {
            const hasAccess = hasRouteAccess(route.path);
            const requiredRoles = ROUTE_PERMISSIONS[route.path as keyof typeof ROUTE_PERMISSIONS];
            
            return (
              <div key={route.path} className="space-y-2">
                <Button
                  onClick={() => handleTestRoute(route.path)}
                  variant={hasAccess ? "default" : "destructive"}
                  size="sm"
                  className="w-full justify-start text-left"
                >
                  <span className="mr-2">
                    {hasAccess ? "✅" : "❌"}
                  </span>
                  {route.label}
                </Button>
                <div className="text-xs text-muted-foreground px-2">
                  Required: {requiredRoles?.join(', ') || 'Public'}
                </div>
              </div>
            );
          })}
        </div>

        <div className="border-t pt-4 space-y-2">
          <div className="text-sm font-medium">Legend:</div>
          <div className="text-xs space-y-1">
            <div>✅ <span className="text-green-600">You have access</span> - Should navigate successfully</div>
            <div>❌ <span className="text-red-600">Access denied</span> - Should redirect to unauthorized page</div>
          </div>
        </div>

        <div className="border-t pt-4">
          <div className="text-xs text-muted-foreground">
            <strong>How to test:</strong>
            <ol className="list-decimal list-inside mt-1 space-y-1">
              <li>Use the Role Debug Panel to change your role</li>
              <li>Click on route buttons to test access</li>
              <li>Verify that restricted routes redirect to /unauthorized</li>
              <li>Check that allowed routes navigate successfully</li>
            </ol>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Conditional export for development only
export const RouteProtectionTestComponent = process.env.NODE_ENV === 'development' ? RouteProtectionTest : () => null;
