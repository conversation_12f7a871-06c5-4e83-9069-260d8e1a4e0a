import { Main } from "@/components/layout/main";
import { HairColorsPrimaryButtons } from "./components/hair-colors-primary-buttons";
import { HairColorsTable } from "./components/hair-colors-table";
import { columns } from "./components/hair-colors-columns";

export default function HairColorsList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Hair Colors List</h2>
                </div>
                <HairColorsPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <HairColorsTable columns={columns} />
            </div>
        </Main>
    )
}
