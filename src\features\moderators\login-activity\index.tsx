import { Main } from '@/components/layout/main'
import { columns } from './components/moderator-login-activity-columns'
import { moderatorLoginActivities } from './data/moderator-login-activity'
import { ModeratorLoginActivityTable } from './components/moderator-login-activity-table'
import { LoginActivityPrimaryButton } from './components/LoginActivityPrimaryButton'

export default function ModeratorLoginActivity() {
  return (
    <Main>
      <div className=' flex flex-wrap items-center justify-between space-y-2'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>Moderator Login Activity</h2>
        </div>
        {/* <LoginActivityPrimaryButton /> */}

      </div>

      <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
        <ModeratorLoginActivityTable data={moderatorLoginActivities} columns={columns} />
      </div>
    </Main>
  )
}
