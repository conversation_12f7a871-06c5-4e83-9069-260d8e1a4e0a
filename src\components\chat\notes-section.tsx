import { Input } from "@/components/ui/input";
import { Note } from "./types";

interface NotesSectionProps {
  notes: Note[];
  onAddNote?: (note: string) => void;
}

export function NotesSection({ notes, onAddNote }: NotesSectionProps) {
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const note = formData.get("note") as string;
    if (note.trim()) {
      onAddNote?.(note.trim());
      e.currentTarget.reset();
    }
  };

  return (
    <div className="bg-sidebar p-4 rounded-2xl">
      <div className="text-base font-semibold mb-[12px]">Notes</div>
      
      {notes.length > 0 && (
        <div className="flex mb-4 overflow-x-auto space-x-3 flex-nowrap">
          {notes.map((note) => (
            <div key={note.id} className="bg-sidebar-accent p-3 rounded-lg min-w-[200px]">
              <div className="text-sm">{note.content}</div>
              <hr className="my-3" />
              <div className="flex gap-4 items-center justify-between">
                <div className="text-xs">Edited: {note.editedDate}</div>
                <div></div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <Input 
          type="text" 
          name="note"
          placeholder="Enter Note" 
          className="text-sm h-[42px] shadow-none" 
        />
      </form>
    </div>
  );
}
