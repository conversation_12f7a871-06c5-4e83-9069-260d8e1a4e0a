import { IconUserPlus } from "@tabler/icons-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";

export function SmilyPrimaryButtons() {
    const router = useRouter();

    return (
        <div className="flex gap-2">
            <Button
                className="space-x-1"
                onClick={() =>
                    router.navigate({ to: END_POINTS.ADD_SMILEY })
                }
            >
                <span>Add Smily</span> <IconUserPlus size={18} />
            </Button>


        </div>
    );
}
