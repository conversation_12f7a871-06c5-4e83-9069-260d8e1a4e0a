import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useState } from 'react'
import { giftGroupValue } from '../utils/constant'

interface DataTableToolbarProps<TData> {
    readonly table: Table<TData>
    readonly onFilterChanged?: any
}

const groupOptions = [
    'Adult/Casual',
    'Love',
    'Sexy',
]


export function DataTableToolbar<TData>({
    table,
    onFilterChanged
}: DataTableToolbarProps<TData>) {
    const isFiltered = table.getState().columnFilters.length > 0

    const [filters, setFilters] = useState({
        group: undefined as string | undefined,
    })

    const [hasSearched, setHasSearched] = useState(false)

    const handleFilterChange = (
        key: 'group',
        value: string | undefined
    ) => {
        setFilters((prev) => ({ ...prev, [key]: value }))
    }

    const handleSearch = () => {
        // Apply filters to the table
        if (filters.group && (filters.group === 'Adult/Casual' || filters.group === 'Love' || filters.group === 'Sexy')) {
            const val = giftGroupValue[filters.group]
            table.getColumn('type')?.setFilterValue(val)
        }

        setHasSearched(true)
        onFilterChanged?.({
            group: filters.group ? giftGroupValue[filters.group as keyof typeof giftGroupValue] : undefined,
        }, 1)
    }

    const handleReset = () => {
        // Reset all column filters
        table.resetColumnFilters()

        // Reset the custom filter function for profile column
        const profileColumn = table.getColumn('profile')
        if (profileColumn?.columnDef) {
            // Reset to default filter function (contains text search)
            profileColumn.columnDef.filterFn = 'includesString'
        }

        const f: any = {
            type: undefined,
        }
        // Reset filter state
        setFilters(f)
        setHasSearched(false)
        onFilterChanged(f, 0)
    }

    const hasActiveFilters = Boolean(filters.group)

    return (
        <div className='flex items-center justify-between'>
            <div className='flex flex-1 items-center gap-4 flex-wrap'>
                <FilterSelect
                    value={filters.group}
                    placeholder="Select Type"
                    options={groupOptions}
                    onChange={(value) => handleFilterChange('group', value)}
                />
                <Button
                    onClick={handleSearch}
                    className="h-8 px-3"
                    disabled={!hasActiveFilters}
                >
                    Search
                </Button>
                {(isFiltered || hasSearched) && (
                    <Button
                        variant='outline'
                        onClick={handleReset}
                        className='h-8 px-2 lg:px-3'
                    >
                        Reset
                        {/* <Cross2Icon className='ml-2 h-4 w-4' /> */}
                    </Button>
                )}
            </div>
        </div>
    )
}
