import { 
  Icon<PERSON>lock, 
  <PERSON>conLanguage, 
  IconPaperclip, 
  IconGift, 
  IconMoodSmile,
  IconShareplay,
  IconMessageReply,
  IconDashboard,
  IconUsersGroup
} from "@tabler/icons-react";
import { useState } from "react";

interface ChatInputProps {
  onReplyStay?: () => void;
  onReply?: () => void;
  onLobby?: () => void;
  onProblem?: () => void;
  onSendMessage?: (message: string) => void;
}

export function ChatInput({ 
  onReplyStay, 
  onReply, 
  onLobby, 
  onProblem, 
  onSendMessage 
}: ChatInputProps) {
  const [message, setMessage] = useState("");
  const [timer, setTimer] = useState(266);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      onSendMessage?.(message.trim());
      setMessage("");
    }
  };

  const addTime = () => {
    setTimer(prev => prev + 60);
  };

  return (
    <div className="w-full p-4 space-y-4 bg-sidebar rounded-b-2xl">
      {/* Timer Buttons */}
      <div className="flex gap-2 mb-2">
        <button className="flex items-center gap-1 px-4 py-2 text-sm bg-sidebar-accent rounded-md">
          <IconClock size={16} />
          {timer} Sec
        </button>
        <button 
          onClick={addTime}
          className="px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md"
        >
          + 60 Sec
        </button>
      </div>

      {/* Message Input */}
      <form onSubmit={handleSubmit}>
        <div className="flex items-center justify-between border rounded-xl px-4 py-3 bg-sidebar mb-2">
          <input
            type="text"
            placeholder="Enter Message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="flex-1 outline-none text-md bg-transparent placeholder-gray-400"
          />
          <div className="flex items-center gap-3 text-gray-500">
            <IconLanguage size={24} />
            <IconPaperclip size={24} />
            <IconGift size={24} />
            <IconMoodSmile size={24} />
          </div>
        </div>
      </form>

      {/* Action Buttons */}
      <div className="flex flex-wrap items-center justify-between gap-2">
        <div className="flex gap-2">
          <button 
            onClick={onReplyStay}
            className="flex items-center gap-1 px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md"
          >
            <IconShareplay size={16} />
            Reply & Stay
          </button>
          <button 
            onClick={onReply}
            className="flex items-center gap-1 px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md"
          >
            <IconMessageReply size={16} />
            Reply
          </button>
          <button 
            onClick={onLobby}
            className="flex items-center gap-1 px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md"
          >
            <IconDashboard size={16} />
            Lobby
          </button>
        </div>
        <button 
          onClick={onProblem}
          className="flex items-center gap-1 px-4 py-2 text-sm border rounded-md cursor-pointer"
        >
          <IconUsersGroup size={16} />
          Problem
        </button>
      </div>
    </div>
  );
}
