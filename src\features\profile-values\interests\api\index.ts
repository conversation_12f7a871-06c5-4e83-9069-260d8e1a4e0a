import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getInterestsApi = (params: any = {}) => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.INTERESTS, { params })).data ?? {},
    queryKey: ["interests-list"],
});

export const addInterestsApi = () => useMutation({
    mutationFn: async (data: any) => (await apiClient.post(API_ENDPOINTS.INTERESTS, data)).data,
});

export const updateInterestsApi = () => useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => (await apiClient.put(`${API_ENDPOINTS.INTERESTS}/${id}`, data)).data,
});

export const deleteInterestsApi = () => useMutation({
    mutationFn: async (id: string) => (await apiClient.delete(`${API_ENDPOINTS.INTERESTS}/${id}`)).data,
});

export const getInterestsDetails = (id: string) => useQuery({
    queryFn: async () => (await apiClient.get(`${API_ENDPOINTS.INTERESTS}/${id}`)).data ?? {},
    queryKey: ["interests-details", id],
    enabled: !!id,
});

export const getMasterLanguagesApi = () => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES)).data ?? {},
    queryKey: ["master-languages"],
});
