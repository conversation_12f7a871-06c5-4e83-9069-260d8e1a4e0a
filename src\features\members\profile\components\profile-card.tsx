import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { IconDotsVertical, IconCamera, IconChevronRight } from '@tabler/icons-react'
import { Member } from '../../data/schema'
import { useLocation, useNavigate, useParams, useSearch } from '@tanstack/react-router'
import { useState, useRef, useEffect } from 'react'
import { END_POINTS } from '../../utils/constant'
import { toast } from 'sonner'
import { S3_BASE_URL, validateImageFile } from '../../utils/utilities'
import { getPresignedUrl, imageApprovalApi, updateAvatarApi, uploadFileToS3 } from '../../api'
import { useQueryClient } from '@tanstack/react-query'

interface ProfileCardProps {
  member: any
  activeSection?: string
  onSelectSection?: any
}

const PATHS: any = ['messages', 'pictures', 'credits']


export default function ProfileCard({ member, activeSection, onSelectSection }: Readonly<ProfileCardProps>) {
  const navigate = useNavigate()
  const location: any = useLocation()
  const segments = location.pathname.split('/').filter(Boolean)

  const { memberId } = useParams({ from: '/_authenticated/members/profile/$memberId' })
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [profileImage, setProfileImage] = useState<string>(S3_BASE_URL + member?.avatar)
  const [selectedSection, setSelectedSection] = useState<string>(`${PATHS.includes(segments[segments.length - 1]) ? segments[segments.length - 1] : 'profile'}`)
  const [isUploading, setIsUploading] = useState(false);

  const { mutateAsync: getPreSignedUrlMutation } = getPresignedUrl()
  const { mutateAsync: updateAvatarMutation } = updateAvatarApi()
  const { mutateAsync: uploadFileToS3Mutation } = uploadFileToS3()
  const { mutateAsync: imageApprovalMutation } = imageApprovalApi()
  const queryClient = useQueryClient()

  // Auto-detect active section from URL if not provided
  const urlActiveSection = (() => {
    const currentPath = window.location.pathname

    // Check for specific sections in the URL
    if (currentPath.includes('/pictures')) {
      return 'pictures'
    }
    if (currentPath.includes('/credits')) {
      return 'credits'
    }
    if (currentPath.includes('/messages')) {
      return 'messages'
    }

    return 'profile'
  })()

  // Use selectedSection state if set, otherwise use activeSection prop or URL detection
  const currentActiveSection = selectedSection ?? activeSection ?? urlActiveSection

  // Initialize selectedSection based on URL when component mounts
  useEffect(() => {
    if (!selectedSection && !activeSection) {
      setSelectedSection(urlActiveSection)
    }
  }, [urlActiveSection, selectedSection, activeSection])

  useEffect(() => {
    setProfileImage(S3_BASE_URL + member?.avatar)
  }, [member?.avatar])

  // const initials = member.profile
  //   .split(' ')
  //   .map(n => n[0])
  //   .join('')
  //   .toUpperCase()

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const error = validateImageFile(file);
    if (error) {
      toast.error(error);
      return;
    }
    setIsUploading(true);
    try {
      const ext = file.name.split(".").pop()?.toLowerCase() || "jpg";
      const presignedRes: any = await getPreSignedUrlMutation({
        location: "users",
        type: ext,
        count: 1,
      });
      const fileData = presignedRes?.data?.files?.[0];
      if (!fileData) {
        toast.error("Failed to get S3 upload URL.");
        return;
      }
      await uploadFileToS3Mutation({
        url: fileData.url,
        file,
      });
      await updateAvatarMutation({
        imagePath: fileData.filename,
        id: memberId
      });

      setProfileImage(import.meta.env.VITE_S3_BASE_URL + fileData.filename)
      queryClient.invalidateQueries({ queryKey: ['member-details'] })
    } catch (err: any) {
      console.log(err);
    } finally {
      setIsUploading(false);
      e.target.value = ""
    }
  }

  const handleCameraClick = () => {
    fileInputRef.current?.click()
  }

  const menuItems = [
    {
      label: 'View Profile',
      key: 'profile',
      onClick: () => {
        setSelectedSection('profile')
        onSelectSection('profile')
        navigate({ to: `${END_POINTS.MEMBERS_PROFILE}/$memberId`, params: { memberId } })
      }
    },
    {
      label: 'Profile Pictures',
      key: 'pictures',
      onClick: () => {
        setSelectedSection('pictures')
        onSelectSection('pictures')
        navigate({ to: `${END_POINTS.MEMBERS_PROFILE}/$memberId/pictures`, params: { memberId } })
      }
    },
    {
      label: 'Credit History',
      key: 'credits',
      onClick: () => {
        setSelectedSection('credits')
        onSelectSection('credits')
        navigate?.({ to: `${END_POINTS.MEMBERS_PROFILE}/$memberId/credits`, params: { memberId } })
      }
    },
    {
      label: 'Message History',
      key: 'messages',
      onClick: () => {
        setSelectedSection('messages')
        onSelectSection('messages')
        navigate({ to: `${END_POINTS.MEMBERS_PROFILE}/$memberId/messages`, params: { memberId } })
      }
    }
  ]

  const approveProfilePicture = async () => {
    try {
      const response: any = await imageApprovalMutation({
        id: memberId
      });
      if (response?.success) {
        toast.success('Profile picture has been approved!')
        queryClient.invalidateQueries({ queryKey: ['member-details'] })
      }
    } catch (error) {
      toast.error('Failed to approve profile picture')
    }
  }

  return (
    <div className="bg-background rounded-xl border overflow-hidden h-full">
      {/* Profile Header */}
      <div className="p-4 border border-b">
        <div className="text-base font-medium text-muted-foreground mb-4">Profile Picture</div>
        <div className="flex flex-col w-full">
          {/* Avatar with camera icon */}
          <div className="relative mb-3 h-30 w-30">
            <Avatar className="h-30 w-30 flex items-center justify-center">
              <AvatarImage src={profileImage} alt={member?.profile || ""} />
              <svg xmlns="http://www.w3.org/2000/svg" className='w-10 h-10 text-muted-foreground' viewBox="0 0 24 24" fill="currentColor"><path d="M4 22C4 17.5817 7.58172 14 12 14C16.4183 14 20 17.5817 20 22H18C18 18.6863 15.3137 16 12 16C8.68629 16 6 18.6863 6 22H4ZM12 13C8.685 13 6 10.315 6 7C6 3.685 8.685 1 12 1C15.315 1 18 3.685 18 7C18 10.315 15.315 13 12 13ZM12 11C14.21 11 16 9.21 16 7C16 4.79 14.21 3 12 3C9.79 3 8 4.79 8 7C8 9.21 9.79 11 12 11Z"></path></svg>
            </Avatar>
            <Button
              size="icon"
              className="absolute bottom-1 right-1 h-8 w-8 rounded-full cursor-pointer bg-[#4F4F4F]"
              onClick={handleCameraClick}
            >
              <IconCamera className="h-4 w-4" />
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
          </div>
          <h2 className="text-base font-bold text-left text-muted-foreground">{member?.username || ""}</h2>
          {
            !member?.isImageApproved &&
            <Button onClick={approveProfilePicture} variant={'secondary'} className='mt-4' style={{ cursor: 'pointer' }}>
              Click to Approve Profile Picture
            </Button>
          }

          {/* Name */}
        </div>

        {/* Actions Menu */}
        {/* <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="absolute top-4 right-4 cursor-pointer">
              <IconDotsVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem className="cursor-pointer">Edit Profile Details</DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer">Reset Password</DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer">View Login Activity</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-red-600 cursor-pointer">Block User</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu> */}
      </div>

      {/* Menu Items */}
      <div className="">
        <div className="space-y-1">
          {menuItems.map((item) => (
            <button
              key={item.key}
              onClick={item.onClick}
              className={`w-full flex items-center justify-between p-4 text-left transition-all duration-200 hover:bg-muted/50 ${currentActiveSection === item.key
                ? 'bg-primary/5  text-primary font-semibold'
                : 'hover:bg-muted/50'
                }`}
            >
              <span className={`text-sm font-medium ${currentActiveSection === item.key ? 'text-primary' : 'text-muted-foreground'
                }`}>
                {item.label}
              </span>
              <IconChevronRight className={`h-4 w-4 ${currentActiveSection === item.key ? 'text-primary' : 'text-foreground'
                }`} />
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}