import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getBestFeaturesApi = (params: any = {}) => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.BEST_FEATURES, { params })).data ?? {},
    queryKey: ["best-features-list"],
});

export const addBestFeaturesApi = () => useMutation({
    mutationFn: async (data: any) => (await apiClient.post(API_ENDPOINTS.BEST_FEATURES, data)).data,
});

export const updateBestFeaturesApi = () => useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => (await apiClient.put(`${API_ENDPOINTS.BEST_FEATURES}/${id}`, data)).data,
});

export const deleteBestFeaturesApi = () => useMutation({
    mutationFn: async (id: string) => (await apiClient.delete(`${API_ENDPOINTS.BEST_FEATURES}/${id}`)).data,
});

export const getBestFeaturesDetails = (id: string) => useQuery({
    queryFn: async () => (await apiClient.get(`${API_ENDPOINTS.BEST_FEATURES}/${id}`)).data ?? {},
    queryKey: ["best-features-details", id],
    enabled: !!id,
});

export const getMasterLanguagesApi = () => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES)).data ?? {},
    queryKey: ["master-languages"],
});
