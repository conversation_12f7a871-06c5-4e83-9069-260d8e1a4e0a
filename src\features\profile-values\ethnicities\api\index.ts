import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getEthnicitiesApi = (params: any = {}) => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.ETHNICITIES, { params })).data ?? {},
    queryKey: ["ethnicities-list"],
});

export const addEthnicitiesApi = () => useMutation({
    mutationFn: async (data: any) => (await apiClient.post(API_ENDPOINTS.ETHNICITIES, data)).data,
});

export const updateEthnicitiesApi = () => useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => (await apiClient.put(`${API_ENDPOINTS.ETHNICITIES}/${id}`, data)).data,
});

export const deleteEthnicitiesApi = () => useMutation({
    mutationFn: async (id: string) => (await apiClient.delete(`${API_ENDPOINTS.ETHNICITIES}/${id}`)).data,
});

export const getEthnicitiesDetails = (id: string) => useQuery({
    queryFn: async () => (await apiClient.get(`${API_ENDPOINTS.ETHNICITIES}/${id}`)).data ?? {},
    queryKey: ["ethnicities-details", id],
    enabled: !!id,
});

export const getMasterLanguagesApi = () => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES)).data ?? {},
    queryKey: ["master-languages"],
});
