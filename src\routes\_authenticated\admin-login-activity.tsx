import AdminLoginActivity from '@/features/admin-login-activity'
import { END_POINTS } from '@/features/members/utils/constant'
import { createFileRoute } from '@tanstack/react-router'
import { roleGuards } from '@/utils/route-protection'

export const Route = createFileRoute<any>(`/_authenticated${END_POINTS.ADMIN_LOGIN_ACTIVITY}`)({
  beforeLoad: ({ location }) => {
    roleGuards.adminOnly(location.pathname)
  },
  component: AdminLoginActivity,
})
