import { END_POINTS } from '@/features/members/utils/constant'
import ModeratorLoginActivity from '@/features/moderators/login-activity'
import { createFileRoute } from '@tanstack/react-router'
import { roleGuards } from '@/utils/route-protection'

export const Route = createFileRoute<any>(`/_authenticated${END_POINTS.MODERATORS_LOGIN_ACTIVITY}`)({
  beforeLoad: ({ location }) => {
    roleGuards.adminOnly(location.pathname)
  },
  component: ModeratorLoginActivity,
})
