import { useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";


export const getContactUsList = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.CONTACT_US, {
                params,
            });
            return response?.data ?? {};
        },
        queryKey: ["contact-us-list", params],
    });
