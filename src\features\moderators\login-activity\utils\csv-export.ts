import { ModeratorLoginActivity } from '../data/schema'

/**
 * Utility function to export moderator login activity data to CSV
 * @param data - Array of ModeratorLoginActivity objects
 * @param filename - Name of the CSV file to download
 */
export function exportToCSV(data: ModeratorLoginActivity[], filename: string = 'moderatorLoginActivity.csv') {
  // Define CSV headers
  const headers = [
    '#',
    'Moderator',
    'Login',
    'Logout',
    'Login Session',
    'Last Activity Time',
    'IP (Country)',
    'Device Info',
    'Total Message Sent'
  ]

  // Helper function to escape CSV values
  const escapeCSVValue = (value: any): string => {
    if (value === null || value === undefined) return ''
    const stringValue = String(value)
    // If the value contains comma, newline, or quote, wrap it in quotes and escape internal quotes
    if (stringValue.includes(',') || stringValue.includes('\n') || stringValue.includes('"')) {
      return `"${stringValue.replace(/"/g, '""')}"`
    }
    return stringValue
  }

  // Convert data to CSV format
  const csvContent = [
    headers.join(','), // Header row
    ...data.map((row: ModeratorLoginActivity) => [
      escapeCSVValue(row.id),
      escapeCSVValue(row.moderator),
      escapeCSVValue(new Date(row.login).toLocaleString()),
      escapeCSVValue(new Date(row.logout).toLocaleString()),
      escapeCSVValue(row.loginSession),
      escapeCSVValue(new Date(row.lastActivityTime).toLocaleString()),
      escapeCSVValue(row.ipCountry),
      escapeCSVValue(row.deviceInfo),
      escapeCSVValue(row.totalMessageSent)
    ].join(','))
  ].join('\n')

  // Create and download the CSV file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)

  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'

  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // Clean up the URL object
  URL.revokeObjectURL(url)
}

/**
 * Export all moderator login activity data
 * @param data - Array of all ModeratorLoginActivity objects
 */
export function exportAllData(data: ModeratorLoginActivity[]) {
  exportToCSV(data, 'moderatorLoginActivity.csv')
}

/**
 * Export filtered moderator login activity data
 * @param data - Array of filtered ModeratorLoginActivity objects
 */
export function exportFilteredData(data: ModeratorLoginActivity[]) {
  exportToCSV(data, 'moderatorLoginActivityFiltered.csv')
}
