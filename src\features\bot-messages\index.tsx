import { Main } from "@/components/layout/main";
// import BotMessagesProvider from "./context/bot-messages-context"; // Uncomment if context is created

import { <PERSON><PERSON> } from "@/components/ui/button";
import { BotMessagesTable } from "./components/bot-messages-table";
import { columns } from "./components/bot-messages-columns";
import { BotMessagePrimaryButtons } from "./components/bot-message-primary-button";

export default function List() {
    return (
        // <BotMessagesProvider> {/* Uncomment if context is created */}
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">
                        Bot Messages List
                    </h2>
                </div>
                <BotMessagePrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <BotMessagesTable columns={columns} />
            </div>
        </Main>
        // </BotMessagesProvider>
    );
} 