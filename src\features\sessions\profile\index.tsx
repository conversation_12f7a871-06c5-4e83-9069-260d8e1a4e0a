import { use<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import { models } from '../data/models'
import ProfileCard from './components/profile-card'
import { IconArrowLeft } from '@tabler/icons-react'
import ProfileStats from './components/profile-stats'
import ActivityChart from './components/activity-chart'

export default function ModelProfile() {
  const { modelId } = useParams({ from: '/_authenticated/models/profile/$modelId' })

  // Find the model by ID (in a real app, this would be an API call)
  const model = models.find(m => m.id === modelId)

  if (!model) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Model not found</p>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className=" mx-auto">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-gray-600 mb-6">
          <Link to="/models" className="flex items-center gap-1 hover:text-gray-900">
            <IconArrowLeft className="h-4 w-4" />
            Models
          </Link>
          <span>/</span>
          <span className="text-gray-900">Model Profile</span>
        </div>

        {/* Main Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 ">
          {/* Left Side - Profile Card */}
          <div className="lg:col-span-1">
            <ProfileCard model={model} />
          </div>

          {/* Right Side - Stats and Chart */}
          <div className="lg:col-span-3 space-y-6">
            {/* Stats Cards */}
            <ProfileStats />

            {/* Activity Chart */}
            <ActivityChart />
          </div>
        </div>
      </div>
    </div>
  )
}
