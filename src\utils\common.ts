export const calculateAge = (dob: string) => {
  if (!dob) return "N/A";
  const birthDate = new Date(dob);
  const today = new Date();
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    return age - 1;
  }
  return age;
};

export const generateNickName = (name: string | null) => {
  if (!name) return;

  const nameArray = name.split(" ");
  if (nameArray?.[0] && nameArray?.[1]) {
    return `${nameArray[0][0]}${nameArray[1][0]}`;
  } else {
    return nameArray[0][0]?.toUpperCase();
  }
};

export const FormatS3ImgUrl = (filename: string) => {
    return `${import.meta.env.VITE_S3_BASE_URL}${filename}`
}
