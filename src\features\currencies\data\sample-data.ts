import { CurrenciessData } from "../components/currencies-table";


// Sample data for Signups
export const currenciesData: CurrenciessData[] = [
    { rank: 1, currencyName: 'Euro', code: 'EUR', symbol: "$", isDefault: "Yes", exchangeRate: 100000, lastUpdate: 'Jan 18 2025', displayOrder: 1, apiSource: "Fixer.io", status: "Active" },
    { rank: 2, currencyName: 'US Dollar', code: 'EUR', symbol: "$", isDefault: "No", exchangeRate: 100000, lastUpdate: 'Jan 18 2025', displayOrder: 2, apiSource: "Fixer.io", status: "Active" },
    { rank: 3, currencyName: 'Canadian Dollars', code: 'EUR', symbol: "$", isDefault: "No", exchangeRate: 100000, lastUpdate: 'Jan 18 2025', displayOrder: 3, apiSource: "Fixer.io", status: "Active" },
    { rank: 4, currencyName: 'Australian Dollars', code: 'EUR', symbol: "$", isDefault: "No", exchangeRate: 100000, lastUpdate: 'Jan 18 2025', displayOrder: 4, apiSource: "Fixer.io", status: "Active" },
    { rank: 5, currencyName: 'Pond Sterling', code: 'EUR', symbol: "$", isDefault: "No", exchangeRate: 100000, lastUpdate: 'Jan 18 2025', displayOrder: 5, apiSource: "Fixer.io", status: "Active" },
]