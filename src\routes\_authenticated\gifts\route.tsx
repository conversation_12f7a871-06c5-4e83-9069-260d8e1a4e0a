import CommonLayout from "@/features/gifts/common-layout";
import { END_POINTS } from "@/features/members/utils/constant";
import { createFileRoute } from "@tanstack/react-router";
import { roleGuards } from '@/utils/route-protection'

export const Route = createFileRoute<any>(`/_authenticated${END_POINTS.GIFTS}`)({
  beforeLoad: ({ location }) => {
    roleGuards.managerAndAbove(location.pathname)
  },
  component: CommonLayout,
});
