import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";


export const useGetModerators = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.GET_MODERATOR_LIST, {
                params,
            });
            return response?.data ?? {}; // return [] or {} as a fallback

        },
        queryKey: ["moderator-list"],
    });


export const addModerator = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.ADD_MODERATOR, payload);
        },
    });

export const updateModerator = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.put(API_ENDPOINTS.UPDATE_MODERATOR, payload);
        },
    });

export const modStatusChange = () =>
    useMutation({
        mutationFn: async (id: any) => {
            return await apiClient.post(`${API_ENDPOINTS.MOD_STATUS}/${id}`);
        },
    });


export const getModDetails = (id: any = {}) =>
    useQuery({
        queryFn: async () => {
            if (typeof id === 'string') {
                const response = await apiClient.get(`${API_ENDPOINTS.MOD_VIEW}/${id}`)
                return response?.data ?? {}; // return [] or {} as a fallback
            }
            return {}

        },
        queryKey: ["moderator-details", id],
        enabled: !!id
    });

