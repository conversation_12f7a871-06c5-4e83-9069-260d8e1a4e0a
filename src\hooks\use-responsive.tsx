import * as React from 'react'

// Tailwind CSS breakpoints
const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

type Breakpoint = keyof typeof BREAKPOINTS

export function useResponsive() {
  const [windowSize, setWindowSize] = React.useState<{
    width: number
    height: number
  }>({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  })

  React.useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    window.addEventListener('resize', handleResize)
    handleResize()

    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const isBreakpoint = React.useCallback(
    (breakpoint: Breakpoint) => {
      return windowSize.width >= BREAKPOINTS[breakpoint]
    },
    [windowSize.width]
  )

  const isBetween = React.useCallback(
    (min: Breakpoint, max: Breakpoint) => {
      return (
        windowSize.width >= BREAKPOINTS[min] &&
        windowSize.width < BREAKPOINTS[max]
      )
    },
    [windowSize.width]
  )

  const isBelow = React.useCallback(
    (breakpoint: Breakpoint) => {
      return windowSize.width < BREAKPOINTS[breakpoint]
    },
    [windowSize.width]
  )

  return {
    windowSize,
    isBreakpoint,
    isBetween,
    isBelow,
    isMobile: isBelow('sm'),
    isTablet: isBetween('sm', 'lg'),
    isDesktop: isBreakpoint('lg'),
    isSmall: isBelow('md'),
    isMedium: isBetween('md', 'xl'),
    isLarge: isBreakpoint('xl'),
  }
}

// Hook for responsive values
export function useResponsiveValue<T>(values: {
  mobile?: T
  tablet?: T
  desktop?: T
  default: T
}): T {
  const { isMobile, isTablet, isDesktop } = useResponsive()

  if (isMobile && values.mobile !== undefined) {
    return values.mobile
  }
  if (isTablet && values.tablet !== undefined) {
    return values.tablet
  }
  if (isDesktop && values.desktop !== undefined) {
    return values.desktop
  }
  return values.default
}

// Hook for responsive grid columns
export function useResponsiveColumns(config: {
  mobile: number
  tablet: number
  desktop: number
}): number {
  return useResponsiveValue({
    mobile: config.mobile,
    tablet: config.tablet,
    desktop: config.desktop,
    default: config.mobile,
  })
}
