import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";
import { Personality } from "../data/types";

const ActionCell = ({ row }: { row: any }) => {
    const navigate = useNavigate();
    const personality = row.original;

    return (
        <div className="flex items-center space-x-2">
            <Button
                variant="outline"
                size="sm"
                onClick={() => navigate({ to: `${END_POINTS.PERSONALITIES}/update/${personality.id}` })}
            >
                <Edit className="h-4 w-4" />
            </Button>
            <Button
                variant="outline"
                size="sm"
                onClick={() => {
                    // Handle delete - you can add confirmation dialog here
                    if (confirm("Are you sure you want to delete this personality?")) {
                        // Call delete API
                    }
                }}
            >
                <Trash2 className="h-4 w-4" />
            </Button>
        </div>
    );
};

export const columns: ColumnDef<Personality>[] = [
    {
        accessorKey: "serialNumber",
        header: "S.No",
        meta: {
            className: "w-16",
        },
    },
    {
        accessorKey: "name",
        header: "Name",
        meta: {
            className: "w-48",
        },
    },
    {
        accessorKey: "items",
        header: "Languages",
        cell: ({ row }) => {
            const items = row.original.items || [];
            return (
                <div className="flex flex-wrap gap-1">
                    {items.map((item, index) => (
                        <span
                            key={index}
                            className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800"
                        >
                            {item.languageCode}
                        </span>
                    ))}
                </div>
            );
        },
        meta: {
            className: "w-64",
        },
    },
    {
        accessorKey: "createdAt",
        header: "Created At",
        cell: ({ row }) => {
            const date = row.original.createdAt;
            return date ? new Date(date).toLocaleDateString() : "-";
        },
        meta: {
            className: "w-32",
        },
    },
    {
        id: "actions",
        header: "Actions",
        cell: ActionCell,
        meta: {
            className: "w-32",
        },
    },
];
