export interface Personality {
    id: string;
    name: string;
    items: PersonalityItem[];
    createdAt?: string;
    updatedAt?: string;
}

export interface PersonalityItem {
    id: string;
    languageCode: string;
    message: string;
}

export interface PersonalityFormData {
    [key: string]: string;
}

export interface PersonalityApiResponse {
    personalities: Personality[];
    meta: {
        page: number;
        limit: number;
        total: number;
        pages: number;
    };
}
