"use client";

import { Main } from "@/components/layout/main";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useEffect } from "react";
import { toast } from "sonner";
import { useAuthStore } from "@/stores/authStore";
import { affiliateCreateSchema, AffiliateFormValues } from "./data/schema";
import { createAffiliateApi, getAffiliateDetails, updateAffiliateApi, getCountryList } from "./api";
import { END_POINTS } from "./utils/constant";

export default function CreateAffiliate() {
    const navigate = useNavigate();
    const { auth: { setShowSpinner } } = useAuthStore((state) => state);
    const { affiliateId } = useParams({ strict: false });

    const { mutateAsync: createAffiliateMutation } = createAffiliateApi();
    const { mutateAsync: updateAffiliateMutation } = updateAffiliateApi();
    const { data = {} } = getAffiliateDetails(affiliateId);
    const { data: countryList } = getCountryList();

    const form = useForm<AffiliateFormValues>({
        resolver: zodResolver(affiliateCreateSchema),
        defaultValues: {
            firstName: "",
            lastName: "",
            email: "",
            password: "",
            country: "",
            city: "",
            nickname: "",
            phone: "",
            company: "",
            skype: "",
            currentWebsites: "",
            marketingMethods: "",
            chargebackRate: "",
            minimumChargeback: "",
            maximumChargeback: "",
            postbackLink: "",
            canSeeAffiliateClientInfo: "yes",
            accessAffiliateClientMessages: "no",
            receiveSalesEmail: "no",
            assignAsOwner: "no",
        },
    });

    const { control, handleSubmit, reset, getValues } = form;

    useEffect(() => {
        if (data && data.affiliate) {
            const affiliate = data.affiliate;
            reset({
                firstName: affiliate.firstName || "",
                lastName: affiliate.lastName || "",
                email: affiliate.email || "",
                password: "",
                // store id as string in form state
                country: affiliate.country ? String(affiliate.country) : "",
                city: affiliate.city || "",
                nickname: affiliate.nickname || "",
                phone: affiliate.phone || "",
                company: affiliate.company || "",
                skype: affiliate.skype || "",
                currentWebsites: affiliate.currentWebsites || "",
                marketingMethods: affiliate.marketingMethods || "",
                chargebackRate: affiliate.chargebackRate || "",
                minimumChargeback: affiliate.minimumChargeback || "",
                maximumChargeback: affiliate.maximumChargeback || "",
                postbackLink: affiliate.postbackLink || "",
                canSeeAffiliateClientInfo: affiliate.canSeeAffiliateClientInfo || "yes",
                accessAffiliateClientMessages: affiliate.accessAffiliateClientMessages || "no",
                receiveSalesEmail: affiliate.receiveSalesEmail || "no",
                assignAsOwner: affiliate.assignAsOwner || "no",
            });
        }
    }, [data, reset]);

    const onSubmit = async (values: AffiliateFormValues) => {
        try {
            setShowSpinner(true);
            // ensure country is sent as numeric id
            const payload = {
                ...values,
                country: values.country ? Number(values.country) : undefined,
            };
            if (affiliateId) {
                await updateAffiliateMutation({ id: affiliateId, data: payload });
                toast.success("Affiliate updated successfully!");
            } else {
                await createAffiliateMutation(payload);
                toast.success("Affiliate created successfully!");
            }
            navigate({ to: END_POINTS.AFFILIATES });
        } catch (error) {
            console.error("Error submitting form:", error);
        } finally {
            setShowSpinner(false);
        }
    };

    return (
        <Main>
            <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
                    {affiliateId ? "Update Affiliate" : "Create Affiliate"}
                </h1>
                <p className="text-muted-foreground">
                    {affiliateId ? "Update affiliate information." : "Create a new affiliate account."}
                </p>
            </div>
            <Separator className="my-4 lg:my-3" />

            <Form {...form}>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                    <Card>
                        <CardContent className="pt-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {/* Basic Information */}
                                <FormField
                                    control={control}
                                    name="firstName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>First Name</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter first name" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="lastName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Last Name</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter last name" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="email"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Email</FormLabel>
                                            <FormControl>
                                                <Input {...field} type="email" placeholder="Enter email address" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="password"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Password</FormLabel>
                                            <FormControl>
                                                <Input {...field} type="password" placeholder="Enter password" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="country"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Country</FormLabel>
                                            <Select onValueChange={field.onChange} value={field.value}>
                                                <FormControl>
                                                    <SelectTrigger style={{ width: "auto" }}>
                                                        <SelectValue placeholder="Select Country">
                                                            {field.value
                                                                ? countryList?.country?.find((c: any) => String(c.id) === String(field.value))?.name
                                                                : "Select Country"}
                                                        </SelectValue>
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {countryList?.country?.map((c: any) => (
                                                        <SelectItem key={c.id} value={String(c.id)}>
                                                            {c.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="city"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>City</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter a location" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="nickname"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Nickname</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter nickname" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="phone"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Phone</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter phone number" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="company"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Company</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter company name" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="skype"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Skype</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter Skype username" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="pt-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <FormField
                                    control={control}
                                    name="currentWebsites"
                                    render={({ field }) => (
                                        <FormItem className="md:col-span-2">
                                            <FormLabel>Current Websites</FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    {...field}
                                                    placeholder="Enter current websites"
                                                    className="resize-none"
                                                    rows={3}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="marketingMethods"
                                    render={({ field }) => (
                                        <FormItem className="md:col-span-2">
                                            <FormLabel>Marketing Methods</FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    {...field}
                                                    placeholder="Enter marketing methods"
                                                    className="resize-none"
                                                    rows={3}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="chargebackRate"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Chargeback Rate (%)</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter chargeback rate" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="minimumChargeback"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Minimum Chargeback</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter minimum chargeback" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="maximumChargeback"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Maximum Chargeback</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter maximum chargeback" />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="postbackLink"
                                    render={({ field }) => (
                                        <FormItem className="md:col-span-2">
                                            <FormLabel>Postback Link</FormLabel>
                                            <FormControl>
                                                <Input {...field} placeholder="Enter postback link" />
                                            </FormControl>
                                            <p className="text-sm text-muted-foreground">
                                                Info: Use only when affiliate have his own tracking link. Ex:- https://tremendio.scaleo-up.com/track/goal-by-click-id?click_id=&#123;CLICK_ID&#125;
                                            </p>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="pt-6">
                            <div className="space-y-4">
                                <FormField
                                    control={control}
                                    name="canSeeAffiliateClientInfo"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Can See Affiliate Client Info.?</FormLabel>
                                            <FormControl>
                                                <RadioGroup
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                    className="flex flex-row space-x-4"
                                                >
                                                    <div className="flex items-center space-x-2">
                                                        <RadioGroupItem value="yes" id="canSee-yes" />
                                                        <label htmlFor="canSee-yes">Yes</label>
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <RadioGroupItem value="no" id="canSee-no" />
                                                        <label htmlFor="canSee-no">No</label>
                                                    </div>
                                                </RadioGroup>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="accessAffiliateClientMessages"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Access Affiliate Client Messages?</FormLabel>
                                            <FormControl>
                                                <RadioGroup
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                    className="flex flex-row space-x-4"
                                                >
                                                    <div className="flex items-center space-x-2">
                                                        <RadioGroupItem value="yes" id="access-yes" />
                                                        <label htmlFor="access-yes">Yes</label>
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <RadioGroupItem value="no" id="access-no" />
                                                        <label htmlFor="access-no">No</label>
                                                    </div>
                                                </RadioGroup>
                                            </FormControl>
                                            <p className="text-sm text-muted-foreground">
                                                Info: Give access to see message history of affiliate clients (Customers).
                                            </p>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="receiveSalesEmail"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Receive Sales Email?</FormLabel>
                                            <FormControl>
                                                <RadioGroup
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                    className="flex flex-row space-x-4"
                                                >
                                                    <div className="flex items-center space-x-2">
                                                        <RadioGroupItem value="yes" id="sales-yes" />
                                                        <label htmlFor="sales-yes">Yes</label>
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <RadioGroupItem value="no" id="sales-no" />
                                                        <label htmlFor="sales-no">No</label>
                                                    </div>
                                                </RadioGroup>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={control}
                                    name="assignAsOwner"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Assign As Owner?</FormLabel>
                                            <FormControl>
                                                <RadioGroup
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                    className="flex flex-row space-x-4"
                                                >
                                                    <div className="flex items-center space-x-2">
                                                        <RadioGroupItem value="yes" id="owner-yes" />
                                                        <label htmlFor="owner-yes">Yes</label>
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <RadioGroupItem value="no" id="owner-no" />
                                                        <label htmlFor="owner-no">No</label>
                                                    </div>
                                                </RadioGroup>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    <div className="flex justify-end">
                        <Button type="submit" className="bg-green-600 hover:bg-green-700">
                            {affiliateId ? "Update" : "Create"}
                        </Button>
                    </div>
                </form>
            </Form>
        </Main>
    );
}
