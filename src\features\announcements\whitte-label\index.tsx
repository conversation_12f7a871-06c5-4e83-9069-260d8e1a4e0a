import { CONSTANTS } from "@/features/members/utils/constant";
import { getActiveAnnouncementsApi, getAllAnnouncementsApi, setPastAnnouncement } from "../api";
import { AnnouncementCard } from "../components/active-announcements";
import PostGrid from "../components/past-grid";

export default function WhiteLabelComponent() {
    const { data: { announcements = [] } = {}, refetch } = getActiveAnnouncementsApi({ type: CONSTANTS.WHITE_LABEL });
    const { data: { announcement: pastAnnouncements = [] } = {}, refetch: refetchAll } = getAllAnnouncementsApi({ type: CONSTANTS.WHITE_LABEL });

    const { mutateAsync: setPastAnnouncementMutation } = setPastAnnouncement();

    const sentAnnouncementPast = async (_isChecked: boolean, id: any) => {
        try {
            const response: any = await setPastAnnouncementMutation(id);
            refetch();
            refetchAll();
        } catch (error: any) {
            console.log(error);
        }
    };
    return (
        <div className="">
            <div className="flex gap-4 flex-wrap">
                {announcements.map((item: any) => (

                    <AnnouncementCard
                        key={item.id}
                        name={item.name}
                        email={item.email}
                        content={item.content}
                        date={item.updatedAt}
                        id={item.id}
                        defaultIsPast={item.isPast}
                        onToggleChange={sentAnnouncementPast}
                    />
                ))}
            </div>
            {
                pastAnnouncements.length > 0 &&
                <div className="space-y-2 flex flex-col gap-2" style={{ marginTop: "30px" }}>
                    <label className="font-medium ml-1">Past Announcements</label>
                    <PostGrid data={pastAnnouncements} sentAnnouncementPast={sentAnnouncementPast} />
                </div>
            }
        </div>
    );
}