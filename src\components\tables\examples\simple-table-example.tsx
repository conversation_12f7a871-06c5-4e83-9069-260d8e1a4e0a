/**
 * Simple example demonstrating the basic usage of the DataTable component
 * This can be used as a starting point for new table implementations
 */

import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/tables'
import { DataTableColumnHeader } from '@/components/data-table-column-header'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

// Simple data type
interface Product {
  id: string
  name: string
  category: string
  price: number
  status: 'in-stock' | 'out-of-stock' | 'discontinued'
  createdAt: string
}

// Simple column definitions
export const productColumns: ColumnDef<Product>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Product Name' />
    ),
    cell: ({ row }) => (
      <div className='font-medium'>{row.getValue('name')}</div>
    ),
  },
  {
    accessorKey: 'category',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Category' />
    ),
    cell: ({ row }) => <div>{row.getValue('category')}</div>,
  },
  {
    accessorKey: 'price',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Price' />
    ),
    cell: ({ row }) => {
      const price = parseFloat(row.getValue('price'))
      const formatted = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'EUR',
      }).format(price)
      return <div className='text-right font-medium'>{formatted}</div>
    },
    meta: { className: 'text-right' },
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string
      return (
        <Badge
          variant={
            status === 'in-stock'
              ? 'default'
              : status === 'out-of-stock'
              ? 'destructive'
              : 'secondary'
          }
        >
          {status.replace('-', ' ')}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const product = row.original
      return (
        <div className='flex items-center space-x-2'>
          <Button variant='ghost' size='sm'>
            Edit
          </Button>
          <Button variant='ghost' size='sm'>
            Delete
          </Button>
        </div>
      )
    },
    enableSorting: false,
    enableHiding: false,
  },
]

// Sample data
export const sampleProducts: Product[] = [
  {
    id: '1',
    name: 'Wireless Headphones',
    category: 'Electronics',
    price: 99.99,
    status: 'in-stock',
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    name: 'Coffee Mug',
    category: 'Home & Garden',
    price: 12.50,
    status: 'in-stock',
    createdAt: '2024-01-16T14:20:00Z',
  },
  {
    id: '3',
    name: 'Laptop Stand',
    category: 'Electronics',
    price: 45.00,
    status: 'out-of-stock',
    createdAt: '2024-01-17T09:15:00Z',
  },
  {
    id: '4',
    name: 'Desk Lamp',
    category: 'Home & Garden',
    price: 35.99,
    status: 'in-stock',
    createdAt: '2024-01-18T16:45:00Z',
  },
  {
    id: '5',
    name: 'Old Phone',
    category: 'Electronics',
    price: 199.99,
    status: 'discontinued',
    createdAt: '2024-01-19T11:30:00Z',
  },
]

// Simple table component
interface SimpleTableProps {
  data?: Product[]
}

export function SimpleTableExample({ data = sampleProducts }: SimpleTableProps) {
  return (
    <div className='space-y-4'>
      <div className='flex items-center justify-between'>
        <h2 className='text-2xl font-bold tracking-tight'>Products</h2>
        <Button>Add Product</Button>
      </div>
      
      <DataTable
        columns={productColumns}
        data={data}
        searchKey='name'
        searchPlaceholder='Filter products...'
        filters={[
          {
            column: 'status',
            title: 'Status',
            options: [
              { label: 'In Stock', value: 'in-stock' },
              { label: 'Out of Stock', value: 'out-of-stock' },
              { label: 'Discontinued', value: 'discontinued' },
            ],
          },
          {
            column: 'category',
            title: 'Category',
            options: [
              { label: 'Electronics', value: 'Electronics' },
              { label: 'Home & Garden', value: 'Home & Garden' },
            ],
          },
        ]}
        enableRowSelection={false}
        pageSize={5}
      />
    </div>
  )
}

// Minimal table without toolbar
export function MinimalTableExample({ data = sampleProducts }: SimpleTableProps) {
  return (
    <DataTable
      columns={productColumns.slice(0, 4)} // Exclude actions column
      data={data}
      showToolbar={false}
      showPagination={false}
      enableRowSelection={false}
      className='border-0'
    />
  )
}

/**
 * Usage examples:
 * 
 * // Full-featured table
 * <SimpleTableExample />
 * 
 * // Minimal table for cards or embedded use
 * <MinimalTableExample data={products.slice(0, 3)} />
 * 
 * // Custom data
 * <SimpleTableExample data={myProducts} />
 */
