import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./chat-header";
import { MessagesList } from "./messages-list";
import { ChatInput } from "./chat-input";
import { ChatPanelProps } from "./types";

export function ChatPanel({ 
  messages, 
  stats, 
  onHold, 
  onReplyStay, 
  onReply, 
  onLobby, 
  onProblem, 
  onSendMessage 
}: ChatPanelProps) {
  return (
    <div className="flex flex-col rounded-2xl">
      <ChatHeader 
        stats={stats}
        onHold={onHold}
      />
      
      <MessagesList messages={messages} />
      
      <ChatInput
        onReplyStay={onReplyStay}
        onReply={onReply}
        onLobby={onLobby}
        onProblem={onProblem}
        onSendMessage={onSendMessage}
      />
    </div>
  );
}
