import SharedCommonLayout from '@/components/layout/shared-common-layout'
import Dashboard from '@/features/dashboard'
import List from '@/features/sessions'
import { useAuthStore } from '@/stores/authStore'
import { createFileRoute } from '@tanstack/react-router'

export const Route = createFileRoute('/_authenticated/')({
  component: IndexPage,
})

function IndexPage() {
  const user = useAuthStore((state) => state.auth)
  if (!user) {
    return <div>Please log in</div> // or redirect to login
  }


  if (['superadmin', 'admin'].some(role => user.roles.includes(role))) {
    return <Dashboard />
  }

  if (user.roles.includes('chat-mod')) {
    return (
      <SharedCommonLayout>
        <List />
      </SharedCommonLayout>)
  }

  return <div>Unauthorized</div>
}
