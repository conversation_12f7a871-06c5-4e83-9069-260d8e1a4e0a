import { faker } from '@faker-js/faker'
import { ChatGroup } from './schema'

const moderatorNames = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>'
]

const groupNames = [
  'VIP Members Chat',
  'General Discussion',
  'Premium Support',
  'New Members Welcome',
  'Dating Tips & Advice',
  'Success Stories',
  'Technical Support',
  'Community Guidelines',
  'Events & Meetups',
  'Feedback & Suggestions',
  'International Members',
  'Local Connections',
  'Safety & Security',
  'Feature Updates',
  'Weekend Social'
]

export const chatGroups: ChatGroup[] = Array.from({ length: 15 }, (_, index) => {
  return {
    id: faker.string.uuid(),
    serialNumber: index + 1,
    moderator: faker.helpers.arrayElement(moderatorNames),
    group: faker.helpers.arrayElement(groupNames),
    status: faker.helpers.arrayElement(['active', 'inactive'] as const),
    createdAt: faker.date.past(),
    updatedAt: faker.date.recent(),
  }
})
