import { RedsoftSidebar } from "@/assets/images";
import { END_POINTS } from "@/features/members/utils/constant";
import { ActivityLogIcon, SpeakerLoudIcon } from "@radix-ui/react-icons";
import {
  IconAffiliate,
  IconChartInfographic,
  IconInvoice,
  IconLayoutDashboard,
  IconMessages,
  IconPalette,
  IconSettings,
  IconUserCog,
} from "@tabler/icons-react";
import {
  BadgeDollarSign, Bot, ChartBarIncreasing,
  Contact, DollarSign, FolderOpen, Gift, Globe, Image,
  HeartHandshake, LanguagesIcon, List,
  Package,
  PersonStandingIcon,
  Plus, ShieldMinus, ShieldPlus, Smile, Sofa, UserCheck2Icon, UserCog, Users,
  Users2,
  MessageCircleDashed,
  Settings2Icon,
  GroupIcon
} from "lucide-react";

export const sidebarData: any = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  teams: [
    {
      name: "Redsoft Admin",
      logo: RedsoftSidebar,
      plan: "",
    },
    // {
    //   name: "Acme Inc",
    //   logo: GalleryVerticalEnd,
    //   plan: "Enterprise",
    // },
    // {
    //   name: "Acme Corp.",
    //   logo: AudioWaveform,
    //   plan: "Startup",
    // },
  ],
  navGroups: [
    {
      title: "",
      items: [
        {
          title: "Dashboard",
          url: "/",
          icon: IconLayoutDashboard,
          roles: ['superadmin', 'admin', 'manager']
        },

        // ----------------Super admins sidebar options starts here-----------------
        {
          title: "Sessions",
          icon: Users2,
          roles: ['chat-mod'],
          items: [
            {
              title: "Lobby",
              icon: Sofa,
              url: `${END_POINTS.LOBY}`,
              roles: ['chat-mod']
            }, {
              title: "Active Sessions",
              url: "/active-sessions",
              icon: PersonStandingIcon,
              roles: ['chat-mod']
            },
          ]
        },

        {
          title: "General",
          icon: Users2,
          roles: ['chat-mod'],
          items: [
            {
              title: "Announcements",
              url: "/announcements",
              icon: SpeakerLoudIcon,
              roles: ['chat-mod']
            },
            {
              title: "Private Messages",
              icon: MessageCircleDashed,
              url: `${END_POINTS.PRIVATE_MSGS}`,
              roles: ['chat-mod']
            }, {
              title: "Dashboard",
              url: "/active-sessions",
              icon: IconLayoutDashboard,
              roles: ['chat-mod']
            },
            {
              title: "Account Settings",
              url: "/active-sessions",
              icon: Settings2Icon,
              roles: ['chat-mod']
            },
            {
              title: "Billing Invoices",
              url: "/active-sessions",
              icon: IconInvoice,
              roles: ['chat-mod']
            },
            {
              title: "Shift Plan",
              url: "/active-sessions",
              icon: GroupIcon,
              roles: ['chat-mod']
            },
          ]
        },
        // ----------------Super admins sidebar options ends here -----------------
        {
          title: "Settings",
          icon: IconSettings,
          roles: ['superadmin', 'admin'],
          url: "/settings"
        }, {
          title: "Currencies",
          url: "/currencies",
          icon: DollarSign,
          roles: ['superadmin', 'admin', 'manager']
        }, {
          title: "Announcements",
          url: "/announcements",
          icon: SpeakerLoudIcon,
          roles: ['superadmin', 'admin', 'manager']
        },
        {
          title: "Admin Login Activity",
          url: "/admin-login-activity",
          icon: ActivityLogIcon,
          roles: ['superadmin', 'admin']
        },
        {
          title: "User-Management",
          icon: Users,
          roles: ['superadmin', 'admin', 'manager'],
          items: [
            {
              title: "Moderators",
              icon: IconSettings,
              roles: ['superadmin', 'admin'],
              items: [
                {
                  title: "List",
                  url: "/moderators",
                  icon: IconUserCog,
                  roles: ['superadmin', 'admin']
                },
                {
                  title: "All Reply Messages",
                  url: "/moderators/reply-messages",
                  icon: IconMessages,
                  roles: ['superadmin', 'admin']
                },
                // {
                //   title: "Quality Analysis",
                //   url: "/settings/appearance",
                //   icon: IconPalette,
                // },
                {
                  title: "Login Activity",
                  url: "/moderators/login-activity",
                  icon: ActivityLogIcon,
                  roles: ['superadmin', 'admin']
                },
                {
                  title: "Domains",
                  url: "/moderators/domain",
                  icon: Globe,
                  roles: ['superadmin', 'admin']
                },
              ],
            },

            {
              title: "Members",
              icon: IconSettings,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/members",
                  icon: IconUserCog,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Profile",
                  // url: "/members/profile",
                  icon: IconPalette,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Models",
              icon: IconSettings,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/models",
                  icon: IconUserCog,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Profile",
                  // url: "/models/profile",
                  icon: IconPalette,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
          ],
        },
        {
          title: "Manage Profile Values",
          icon: IconSettings,
          roles: ['superadmin', 'admin', 'manager'],
          items: [
            {
              title: "Personalities",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/personalities",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/personalities/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Relationship Status",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/relationship-status",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/relationship-status/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Ethnicities",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/ethnicities",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/ethnicities/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Religions",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/religions",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/religions/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Hair Colors",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/hair-colors",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/hair-colors/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Appearances",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/appearances",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/appearances/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Eye Colors",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/eye-colors",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/eye-colors/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Body Types",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/body-types",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/body-types/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Star Signs",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/star-signs",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/star-signs/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Smoking Habits",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/smoking-habits",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/smoking-habits/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Drinking Habits",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/drinking-habits",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/drinking-habits/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Best Features",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/best-features",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/best-features/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Body Arts",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/body-arts",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/body-arts/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Interests",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/interests",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/interests/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
            {
              title: "Sexual Orientations",
              icon: IconUserCog,
              roles: ['superadmin', 'admin', 'manager'],
              items: [
                {
                  title: "List",
                  url: "/profile-values/sexual-orientations",
                  icon: List,
                  roles: ['superadmin', 'admin', 'manager']
                },
                {
                  title: "Add",
                  url: "/profile-values/sexual-orientations/add",
                  icon: Plus,
                  roles: ['superadmin', 'admin', 'manager']
                },
              ],
            },
          ],
        },
        {
          title: "Domains",
          icon: Globe,
          roles: ['superadmin', 'admin'],
          items: [
            {
              title: "Waiting Approval List",
              icon: ShieldMinus,
              roles: ['superadmin', 'admin']
            },

            {
              title: "Active Domain List",
              icon: ShieldPlus,
              roles: ['superadmin', 'admin']
            },
          ],
        },
        {
          title: "Country Languages",
          icon: LanguagesIcon,
          roles: ['superadmin', 'admin'],
          items: [
            {
              title: "List",
              icon: List,
              roles: ['superadmin', 'admin']
            },

            {
              title: "Add",
              icon: Plus,
              roles: ['superadmin', 'admin']
            },
          ],
        },
        {
          title: "Affiliates",
          icon: IconAffiliate,
          roles: ['superadmin', 'admin', 'manager'],
          items: [
            {
              title: "New Register",
              icon: UserCog,
              url: "/affiliates/new",
              roles: ['superadmin', 'admin', 'manager']
            },

            {
              title: "Approved Affiliates",
              icon: IconSettings,
              url: "/affiliates/approved",
              roles: ['superadmin', 'admin', 'manager']
            },
            {
              title: "Add",
              icon: Plus,
              url: "/affiliates/create",
              roles: ['superadmin', 'admin']
            },
            {
              title: "Assign Customer",
              icon: UserCheck2Icon,
              roles: ['superadmin', 'admin', 'manager']
            },
          ],
        },
        {
          title: "Affiliate Offers",
          icon: IconAffiliate,
          roles: ['superadmin', 'admin', 'manager'],
          items: [
            {
              title: "List",
              icon: List,
              url: `/${END_POINTS.AFFILIATE_OFFERS}/`,
              roles: ['superadmin', 'admin', 'manager']
            },

            {
              title: "Add",
              icon: Plus,
              url: `/${END_POINTS.AFFILIATE_OFFERS}/add`,
              roles: ['superadmin', 'admin']
            },
          ],
        },
        {
          title: "Statistics",
          icon: IconChartInfographic,
          roles: ['superadmin', 'admin', 'manager'],
          items: [
            {
              title: "Messages",
              icon: IconMessages,
              roles: ['superadmin', 'admin', 'manager']
            },

            {
              title: "Sales",
              icon: BadgeDollarSign,
              roles: ['superadmin', 'admin', 'manager']
            },
            {
              title: "Sales Chart",
              icon: ChartBarIncreasing,
              roles: ['superadmin', 'admin', 'manager']
            },
            {
              title: "WL Sales",
              icon: ChartBarIncreasing,
              roles: ['superadmin', 'admin', 'manager']
            },
          ],
        },
        {
          title: "Packages",
          icon: Package,
          roles: ['superadmin', 'admin', 'manager'],
          // url: END_POINTS.PACKAGES,
        },
        {
          title: "Resources",
          icon: FolderOpen,
          roles: ['superadmin', 'admin', 'manager'],
          items: [
            {
              title: "Smilies",
              icon: Smile,
              url: END_POINTS.SMILIES,
              roles: ['superadmin', 'admin', 'manager']
            },
            {
              title: "Gifts",
              icon: Gift,
              url: END_POINTS.GIFTS,
              roles: ['superadmin', 'admin', 'manager']
            },
          ],
        },
        {
          title: "Flirt Messages",
          icon: HeartHandshake,
          url: END_POINTS.FLIRT_MESSAGES,
          roles: ['superadmin', 'admin', 'manager']
        },
        {
          title: "Bot Messages",
          icon: Bot,
          url: END_POINTS.BOT_MESSAGES,
          roles: ['superadmin', 'admin', 'manager']
          // items: [
          //   {
          //     title: "List",
          //     icon: List,
          //   },

          //   {
          //     title: "Add",
          //     icon: Plus,
          //   },
          // ],
        },
        {
          title: "Contact us",
          icon: Contact,
          url: "/contact-us",
          roles: ['superadmin', 'admin', 'manager']
        },
      ],
    },
  ],
};
