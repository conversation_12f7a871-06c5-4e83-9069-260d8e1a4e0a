import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";


export const getAllAnnouncementsApi = (params: any) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(`${API_ENDPOINTS.ANNOUNCEMENT}?type=${params.type}`);
            return response?.data ?? {};
        },
        queryKey: ["get-all-announcement", params.type],
    });


export const createAnnouncementApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(`${API_ENDPOINTS.ANNOUNCEMENT}`, payload);
        },
    });

export const setPastAnnouncement = () =>
    useMutation({
        mutationFn: async (id: any) => {
            return await apiClient.put(`${API_ENDPOINTS.ANNOUNCEMENT}/${id}/set-past`);
        },
    });

export const getActiveAnnouncementsApi = (params: any) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(`${API_ENDPOINTS.ANNOUNCEMENT_ACTIVE_LIST}?type=${params.type}`);
            return response?.data ?? {};
        },
        queryKey: ["active-announcement-list", params.type],
    });


