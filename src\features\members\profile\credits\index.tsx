import { use<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import { members } from '../../data/members'
import ProfileCard from '../components/profile-card'
import { IconArrowLeft } from '@tabler/icons-react'
import { Main } from '@/components/layout/main'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

// Mock credit history data
const creditHistory = [
  {
    id: '1',
    date: 'Feb 12, 2025 10:30 AM',
    type: 'Purchase',
    amount: '+50',
    description: 'Credit package purchase',
    status: 'completed'
  },
  {
    id: '2',
    date: 'Feb 10, 2025 3:15 PM',
    type: 'Usage',
    amount: '-5',
    description: 'Message sent to <PERSON>',
    status: 'completed'
  },
  {
    id: '3',
    date: 'Feb 08, 2025 8:45 AM',
    type: 'Usage',
    amount: '-3',
    description: 'Photo view',
    status: 'completed'
  },
  {
    id: '4',
    date: 'Feb 05, 2025 2:20 PM',
    type: 'Refund',
    amount: '+10',
    description: 'Refund for cancelled subscription',
    status: 'completed'
  },
  {
    id: '5',
    date: 'Feb 03, 2025 11:00 AM',
    type: 'Purchase',
    amount: '+100',
    description: 'Premium credit package',
    status: 'completed'
  }
]

export default function MemberCreditHistory() {
  const { memberId } = useParams({ from: '/_authenticated/members/profile/$memberId/credits' })

  // Find the member by ID (in a real app, this would be an API call)
  const member = members.find(m => m.id === memberId)

  if (!member) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Member not found</p>
      </div>
    )
  }

  return (
    <Main>
      <div className=" mx-auto">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
          <Link to="/members" className="flex items-center gap-1 hover:text-foreground">
            <IconArrowLeft className="h-4 w-4" />
            Members List
          </Link>
          <span>/</span>
          <Link to="/members/profile/$memberId" params={{ memberId }} className="hover:text-foreground">
            View Profile
          </Link>
          <span>/</span>
          <span className="text-foreground">Credit History</span>
        </div>

        {/* Main Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Side - Profile Card */}
          <div className="lg:col-span-1">
            <ProfileCard member={member} />
          </div>

          {/* Right Side - Credit History */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Credit History</CardTitle>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>Current Balance: <strong className="text-foreground">{member.credits} credits</strong></span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {creditHistory.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-1">
                          <Badge variant={transaction.type === 'Purchase' || transaction.type === 'Refund' ? 'default' : 'secondary'}>
                            {transaction.type}
                          </Badge>
                          <span className="text-sm text-muted-foreground">{transaction.date}</span>
                        </div>
                        <p className="text-sm font-medium">{transaction.description}</p>
                      </div>
                      <div className="text-right">
                        <span className={`text-lg font-bold ${transaction.amount.startsWith('+') ? 'text-green-600' : 'text-red-600'
                          }`}>
                          {transaction.amount}
                        </span>
                        <div className="text-xs text-muted-foreground capitalize">
                          {transaction.status}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Main>
  )
}
