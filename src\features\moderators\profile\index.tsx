import { use<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import { moderators } from '../data/moderators'
import ProfileCard from './components/profile-card'
import ProfileStats from './components/profile-stats'
import { IconArrowLeft } from '@tabler/icons-react'
import ActivityChart from './components/activity-chart'
import { Main } from '@/components/layout/main'
import { getModDetails } from '../api'

export default function ModeratorProfile() {
  const { moderatorId } = useParams({ from: '/_authenticated/moderators/profile/$moderatorId' })

  const { data = {} }: any = getModDetails(moderatorId)

  // // Find the moderator by ID (in a real app, this would be an API call)
  // const moderator = moderators.find(m => m.id === moderatorId)

  // if (!moderator) {
  //   return (
  //     <div className="flex items-center justify-center h-64">
  //       <p className="text-muted-foreground">Moderator not found</p>
  //     </div>
  //   )
  // }

  return (
    <Main>
      <div className="mx-auto">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
          <Link to="/moderators" className="flex items-center gap-1 hover:text-foreground">
            <IconArrowLeft className="h-4 w-4" />
            Moderators
          </Link>
          <span>/</span>
          <span className="text-foreground">Moderator Profile</span>
        </div>

        {/* Main Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 ">
          {/* Left Side - Profile Card */}
          <div className="lg:col-span-1">
            <ProfileCard moderator={data?.user} />
          </div>

          {/* Right Side - Stats and Chart */}
          <div className="lg:col-span-3 space-y-6">
            {/* Stats Cards */}
            <ProfileStats data={data?.user} />

            {/* Activity Chart */}
            <ActivityChart />
          </div>
        </div>
      </div>
    </Main>
  )
}
