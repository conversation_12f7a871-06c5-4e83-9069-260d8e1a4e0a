import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { ModeratorLoginActivity } from '../data/schema'
import { DataTableColumnHeader } from './data-table-column-header'
import LongText from '@/components/long-text'
import { calculateLoginSession, formatTimestampToLocal } from '@/features/members/utils/utilities'

export const columns: ColumnDef<ModeratorLoginActivity>[] = [
  {
    accessorKey: 'serialNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="#" />
    ),
    cell: ({ row }) => <div className="w-[40px]">{row.getValue('serialNumber')}</div>,
    enableSorting: false,
    enableHiding: false,
    meta: {
      className: cn(
        'sticky md:table-cell left-0 z-10 rounded-tl',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted'
      ),
    },
  },
  {
    accessorKey: 'user.username',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Moderator" />
    ),
    cell: ({ row }: any) => (
      <LongText className='max-w-36'>{row?.original?.user?.username || "N/A"}</LongText>
    ),
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-6 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'loginAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Login" />
    ),
    cell: ({ row }: any) => (
      <LongText className='max-w-36'>{formatTimestampToLocal(row?.original?.loginAt) || "N/A"}</LongText>
    ),
    meta: { className: 'w-48' },
  },
  {
    accessorKey: 'logoutAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Logout" />
    ),
    cell: ({ row }: any) => (
      <LongText className='max-w-36'>{row?.original?.logoutAt ? formatTimestampToLocal(row?.original?.logoutAt) : "--"}</LongText>
    ),
    meta: { className: 'w-48' },
  },
  {
    accessorKey: 'sessionDuration',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Login Session" />
    ),
    cell: ({ row }: any) => (
      <LongText className='max-w-24'>{row.original.sessionDuration}</LongText>
      // <LongText className='max-w-24'>{calculateLoginSession(row.original.loginAt, "2025-07-07T08:26:59.904Z")}</LongText>
    ),
    enableSorting: false,
    meta: { className: 'w-32' },
  },
  {
    accessorKey: 'lastActivityTime',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Last Activity Time" />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-36'>{formatTimestampToLocal(row?.original?.lastActivityTime) ?? "N/A"}</LongText>
    ),
    meta: { className: 'w-48' },
  },
  {
    accessorKey: 'country',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="IP (Country)" />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-48'>{row.getValue('country')}</LongText>
    ),
    enableSorting: false,
    meta: { className: 'w-48' },
  },
  {
    accessorKey: 'deviceInfo',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Device Info." />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-36'>{row.getValue('deviceInfo')}</LongText>
    ),
    enableSorting: false,
    meta: { className: 'w-36' },
  },
  {
    accessorKey: 'totalMessageSent',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Total Message Sent" />
    ),
    cell: ({ row }) => (
      <div className="text-center">{row.getValue('totalMessageSent')}</div>
    ),
    enableSorting: false,
    meta: { className: 'w-32 text-center' },
  },
]
