import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import { Row } from '@tanstack/react-table'
import { IconEdit, IconTrash } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Moderator } from '../data/schema'
import { useModerators } from '../context/moderators-context'
import { useNavigate } from '@tanstack/react-router'
import { modStatusChange } from '../api'
import { useQueryClient } from '@tanstack/react-query'
import { END_POINTS } from '@/features/members/utils/constant'

interface DataTableRowActionsProps {
  row: Row<Moderator>
}

export function DataTableRowActions({ row }: DataTableRowActionsProps) {
  const { setOpen, setCurrentRow } = useModerators()
  const navigate = useNavigate()
  const { mutateAsync: statusChangeMutation } = modStatusChange();
  const queryClient = useQueryClient();

  const handleAction = async (action: any) => {
    switch (action) {
      case 'view':
        navigate({
          to: `${END_POINTS.MODERATOR_PROFILE}/$moderatorId`,
          params: { moderatorId: row.original.id },
        });
        return;

      case 'edit':
        navigate({
          to: `${END_POINTS.UPDATE_MODERATOR}/${row.original.id}`,
        });
        return;

      case 'login':
        navigate({
          to: END_POINTS.MODERATORS_LOGIN_ACTIVITY,
        });
        return;

      case 'reply':
        navigate({
          to: END_POINTS.MODERATORS_REPLY_MESSAGE,
        });
        return;

      case 'deactivate':
        const response: any = await statusChangeMutation(row.original.id);
        if (response?.success) {
          queryClient.invalidateQueries({ queryKey: ['moderator-list'] });
        }
        break;

      default:
        break;
    }

    setCurrentRow(row.original);
    setOpen(action);
  };


  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          className='data-[state=open]:bg-muted flex h-8 w-8 p-0 cursor-pointer'
        >
          <DotsHorizontalIcon className='h-4 w-4' />
          <span className='sr-only'>Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-[200px]'>
        <DropdownMenuItem onClick={() => handleAction('view')}>
          View Profile
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleAction('edit')}>
          Edit Profile Details
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleAction('login')}>
          Login Activity
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleAction('reply')}>
          Reply Messages
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleAction('transactions')}>
          Transaction History
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => handleAction('deactivate')}
          className='text-red-600'
        >
          {row?.original?.isSuspended ? "Active" : "Inactive"}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
