# Role-Based Navigation System

This document explains how the role-based navigation system works in the application.

## Overview

The application now supports role-based access control for navigation items. Users will only see navigation items they have permission to access based on their assigned roles.

## Supported Roles

The system supports the following roles:

- `superadmin` - Full access to all features
- `admin` - Administrative access to most features
- `manager` - Management access to business features
- `cashier` - Limited access to transaction-related features
- `chat-mod` - Access to chat moderation features

## How It Works

### 1. Role Storage

User roles are stored in the authentication store (`useAuthStore`) in two possible locations:
- `auth.roles` - Array of role strings
- `auth.user.role` - Array of role strings (fallback)

### 2. Navigation Filtering

The navigation is filtered using utility functions in `src/utils/role-based-navigation.ts`:

- `hasRoleAccess(userRoles, itemRoles)` - Checks if user has access to an item
- `filterNavigationItemsByRoles(items, userRoles)` - Filters navigation items
- `getFilteredSidebarData(sidebarData, userRoles)` - Filters entire sidebar

### 3. Sidebar Configuration

Navigation items in `src/components/layout/data/sidebar-data.ts` can have a `roles` property:

```typescript
{
  title: "Settings",
  icon: IconSettings,
  roles: ['superadmin', 'admin'], // Only superadmin and admin can see this
  url: "/settings"
}
```

## Role Assignments by Feature

### Dashboard
- **Access**: All users (no role restriction)

### Sessions
- **Access**: `superadmin`, `admin`, `manager`, `chat-mod`
- **Features**: Lobby, Active Sessions

### Settings
- **Access**: `superadmin`, `admin`
- **Features**: System configuration

### User Management
- **Access**: `superadmin`, `admin`, `manager`
- **Features**: 
  - Moderators (superadmin, admin only)
  - Members (superadmin, admin, manager)
  - Models (superadmin, admin, manager)

### Resources
- **Access**: `superadmin`, `admin`, `manager`
- **Features**: Smilies, GIFs, Gifts

### Statistics & Reports
- **Access**: `superadmin`, `admin`, `manager`
- **Features**: Messages, Sales, Charts

### Content Management
- **Access**: `superadmin`, `admin`, `manager`
- **Features**: Flirt Messages, Bot Messages

## Testing Role-Based Access

### Development Testing

1. **Use the Debug Panel** (Development only):
   ```typescript
   import { RoleDebug } from '@/components/debug/role-debug';
   
   // Add to any component during development
   <RoleDebug />
   ```

2. **Console Testing**:
   ```typescript
   import { testRoleAccess, logRoleAccess, TEST_ROLES } from '@/utils/role-testing';
   
   // Test specific roles
   logRoleAccess(['admin', 'manager']);
   
   // Test all predefined role combinations
   testAllRoles();
   ```

3. **Manual Role Setting**:
   ```typescript
   const { setUser } = useAuthStore();
   
   // Temporarily set roles for testing
   setUser({ ...user, role: ['admin'] }, ['admin']);
   ```

### Production Testing

1. **Backend Role Assignment**: Ensure your backend properly assigns roles to users
2. **Login Testing**: Test with different user accounts having different roles
3. **Navigation Verification**: Verify that users only see appropriate navigation items

## Adding New Role-Protected Features

### 1. Add Role to Navigation Item

```typescript
// In src/components/layout/data/sidebar-data.ts
{
  title: "New Feature",
  icon: NewIcon,
  roles: ['superadmin', 'admin'], // Define required roles
  url: "/new-feature"
}
```

### 2. Add Route Protection (Optional)

```typescript
// In route files
export const Route = createFileRoute('/new-feature')({
  beforeLoad: ({ location }) => {
    const { user, roles } = useAuthStore.getState().auth;
    const userRoles = roles || user?.role || [];
    
    if (!hasRoleAccess(userRoles, ['superadmin', 'admin'])) {
      throw redirect({ to: '/unauthorized' });
    }
  },
  component: NewFeatureComponent,
});
```

### 3. Add Component-Level Protection

```typescript
// In components
import { useAuthStore } from '@/stores/authStore';
import { hasRoleAccess } from '@/utils/role-based-navigation';

function MyComponent() {
  const { user, roles } = useAuthStore((state) => state.auth);
  const userRoles = roles || user?.role || [];
  
  if (!hasRoleAccess(userRoles, ['admin'])) {
    return <div>Access Denied</div>;
  }
  
  return <div>Protected Content</div>;
}
```

## Troubleshooting

### Navigation Items Not Showing

1. **Check User Roles**: Verify user has correct roles assigned
2. **Check Role Spelling**: Ensure role names match exactly
3. **Check Nested Items**: Parent items are hidden if no child items are accessible
4. **Check Console**: Use debug utilities to see what's being filtered

### Common Issues

1. **Empty Sidebar**: User has no roles or roles don't match any navigation items
2. **Partial Access**: User has some roles but not all required for certain features
3. **Role Mismatch**: Backend and frontend role names don't match

### Debug Commands

```javascript
// In browser console (development only)
testRoles.logRoleAccess(['admin']);
testRoles.testAllRoles();
testRoles.getRoleSummary();
```

## Best Practices

1. **Principle of Least Privilege**: Give users only the minimum roles needed
2. **Consistent Role Names**: Use the same role names across backend and frontend
3. **Graceful Degradation**: Show appropriate messages when access is denied
4. **Regular Testing**: Test role access regularly during development
5. **Documentation**: Keep role assignments documented and up to date
