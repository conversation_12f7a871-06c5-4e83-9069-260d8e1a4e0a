import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { IconSearch } from '@tabler/icons-react'
import { Member } from '../../data/schema'
import { useNavigate } from '@tanstack/react-router'
import { useParams } from '@tanstack/react-router'
import { END_POINTS } from '../../utils/constant'

interface MessageHistoryProps {
  member: Member
}

// Mock message history data - in real app this would come from API
const mockMessageHistory = [
  {
    id: '1',
    name: '<PERSON>',
    message: 'Good morning. How are you? Can we meet yesterday?',
    time: '5 min ago',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '2',
    name: '<PERSON>',
    message: 'Good morning. How are you? Can we meet yesterday?',
    time: '5 min ago',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '3',
    name: 'Iva Ryan',
    message: 'Good morning. How are you? Can we meet yesterday?',
    time: '5 min ago',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '4',
    name: 'Dennis Callis',
    message: 'Good morning. How are you? Can we meet yesterday?',
    time: '5 min ago',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '5',
    name: 'Ricky Smith',
    message: 'Good morning. How are you? Can we meet yesterday?',
    time: '5 min ago',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '6',
    name: 'Judith Rodriguez',
    message: 'Good morning. How are you? Can we meet yesterday?',
    time: '5 min ago',
    avatar: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '7',
    name: 'Mary Freund',
    message: 'Good morning. How are you? Can we meet yesterday?',
    time: '5 min ago',
    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '8',
    name: 'Lorri Warf',
    message: 'Good morning. How are you? Can we meet yesterday?',
    time: '5 min ago',
    avatar: 'https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '9',
    name: 'John Dukes',
    message: 'Good morning. How are you? Can we meet yesterday?',
    time: '5 min ago',
    avatar: 'https://images.unsplash.com/photo-1547425260-76bcadfb4f2c?w=100&h=100&fit=crop&crop=face'
  }
]

export default function MessageHistory({ member }: MessageHistoryProps) {

  const navigate = useNavigate()
  const { memberId } = useParams<any>({ from: `/_authenticated${END_POINTS.MEMBERS_PROFILE}/$memberId` })

  return (
    <Card onClick={() => navigate({ to: `${END_POINTS.MEMBERS_PROFILE}/$memberId/messages/$conversationId`, params: { memberId, conversationId: '5' } })}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg">Message History</CardTitle>
        {/* Search Input */}
        <div className="relative">
          <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search people"
            className="pl-10 bg-background"
          />
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="max-h-[600px] overflow-y-auto">
          {mockMessageHistory.map((message, index) => (
            <div
              key={message.id}
              className={`flex items-center gap-3 p-4 hover:bg-muted/50 transition-colors cursor-pointer border-b border-border last:border-b-0 ${index % 2 === 0 ? 'bg-background' : 'bg-muted/20'
                }`}
            >
              {/* Avatar */}
              <Avatar className="h-12 w-12 flex-shrink-0">
                <AvatarImage src={message.avatar} alt={message.name} />
                <AvatarFallback className="text-sm">
                  {message.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>

              {/* Message Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="font-medium text-sm text-foreground truncate">
                    {message.name}
                  </h4>
                  <span className="text-xs text-muted-foreground flex-shrink-0 ml-2">
                    {message.time}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {message.message}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="p-4 bg-muted/30 border-t">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">
              {mockMessageHistory.length} conversations
            </span>
            <span className="text-muted-foreground">
              Last activity: 5 min ago
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
