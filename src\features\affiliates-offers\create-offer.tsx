"use client"

import { Main } from '@/components/layout/main'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import { getPresignedUrl, uploadFileToS3 } from '@/features/members/api'
import { validateImageFile } from '@/features/members/utils/utilities'
import { useNavigate } from '@tanstack/react-router'
import { END_POINTS } from '@/features/members/utils/constant'
import { Plus } from 'lucide-react'

const statusOptions = ['Active', 'Archived']
const deviceTypeOptions = ['WEB', 'MOB']
const domainTypeOptions = ['discreetdating.club', 'passionhub.net', 'lovequest.org']

const offerSchema = z.object({
    domain: z.string().min(1, 'Domain is required'),
    title: z.string().min(1, 'Title is required'),
    image: z.string().optional(),
    description: z.string().optional(),
    deviceTypes: z.array(z.string()).optional(),
    status: z.enum(['Active', 'Archived']),
})

type OfferFormValues = z.infer<typeof offerSchema>

export default function CreateAffiliateOffer() {
    const navigate = useNavigate()
    const fileInputRef = useRef<HTMLInputElement>(null)
    const [profileImage, setProfileImage] = useState<string>('')

    const form = useForm<OfferFormValues>({
        resolver: zodResolver(offerSchema),
        defaultValues: { domain: '', title: '', image: '', description: '', deviceTypes: [], status: 'Active' },
    })

    const { mutateAsync: getPreSignedUrlMutation } = getPresignedUrl()
    const { mutateAsync: uploadFileToS3Mutation } = uploadFileToS3()

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]; if (!file) return
        const error = validateImageFile(file); if (error) { toast.error(error); return }
        try {
            const ext = file.name.split('.').pop()?.toLowerCase() || 'jpg'
            const presignedRes: any = await getPreSignedUrlMutation({ location: 'offers', type: ext, count: 1 })
            const fileData = presignedRes?.data?.files?.[0]
            await uploadFileToS3Mutation({ url: fileData.url, file })
            form.setValue('image', fileData.filename)
            setProfileImage(import.meta.env.VITE_S3_BASE_URL + fileData.filename)
            toast.success('Image uploaded')
        } catch {
            toast.error('Image upload failed')
        } finally {
            e.target.value = ''
        }
    }

    const onSubmit = async (values: OfferFormValues) => {
        // Wire to create API when available
        toast.success('Affiliate offer created')
        navigate({ to: `/_authenticated${END_POINTS.AFFILIATE_OFFERS}/` })
    }

    return (
        <Main>
            <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Create Affiliate Offer</h1>
            </div>
            <Card className='mt-4'>
                <CardHeader>
                    <CardTitle>Basic info</CardTitle>
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
                            <div className='grid gap-4 md:grid-cols-2'>
                                <FormField name='domain' control={form.control} render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Domain</FormLabel>
                                        <FormControl>
                                            <FilterSelect value={field.value || undefined} placeholder='Select Domain' options={domainTypeOptions} onChange={(val) => field.onChange(val || '')} className='w-full bg-card' />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )} />

                                <FormField name='title' control={form.control} render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Offer Title</FormLabel>
                                        <FormControl>
                                            <Input placeholder='Enter offer title' {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )} />

                                <FormField name='status' control={form.control} render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Status</FormLabel>
                                        <FormControl>
                                            <FilterSelect value={field.value} placeholder='Select Status' options={statusOptions} onChange={(val) => field.onChange(val || 'Active')} className='w-full bg-card' />
                                        </FormControl>
                                    </FormItem>
                                )} />

                                <FormField name='deviceTypes' control={form.control} render={() => (
                                    <FormItem >
                                        <FormLabel>Device Types</FormLabel>
                                        <FormControl>
                                            <div className='flex items-center gap-6'>
                                                {deviceTypeOptions.map((opt) => {
                                                    const checked = (form.watch('deviceTypes') || []).includes(opt)
                                                    return (
                                                        <label key={opt} className='flex items-center gap-2 text-sm cursor-pointer'>
                                                            <Checkbox checked={checked} onCheckedChange={() => {
                                                                const prev = form.getValues('deviceTypes') || []
                                                                form.setValue('deviceTypes', checked ? prev.filter(v => v !== opt) : [...prev, opt])
                                                            }} />
                                                            <span>{opt}</span>
                                                        </label>
                                                    )
                                                })}
                                            </div>
                                        </FormControl>
                                    </FormItem>
                                )} />

                                <FormField name='image' control={form.control} render={() => (
                                    <FormItem className='md:col-span-2'>
                                        <FormLabel>Offer Image</FormLabel>
                                        <FormControl>
                                            <div
                                                className="w-16 h-16 flex justify-center items-center border-2 border-dashed rounded-lg cursor-pointer relative overflow-hidden bg-muted"
                                                onClick={() => fileInputRef.current?.click()}
                                            >
                                                {profileImage ? (
                                                    <img src={profileImage} alt="Image Preview" className="h-full w-full object-cover" />
                                                ) : (
                                                    <Plus className="h-6 w-6 text-muted-foreground" />
                                                )}
                                                <Input
                                                    type="file"
                                                    ref={fileInputRef}
                                                    className="hidden"
                                                    onChange={handleFileChange}
                                                    accept="image/*"
                                                />
                                            </div>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )} />

                                <FormField name='description' control={form.control} render={({ field }) => (
                                    <FormItem className='md:col-span-2'>
                                        <FormLabel>Description</FormLabel>
                                        <FormControl>
                                            <Textarea rows={5} placeholder='Enter description' {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )} />


                            </div>

                            <div className='pt-2 flex justify-end'>
                                <Button type='submit'>Create</Button>
                            </div>
                        </form>
                    </Form>
                </CardContent>
            </Card>
        </Main>
    )
}
