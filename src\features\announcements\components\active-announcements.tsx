import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Sparkles } from "lucide-react";
import { formatDateToReadable } from "@/features/members/utils/utilities";

interface AnnouncementCardProps {
    name: string;
    email: string;
    content: string;
    date: string;
    defaultIsPast?: boolean;
    onToggleChange?: (newState: boolean, id: number) => void;
    id?: number
}

export function AnnouncementCard({
    name,
    email,
    content,
    date,
    defaultIsPast = false,
    onToggleChange,
    id = 1
}: AnnouncementCardProps) {
    const [isPast, setIsPast] = useState(defaultIsPast);

    const handleToggle = (checked: boolean) => {
        setIsPast(checked);
        onToggleChange?.(checked, id);
    };

    return (
        <Card className="bg-muted-cards text-muted-foreground w-full max-w-md card-bg gap-0">
            <CardContent className="p-0 flex flex-col gap-5 h-full">
                <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                        {/* <Avatar>
                            <AvatarFallback>{name.charAt(0)}</AvatarFallback>
                        </Avatar> */}
                        <div>
                            <p className="font-medium text-foreground card-txt text-lg">{name}</p>
                            <p className="text-sm text-muted-foreground card-txt text-sm">{email}</p>
                        </div>
                    </div>
                    <Sparkles className="w-5 h-5 mt-1 text-white" />
                </div>

                <p className="text-sm text-muted-foreground card-txt text-sm border-b pb-5">
                    {content}
                </p>

                <div className="flex items-center justify-between text-xs text-muted-foreground mt-auto">
                    <span className="card-txt text-sm">{formatDateToReadable(date)}</span>
                    <div className="flex items-center space-x-2">
                        <span className="card-txt text-sm">Convert to Past Announcements</span>
                        <Switch checked={isPast} onCheckedChange={handleToggle} />
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
