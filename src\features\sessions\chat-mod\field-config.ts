import { FieldConfig } from "./types";

export const INFORMATION_FIELD_CONFIGS: FieldConfig[] = [
  {
    id: "name",
    type: "Name",
    label: "Name",
    placeholder: "Enter name",
    inputType: "input",
    apiKey: "name",
  },
  {
    id: "livingConditions",
    type: "Living Conditions",
    label: "Living Conditions",
    placeholder: "Enter living conditions information",
    inputType: "textarea",
    apiKey: "livingConditions",
  },
  {
    id: "familyPets",
    type: "Family / Pets",
    label: "Family / Pets",
    placeholder: "Enter family and pets information",
    inputType: "textarea",
    apiKey: "familyPets",
  },
];

export const getFieldConfigById = (id: string): FieldConfig | undefined => {
  return INFORMATION_FIELD_CONFIGS.find((config) => config.id === id);
};

export const getFieldConfigByType = (type: string): FieldConfig | undefined => {
  return INFORMATION_FIELD_CONFIGS.find((config) => config.type === type);
};

export const getFieldTypeOptions = () => {
  return INFORMATION_FIELD_CONFIGS.map((config) => ({
    value: config.type,
    label: config.type,
  }));
};

export const createFieldsFromApiData = (apiData: any): any[] => {
  return INFORMATION_FIELD_CONFIGS.map((config) => ({
    id: config.id,
    type: config.type,
    items: apiData?.[config.apiKey] || "",
  }));
};

export const createFieldValuesFromApiData = (
  apiData: any
): { [key: string]: string } => {
  const fieldValues: { [key: string]: string } = {};
  INFORMATION_FIELD_CONFIGS.forEach((config) => {
    fieldValues[config.id] = apiData?.[config.apiKey] || "";
  });
  return fieldValues;
};
