import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from '@radix-ui/react-icons'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DataTablePaginationProps } from './types'

export function DataTablePagination<TData>({
  table,
  showRowsPerPage = true,
  showSelectedCount = true,
}: DataTablePaginationProps<TData>) {
  return (
    <div className='flex flex-col space-y-3 px-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0'>
      {showSelectedCount && (
        <div className='text-muted-foreground text-xs sm:text-sm'>
          {table.getFilteredSelectedRowModel().rows.length} of{' '}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
      )}

      <div className='flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-x-6 sm:space-y-0 lg:space-x-8'>
        {showRowsPerPage && (
          <div className='flex items-center space-x-2'>
            <p className='text-xs font-medium sm:text-sm'>Rows per page</p>
            <Select
              value={`${table.getState().pagination.pageSize}`}
              onValueChange={(value) => {
                table.setPageSize(Number(value))
              }}
            >
              <SelectTrigger className='h-9 w-[70px]'>
                <SelectValue placeholder={table.getState().pagination.pageSize} />
              </SelectTrigger>
              <SelectContent side='top'>
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <SelectItem key={pageSize} value={`${pageSize}`}>
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <div className='flex items-center justify-center text-xs font-medium sm:text-sm'>
          Page {table.getState().pagination.pageIndex + 1} of{' '}
          {table.getPageCount()}
        </div>

        <div className='flex items-center justify-center space-x-2'>
          <Button
            variant='outline'
            className='hidden h-8 w-8 p-0 sm:flex'
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
          >
            <span className='sr-only'>Go to first page</span>
            <DoubleArrowLeftIcon className='h-3 w-3 sm:h-4 sm:w-4' />
          </Button>
          <Button
            variant='outline'
            className='h-8 w-8 p-0'
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <span className='sr-only'>Go to previous page</span>
            <ChevronLeftIcon className='h-3 w-3 sm:h-4 sm:w-4' />
          </Button>
          <Button
            variant='outline'
            className='h-8 w-8 p-0'
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <span className='sr-only'>Go to next page</span>
            <ChevronRightIcon className='h-3 w-3 sm:h-4 sm:w-4' />
          </Button>
          <Button
            variant='outline'
            className='hidden h-8 w-8 p-0 sm:flex'
            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
            disabled={!table.getCanNextPage()}
          >
            <span className='sr-only'>Go to last page</span>
            <DoubleArrowRightIcon className='h-3 w-3 sm:h-4 sm:w-4' />
          </Button>
        </div>
      </div>
    </div>
  )
}
