import { z } from "zod";

const userStatusSchema = z.union([
  z.literal("active"),
  z.literal("inactive"),
  z.literal("invited"),
  z.literal("suspended"),
]);
export type ModeratorStatus = z.infer<typeof userStatusSchema>;

const userRoleSchema = z.union([
  z.literal("superadmin"),
  z.literal("admin"),
  z.literal("cashier"),
  z.literal("manager"),
]);

const moderatorSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().optional(),
  phone: z.string(),
  nickname: z.string().optional(),
  moderatorType: z.string(),
  status: userStatusSchema.optional(),
  role: userRoleSchema.optional(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date().optional(),
});

const moderatorFormSchema = z.object({
  name: z.string().trim().nonempty("Name is required"),
  nickname: z.string().trim().min(2, "Nick name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(6, "Phone Number is required"),
  nativeCommission: z.string().min(1, "Native Commision is required"),
  hybridCommission: z.string().min(1, "Hybrid Commision is required"),
  reLobbyCommission: z.string().min(1, "Relobby Commision is Required"),
  isTrigger: z.boolean(),
  translatorAccess: z.boolean(),
  moderatorType: z.string().min(1, "Moderator Type is required"),
  countriesAssignNative: z.array(z.string()).min(1, "Countries Assign Native is Required"),
  countriesAssignHybrid: z.array(z.string()).optional(),
  messageAccess: z.string().min(1, "Message Access is required"),
  minimumPayout: z.string().min(1, "Minimum Payout is required"),
  paymentCurrency: z.string().min(1, "Payment Currency is required"),
  paymentMethod: z.string().min(1, "Payment Method is required"),
  beneficiaryAccountName: z.string().trim().optional(),

  // SEPA/IBAN Payment Method Fields
  iban: z.string().optional(),
  bicSwift: z.string().optional(),

  // SWIFT Payment Method Fields
  accountNumber: z.string().optional(),
  swiftBicCode: z.string().optional(),
  bankName: z.string().optional(),
  bankAddress: z.string().optional(),
  beneficiaryAddress: z.string().optional(),
  intermediaryBankSwift: z.string().optional(),
  purposeOfPayment: z.string().optional(),
}).superRefine((data, ctx) => {
  // Skip validation if payment method is "none"
  if (data.paymentMethod === "none") {
    return;
  }

  // Common validation for both SEPA and SWIFT - Account Holder's Name is required
  if (data.paymentMethod === "sepa" || data.paymentMethod === "swift") {
    if (!data.beneficiaryAccountName || data.beneficiaryAccountName.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Account Holder's Name is required",
        path: ["beneficiaryAccountName"]
      });
    }
  }

  // SEPA/IBAN validation - Required fields
  if (data.paymentMethod === "sepa") {
    if (!data.iban || data.iban.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "IBAN is required for SEPA payment method",
        path: ["iban"]
      });
    }
    // BIC/SWIFT is optional for SEPA
  }

  // SWIFT validation - Required fields
  if (data.paymentMethod === "swift") {
    if (!data.accountNumber || data.accountNumber.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Bank Account Number is required for SWIFT payment method",
        path: ["accountNumber"]
      });
    }

    if (!data.swiftBicCode || data.swiftBicCode.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "SWIFT/BIC Code is required for SWIFT payment method",
        path: ["swiftBicCode"]
      });
    }

    if (!data.bankName || data.bankName.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Bank Name is required for SWIFT payment method",
        path: ["bankName"]
      });
    }

    if (!data.bankAddress || data.bankAddress.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Bank Address is required for SWIFT payment method",
        path: ["bankAddress"]
      });
    }

    if (!data.beneficiaryAddress || data.beneficiaryAddress.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Beneficiary Address is required for SWIFT payment method",
        path: ["beneficiaryAddress"]
      });
    }
  }
});

export type ModeratorFormValues = z.infer<typeof moderatorFormSchema>;

export type Moderator = z.infer<typeof moderatorSchema>;

export const moderatorListSchema = z.array(moderatorSchema);

export const moderatorCreateSchema = moderatorFormSchema;