import { faker } from '@faker-js/faker'
import { ReplyMessage } from './schema'

// Sample domains and countries
const domains = [
  'uforpls.female.deso',
  'example.com',
  'testdomain.net',
  'sample.org',
  'demo.site'
]

const countries = [
  'USA',
  'UK',
  'Canada',
  'Australia',
  'Germany',
  'France',
  'Spain',
  'Italy',
  'Japan',
  'India'
]

const messageTypes = ['text', 'image', 'video', 'audio', 'file'] as const

// Sample client messages and moderator replies
const clientMessages = [
  'Keep in today, 17 Dec 2024, 1:31 PM',
  'Hello, how are you?',
  'Can we chat later?',
  'I need some help',
  'What are you doing?',
  'Are you available?',
  'Good morning!',
  'How was your day?',
  'Can you call me?',
  'I miss you'
]

const moderatorReplies = [
  'How are you? 17 Dec 2024, 1:31 PM',
  'Keep in today, 17 Dec 2024, 1:31 PM',
  'Hello! I am doing well, thank you',
  'Sure, I will be available later',
  'I am here to help you',
  'Just working, how about you?',
  'Yes, I am available now',
  'Good morning to you too!',
  'It was great, thanks for asking',
  'I will call you shortly',
  'Miss you too!'
]

export const replyMessages: ReplyMessage[] = Array.from({ length: 50 }, (_, index) => ({
  id: faker.string.uuid(),
  serialNumber: index + 1,
  domain: domains[index % domains.length],
  client: faker.person.firstName(),
  profile: faker.person.firstName(),
  clientMessage: clientMessages[index % clientMessages.length],
  moderatorReply: moderatorReplies[index % moderatorReplies.length],
  messageType: messageTypes[index % messageTypes.length],
  timestamp: faker.date.recent({ days: 30 }),
  country: countries[index % countries.length],
}));
