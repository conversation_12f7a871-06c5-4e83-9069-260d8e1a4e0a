import React from 'react'
import { cn } from '@/lib/utils'
import {
    Select,
    SelectTrigger,
    SelectValue,
    SelectContent,
    SelectItem,
} from '@/components/ui/select'

interface FilterSelectProps {
    value: string | undefined
    placeholder: string
    options: string[]
    className?: string
    onChange: (value: string | undefined) => void
}

const PLACEHOLDER_VALUE = '__placeholder__'

export const FilterSelect: React.FC<FilterSelectProps> = ({
    value,
    placeholder,
    options,
    onChange,
    className = 'w-[180px] bg-card',
}) => {
    const isPlaceholderSelected = value == null

    return (
        <Select
            value={isPlaceholderSelected ? PLACEHOLDER_VALUE : value}
            onValueChange={(val) =>
                onChange(val === PLACEHOLDER_VALUE ? undefined : val)
            }
        >
            <SelectTrigger className={cn(className, isPlaceholderSelected && 'text-muted-foreground')}>
                <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
                <SelectItem value={PLACEHOLDER_VALUE} disabled className="hidden">
                    {placeholder}
                </SelectItem>
                {options.map((option) => (
                    <SelectItem key={option} value={option}>
                        {option}
                    </SelectItem>
                ))}
            </SelectContent>
        </Select>

    )
}
