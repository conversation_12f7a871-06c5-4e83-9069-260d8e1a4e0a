import { IconUserPlus } from "@tabler/icons-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";
import { UploadIcon } from "lucide-react";
import { useRef } from "react";
import { uploadFlirtMessageCsvApi } from "../api";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

export function FlirtMessagePrimaryButtons() {
    const router = useRouter();
    const fileInputRef = useRef<HTMLInputElement>(null);
    const { mutateAsync: uploadCsvMutation } = uploadFlirtMessageCsvApi();
    const queryClient = useQueryClient();

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;
        try {
            const response: any = await uploadCsvMutation(file);
            if (response?.success) {
                toast.success("CSV uploaded successfully!");
                // Refetch the flirt-message-list after successful upload
                queryClient.invalidateQueries({ queryKey: ["flirt-message-list"] });
            } else {
                toast.error(response?.message || "Failed to upload CSV.");
            }
        } catch (error: any) {
            toast.error(error?.response?.message || "Failed to upload CSV.");
        } finally {
            // Reset the input so the same file can be selected again if needed
            if (fileInputRef.current) fileInputRef.current.value = "";
        }
    };

    const uploadCSV = () => {
        fileInputRef.current?.click();
    };

    return (
        <div className="flex gap-2">
            <Button
                className="space-x-1"
                onClick={() =>
                    router.navigate({ to: END_POINTS.ADD_FLIRT_MESSAGE })
                }
            >
                <span>Add Flirt Message</span> <IconUserPlus size={18} />
            </Button>

            <Button
                className="space-x-1"
                onClick={uploadCSV}
            >
                <span>Upload CSV</span> <UploadIcon size={18} />
            </Button>
            <input
                type="file"
                accept=".csv"
                ref={fileInputRef}
                style={{ display: "none" }}
                onChange={handleFileChange}
            />
        </div>
    );
}
