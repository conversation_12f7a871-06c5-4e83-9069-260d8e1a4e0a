import { z } from "zod";

const chatGroupStatusSchema = z.union([
  z.literal("active"),
  z.literal("inactive"),
]);

export type ChatGroupStatus = z.infer<typeof chatGroupStatusSchema>;

export const chatGroupSchema = z.object({
  id: z.string(),
  serialNumber: z.number(),
  moderator: z.string(),
  group: z.string(),
  status: chatGroupStatusSchema,
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date().optional(),
});

export type ChatGroup = z.infer<typeof chatGroupSchema>;
