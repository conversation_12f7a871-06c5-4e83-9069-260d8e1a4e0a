import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getRelationshipStatusApi = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.RELATIONSHIP_STATUS, {
                params,
            });
            return response?.data ?? {};
        },
        queryKey: ["relationship-status-list"],
    });

export const addRelationshipStatusApi = () =>
    useMutation({
        mutationFn: async (data: any) => {
            const response = await apiClient.post(API_ENDPOINTS.RELATIONSHIP_STATUS, data);
            return response?.data;
        },
    });

export const updateRelationshipStatusApi = () =>
    useMutation({
        mutationFn: async ({ id, data }: { id: string; data: any }) => {
            const response = await apiClient.put(`${API_ENDPOINTS.RELATIONSHIP_STATUS}/${id}`, data);
            return response?.data;
        },
    });

export const deleteRelationshipStatusApi = () =>
    useMutation({
        mutationFn: async (id: string) => {
            const response = await apiClient.delete(`${API_ENDPOINTS.RELATIONSHIP_STATUS}/${id}`);
            return response?.data;
        },
    });

export const getRelationshipStatusDetails = (id: string) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(`${API_ENDPOINTS.RELATIONSHIP_STATUS}/${id}`);
            return response?.data ?? {};
        },
        queryKey: ["relationship-status-details", id],
        enabled: !!id,
    });

export const getMasterLanguagesApi = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES);
            return response?.data ?? {};
        },
        queryKey: ["get-relationship-status-language-list"],
    });
