import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/tables'
import { DataTableColumnHeader } from '@/components/data-table-column-header'

// Types for different dashboard table data
export interface CountryDomainData {
  rank: number
  country: string
  sales: string
  clicks: string
  signups: string
}

export interface AffiliateData {
  rank: number
  name: string
  sales: string
  clicks: string
  signups: string
}

export interface SignupData {
  rank: number
  domain: string
  signups: string
}

export interface SalesData {
  rank: number
  domain: string
  sales: string
}

// Column definitions for Country/Domain Statistics
export const countryDomainColumns: ColumnDef<CountryDomainData>[] = [
  {
    accessorKey: 'rank',
    header: '#',
    cell: ({ row }) => <div className='w-6 text-xs sm:w-8 sm:text-sm'>{row.getValue('rank')}</div>,
    meta: { className: 'w-8 sm:w-12' },
  },
  {
    accessorKey: 'country',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Country' />
    ),
    cell: ({ row }) => <div className='truncate text-xs sm:text-sm'>{row.getValue('country')}</div>,
    meta: { className: 'min-w-0' },
  },
  {
    accessorKey: 'sales',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Sales (€)' />
    ),
    cell: ({ row }) => <div className='text-right text-xs sm:text-sm'>{row.getValue('sales')}</div>,
    meta: { className: 'text-right w-16 sm:w-20' },
  },
  {
    accessorKey: 'clicks',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Clicks' />
    ),
    cell: ({ row }) => <div className='text-right text-xs sm:text-sm'>{row.getValue('clicks')}</div>,
    meta: { className: 'text-right w-16 sm:w-20' },
  },
  {
    accessorKey: 'signups',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Signups' />
    ),
    cell: ({ row }) => <div className='text-right text-xs sm:text-sm'>{row.getValue('signups')}</div>,
    meta: { className: 'text-right w-16 sm:w-20' },
  },
]

// Column definitions for Top 10 Affiliates
export const affiliateColumns: ColumnDef<AffiliateData>[] = [
  {
    accessorKey: 'rank',
    header: '#',
    cell: ({ row }) => <div className='w-6 text-xs sm:w-8 sm:text-sm'>{row.getValue('rank')}</div>,
    meta: { className: 'w-8 sm:w-12' },
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Name' />
    ),
    cell: ({ row }) => <div className='truncate text-xs sm:text-sm'>{row.getValue('name')}</div>,
    meta: { className: 'min-w-0' },
  },
  {
    accessorKey: 'sales',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Sales (€)' />
    ),
    cell: ({ row }) => <div className='text-right text-xs sm:text-sm'>{row.getValue('sales')}</div>,
    meta: { className: 'text-right w-20 sm:w-24' },
  },
  {
    accessorKey: 'clicks',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Clicks' />
    ),
    cell: ({ row }) => <div className='text-right text-xs sm:text-sm'>{row.getValue('clicks')}</div>,
    meta: { className: 'text-right w-20 sm:w-24' },
  },
  {
    accessorKey: 'signups',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Signups' />
    ),
    cell: ({ row }) => <div className='text-right text-xs sm:text-sm'>{row.getValue('signups')}</div>,
    meta: { className: 'text-right w-20 sm:w-24' },
  },
]

// Column definitions for Signups
export const signupColumns: ColumnDef<SignupData>[] = [
  {
    accessorKey: 'rank',
    header: '#',
    cell: ({ row }) => <div className='w-6 text-xs sm:w-8 sm:text-sm'>{row.getValue('rank')}</div>,
    meta: { className: 'w-8 sm:w-12' },
  },
  {
    accessorKey: 'domain',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Domain' />
    ),
    cell: ({ row }) => <div className='truncate text-xs sm:text-sm'>{row.getValue('domain')}</div>,
    meta: { className: 'min-w-0' },
  },
  {
    accessorKey: 'signups',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Signups' />
    ),
    cell: ({ row }) => <div className='text-right text-xs sm:text-sm'>{row.getValue('signups')}</div>,
    meta: { className: 'text-right w-16 sm:w-20' },
  },
]

// Column definitions for Sales
export const salesColumns: ColumnDef<SalesData>[] = [
  {
    accessorKey: 'rank',
    header: '#',
    cell: ({ row }) => <div className='w-6 text-xs sm:w-8 sm:text-sm'>{row.getValue('rank')}</div>,
    meta: { className: 'w-8 sm:w-12' },
  },
  {
    accessorKey: 'domain',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Domain' />
    ),
    cell: ({ row }) => <div className='truncate text-xs sm:text-sm'>{row.getValue('domain')}</div>,
    meta: { className: 'min-w-0' },
  },
  {
    accessorKey: 'sales',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Sales (€)' />
    ),
    cell: ({ row }) => <div className='text-right text-xs sm:text-sm'>{row.getValue('sales')}</div>,
    meta: { className: 'text-right w-16 sm:w-20' },
  },
]

// Reusable Dashboard Table Component
interface DashboardTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  title: string
  searchKey?: string
  showToolbar?: boolean
  showPagination?: boolean
  pageSize?: number
}

export function DashboardTable<T>({
  data,
  columns,
  title,
  searchKey,
  showToolbar = false,
  showPagination = false,
  pageSize = 10,
}: DashboardTableProps<T>) {
  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey={searchKey}
      searchPlaceholder={`Filter ${title.toLowerCase()}...`}
      enableRowSelection={false}
      enableSorting={true}
      enableFiltering={!!searchKey}
      enablePagination={showPagination}
      showToolbar={showToolbar}
      showViewOptions={false}
      showPagination={showPagination}
      pageSize={pageSize}
      className='border-0'
    />
  )
}
