import { Main } from "@/components/layout/main";
import { EyeColorsPrimaryButtons } from "./components/eye-colors-primary-buttons";
import { EyeColorsTable } from "./components/eye-colors-table";
import { columns } from "./components/eye-colors-columns";

export default function EyeColorsList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Eye Colors List</h2>
                </div>
                <EyeColorsPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <EyeColorsTable columns={columns} />
            </div>
        </Main>
    )
}
