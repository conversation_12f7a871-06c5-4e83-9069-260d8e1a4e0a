import { Button } from "@/components/ui/button";
import { IconMessageFilled } from "@tabler/icons-react";
import { useNavigate } from "@tanstack/react-router";

export function DataTableRowActions({row}) {
  const navigate = useNavigate();

  return (
    <Button
      variant="link"
      size="sm"
      onClick={() =>{
        navigate({
            to: `/sessions/chat-mod/${row?.original?.conversationid}`,
          })
        }
      }
        className="cursor-pointer"
    >
      <IconMessageFilled /> Chat
    </Button>
  );
}
