// PaginationControls.tsx
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'

function getPageButtons(current: number, total: number): (number | string)[] {
    const delta = 2
    const range: (number | string)[] = []

    for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
        range.push(i)
    }

    if (current - delta > 2) range.unshift('...')
    if (current + delta < total - 1) range.push('...')

    range.unshift(1)
    if (total > 1) range.push(total)

    return range
}

interface PaginationControlsProps<T> {
    table: Table<T>
    meta?: {
        total: number
        page: number
        pages: number
        limit: number
    }
    onPageChange?: any
    onPageSizeChange?: (newPageSize: number) => void // <-- add this
}

export function PaginationControls<T>({ table, meta, onPageChange, onPageSizeChange }: PaginationControlsProps<T>) {
    if (!meta) return null

    const pageIndex = table.getState().pagination.pageIndex
    const pageCount = meta.pages
    const currentPage = pageIndex + 1
    const pages = getPageButtons(currentPage, pageCount)
    const pageSize = table.getState().pagination.pageSize
    const pageSizeOptions = [10, 20, 30, 50, 100, 200, 500]

    return (
        <div className="flex flex-wrap justify-center items-center space-x-2 mt-4">
            {/* Rows per page selector */}
            <div className="flex items-center space-x-1">
                <span className="text-sm text-muted-foreground">Rows per page:</span>
                <select
                    className="border rounded px-2 py-1 text-sm bg-card"
                    value={pageSize}
                    onChange={e => {
                        const newSize = Number(e.target.value)
                        if (onPageSizeChange) onPageSizeChange(newSize) // <-- call parent first
                        table.setPageSize(newSize)
                        if (onPageChange) onPageChange(0)
                    }}
                >
                    {pageSizeOptions.map(size => (
                        <option key={size} value={size}>{size}</option>
                    ))}
                </select>
            </div>

            {/* Pagination buttons */}
            <div className="flex items-center space-x-1">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                        table.setPageIndex(0)
                        onPageChange && onPageChange(0)
                    }}
                    disabled={currentPage === 1}
                    className='bg-card'
                >
                    « First
                </Button>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                        table.previousPage()
                        onPageChange && onPageChange(currentPage - 2)
                    }}
                    disabled={currentPage === 1}
                    className='bg-card'
                >
                    ‹ Back
                </Button>

                {pages?.map((page, index) =>
                    typeof page === 'string' ? (
                        <span key={index} className="px-2 text-muted-foreground">
                            {page}
                        </span>
                    ) : (
                        <Button
                            key={index}
                            variant={page === currentPage ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => {
                                table.setPageIndex(page - 1)
                                onPageChange && onPageChange(page - 1)
                            }}
                            className={page === currentPage ? 'default' : 'bg-card'}
                        >
                            {page}
                        </Button>
                    )
                )}

                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                        table.nextPage()
                        onPageChange && onPageChange(currentPage)
                    }}
                    disabled={currentPage === pageCount}
                    className='bg-card'
                >
                    Next ›
                </Button>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                        table.setPageIndex(pageCount - 1)
                        onPageChange && onPageChange(pageCount - 1)
                    }}
                    disabled={currentPage === pageCount}
                    className='bg-card'
                >
                    Last »
                </Button>
            </div>
        </div>
    )
}
