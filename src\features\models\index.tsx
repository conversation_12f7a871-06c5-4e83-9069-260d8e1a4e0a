import { Main } from "@/components/layout/main";
import { ModelsPrimaryButtons } from "./components/models-primary-buttons";
import ModelsProvider from "./context/models-context";
import { ModelsTable } from "./components/models-table";
import { columns } from "./components/models-columns";

export default function List() {

  return (
    <ModelsProvider>
      <Main>
        <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              Models List
            </h2>
          </div>
          <ModelsPrimaryButtons />
        </div>
        <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
          <ModelsTable columns={columns} />
        </div>
      </Main>
    </ModelsProvider>
  );
}
