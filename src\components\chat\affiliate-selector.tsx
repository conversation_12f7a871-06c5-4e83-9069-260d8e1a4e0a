interface AffiliateSelectorProps {
  isAffiliate?: boolean;
  onChange?: (isAffiliate: boolean) => void;
}

export function AffiliateSelector({ isAffiliate, onChange }: AffiliateSelectorProps) {
  return (
    <div>
      <div className="text-sm font-semibold mb-[10px]">Affiliate</div>
      <div className="bg-sidebar p-4 rounded-2xl">
        <select 
          className="block w-full bg-sidebar text-sm outline-none"
          value={isAffiliate ? "yes" : "no"}
          onChange={(e) => onChange?.(e.target.value === "yes")}
        >
          <option value="yes">Yes</option>
          <option value="no">No</option>
        </select>
      </div>
    </div>
  );
}
