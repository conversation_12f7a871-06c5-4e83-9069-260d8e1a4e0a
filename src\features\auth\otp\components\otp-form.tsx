import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from '@/components/ui/input-otp'
import { cn } from '@/lib/utils'
import { zodResolver } from '@hookform/resolvers/zod'
import { useNavigate, useRouter, useSearch } from '@tanstack/react-router'
import { HTMLAttributes, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { verifyOtp } from '../../sign-in/api'
import { toast } from 'sonner'
import { END_POINTS } from '@/features/members/utils/constant'

type OtpFormProps = HTMLAttributes<HTMLFormElement>

const formSchema = z.object({
  otp: z.string().min(1, { message: 'Please enter your otp code.' }),
})

export function OtpForm({ className, ...props }: OtpFormProps) {
  const navigate = useNavigate()
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const { email, type }: any = useSearch({ strict: false })
  const { mutateAsync: verifyOtpMutation } = verifyOtp()


  useEffect(() => {
    if (!email || email === "<EMAIL>") {
      // If email not present, redirect back to reset-password
      navigate({ to: END_POINTS.FORGOT_PASSWORD });
    }
  }, [email, navigate]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { otp: '' },
  })

  const otp = form.watch('otp')

  async function onSubmit(data: z.infer<typeof formSchema>) {
    setIsLoading(true)

    const payload: any = {
      email, type, otp: data.otp
    }
    let response: any;
    try {
      response = await verifyOtpMutation(payload)
      if (response?.success) {
        router.navigate({
          to: END_POINTS.RESET_PASSWORD,
          search: { email }
        });
      }
    } catch (error: any) {
      if (error?.response?.data?.message === "OTP.INVALID") {
        toast.error("Invalid Otp")
        setTimeout(() => {
          setIsLoading(false)
        }, 1000)
      }
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn('grid gap-2', className)}
        {...props}
      >
        <FormField
          control={form.control}
          name='otp'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='sr-only'>One-Time Password</FormLabel>
              <FormControl>
                <InputOTP
                  maxLength={4}
                  {...field}
                  containerClassName='justify-center sm:[&>[data-slot="input-otp-group"]>div]:w-12'
                >
                  <InputOTPGroup>
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                  </InputOTPGroup>
                </InputOTP>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button className='mt-2' disabled={otp.length < 4 || isLoading}>
          Verify
        </Button>
      </form>
    </Form>
  )
}
