import { Main } from "@/components/layout/main";
import { RelationshipStatusPrimaryButtons } from "./components/relationship-status-primary-buttons";
import { RelationshipStatusTable } from "./components/relationship-status-table";
import { columns } from "./components/relationship-status-columns";

export default function RelationshipStatusList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Relationship Status List</h2>
                </div>
                <RelationshipStatusPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <RelationshipStatusTable columns={columns} />
            </div>
        </Main>
    )
}


