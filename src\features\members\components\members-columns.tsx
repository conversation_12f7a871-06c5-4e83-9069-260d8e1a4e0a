import { ColumnDef } from '@tanstack/react-table'
import { Member } from '../data/schema'
import { Badge } from '@/components/ui/badge'
import { DataTableRowActions } from './data-table-row-actions'
import { DataTableColumnHeader } from './data-table-column-header'
import LongText from '@/components/long-text'
import { formatTimestampToLocal, S3_BASE_URL } from '../utils/utilities'
import { Avatar, AvatarImage } from '@/components/ui/avatar'

export const columns: ColumnDef<Member>[] = [
  {
    accessorKey: 'serialNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='#' />
    ),
    cell: ({ row }) => <div className='w-[40px]'>{row.getValue('serialNumber')}</div>,
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'profile',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Profile' />
    ),
    cell: ({ row }: any) => {
      return (
        <div className='flex space-x-2'>
          {/* <AvatarImage src={row.original.avatar} /> */}

          <Avatar>
            <AvatarImage src={row.original.avatar ? S3_BASE_URL + row.original.avatar : "https://img.freepik.com/premium-vector/user-profile-icon-flat-style-member-avatar-vector-illustration-isolated-background-human-permission-sign-business-concept_157943-15752.jpg?semt=ais_hybrid&w=740"} />
          </Avatar>
        </div>
      )
    },
  },
  {
    accessorKey: 'username',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Username' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <LongText className='max-w-36'>
            {row.getValue('username')}
          </LongText>
        </div>
      )
    },
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Email' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <LongText className='max-w-36'>
            {row.getValue('email')}
          </LongText>
        </div>
      )
    },
  },
  {
    accessorKey: 'domain',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Domain' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <LongText className='max-w-36'>
            {row.getValue('domain')}
          </LongText>
        </div>
      )
    },
  },
  {
    accessorKey: 'city',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Country / City' />
    ),
    cell: ({ row }) => {
      const city = row.getValue('city') as string
      const country = row.original.country as string

      const cityDisplay = city && city?.trim() !== '' ? city : ''
      const countryDisplay = country && country.length > 0 ? country : ''

      // If both are available, show "city/country"
      // If only one is available, show just that value
      // If neither is available, show empty
      let displayValue = ''
      if (cityDisplay && countryDisplay) {
        displayValue = countryDisplay + " / " + cityDisplay
      } else if (cityDisplay) {
        displayValue = cityDisplay
      } else if (countryDisplay) {
        displayValue = countryDisplay
      }

      return (
        <div className='flex space-x-2'>
          <LongText className='max-w-36'>
            {displayValue}
          </LongText>
        </div>
      )
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Joined On' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <LongText className='max-w-36'>
            {formatTimestampToLocal(row.getValue('createdAt'))}
          </LongText>
        </div>
      )
    },
  },
  {
    accessorKey: 'affiliate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Affiliate' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <LongText className='max-w-36'>
            {row.getValue('affiliate')}
          </LongText>
        </div>
      )
    },
  },
  {
    accessorKey: 'credits',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Credits' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <LongText className='max-w-36'>
            {row.getValue('credits')}
          </LongText>
        </div>
      )
    },
  },
  {
    accessorKey: 'lastActivity',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Last Active' />
    ),
    cell: ({ row }: any) => {
      return (
        <div className='flex space-x-2'>
          <LongText className='max-w-36'>
            {formatTimestampToLocal(row.original.lastActivity)}
          </LongText>
        </div>
      )
    },
  },
  {
    accessorKey: 'isSuspended',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => {
      const isSuspended = row.getValue('isSuspended') as boolean
      return (
        <div className='flex space-x-2'>
          <Badge variant={!isSuspended ? 'default' : 'secondary'}>
            {!isSuspended ? 'Active' : 'Inactive'}
          </Badge>
        </div>
      )
    },
  },
  {
    accessorKey: 'emailVerified',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Email Verified' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <LongText className='max-w-36'>
            {row.getValue('emailVerified') ? "Verified" : "Resend Confirm"}
          </LongText>
        </div>
      )
    },
  },
  {
    accessorKey: 'botOn',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Bot On?' />
    ),
    cell: ({ row }) => {
      return (
        <div className='flex space-x-2'>
          <LongText className='max-w-36'>
            {row.getValue('botOn') ? "Yes" : "No"}
          </LongText>
        </div>
      )
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
]
