import { useState } from "react";
import { UserPanel } from "./user-panel";
import { ChatPanel } from "./chat-panel";
import { ConfirmDialog } from "@/components/modal";
import { HoldMessageModal } from "@/components/hold-message-modal";
import { ProblemModal } from "@/components/problem-modal";
import { UserPanelProps, ChatPanelProps } from "./types";

interface ChatLayoutProps {
  leftUser: UserPanelProps;
  rightUser: UserPanelProps;
  chat: ChatPanelProps;
  onConfirmAction?: () => void;
  onHoldAction?: () => void;
  onProblemAction?: () => void;
}

export function ChatLayout({
  leftUser,
  rightUser,
  chat,
  onConfirmAction,
  onHoldAction,
  onProblemAction
}: ChatLayoutProps) {
  const [showConfirm, setShowConfirm] = useState(false);
  const [showHold, setShowHold] = useState(false);
  const [showProblem, setShowProblem] = useState(false);

  // Enhanced user props with dialog handlers
  const leftUserWithDialog = {
    ...leftUser,
    user: {
      ...leftUser.user,
      onClick: () => { }
    }
  };

  const rightUserWithDialog = {
    ...rightUser,
    user: {
      ...rightUser.user,
      onClick: () => setShowConfirm(true)
    }
  };

  // Enhanced chat props with dialog handlers
  const chatWithDialogs = {
    ...chat,
    onHold: () => setShowHold(true),
    onProblem: () => setShowProblem(true)
  };

  return (
    <div className="grid grid-cols-1 xl:grid-cols-[1fr_2fr_1fr] h-screen w-full gap-[16px] px-3 py-4 sm:px-4 sm:py-6">
      {/* Left User Panel */}
      <UserPanel {...leftUserWithDialog} />

      {/* Center Chat Panel */}
      <ChatPanel {...chatWithDialogs} />

      {/* Right User Panel */}
      <UserPanel {...rightUserWithDialog} />

      {/* Modals */}
      <ProblemModal
        open={showProblem}
        onOpenChange={setShowProblem}
        title="Are you sure?"
        desc="This action cannot be undone. This will permanently delete the record."
        confirmText="Delete"
        cancelBtnText="Cancel"
        destructive
        isLoading={false}
        handleConfirm={() => {
          onProblemAction?.();
          setShowProblem(false);
        }}
      />

      <HoldMessageModal
        open={showHold}
        onOpenChange={setShowHold}
        title="Are you sure?"
        desc="This action cannot be undone. This will permanently delete the record."
        confirmText="Delete"
        cancelBtnText="Cancel"
        destructive
        isLoading={false}
        handleConfirm={() => {
          onHoldAction?.();
          setShowHold(false);
        }}
      />

      <ConfirmDialog
        open={showConfirm}
        onOpenChange={setShowConfirm}
        title="Are you sure?"
        desc="This action cannot be undone. This will permanently delete the record."
        confirmText="Delete"
        cancelBtnText="Cancel"
        destructive
        isLoading={false}
        handleConfirm={() => {
          onConfirmAction?.();
          setShowConfirm(false);
        }}
      />
    </div>
  );
}
