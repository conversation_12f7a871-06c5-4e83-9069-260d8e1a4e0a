import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { IconDotsVertical } from '@tabler/icons-react'
import { Badge } from '@/components/ui/badge'
import { S3_BASE_URL } from '@/features/members/utils/utilities'
import { useNavigate } from '@tanstack/react-router'
import { END_POINTS } from '@/features/members/utils/constant'

interface ProfileCardProps {
  model: any // Using any since the API model structure might be different from the interface
}

export default function ProfileCard({ model }: ProfileCardProps) {
  const navigate = useNavigate()

  // Get initials from username
  const initials = model?.username ? model.username.substring(0, 2).toUpperCase() : 'M'

  // Calculate age from date of birth
  const calculateAge = (dob: string) => {
    if (!dob) return 'N/A'
    const birthDate = new Date(dob)
    const today = new Date()
    const age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1
    }
    return age
  }

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Basic Information
  const basicInfo = [
    { label: 'Username', value: model?.username || 'N/A' },
    { label: 'Age', value: calculateAge(model?.dob) },
    { label: 'Gender', value: model?.gender?.title || 'N/A' },
    { label: 'Date of Birth', value: formatDate(model?.dob) },
    { label: 'Country', value: model?.country?.name || 'N/A' },
    { label: 'City', value: model?.city || 'N/A' },
    { label: 'Domain Type', value: model?.domainType || 'N/A' },
    { label: 'Model Group', value: model?.modelGroup?.title || 'N/A' },
  ]

  // Physical Attributes
  const physicalAttributes = [
    { label: 'Height', value: model?.height ? `${model.height} cm` : 'N/A' },
    { label: 'Weight', value: model?.weight ? `${model.weight} kg` : 'N/A' },
    { label: 'Hair Color', value: model?.hairColor?.title || 'N/A' },
    { label: 'Eye Color', value: model?.eyeColor?.title || 'N/A' },
    { label: 'Body Type', value: model?.bodyType?.title || 'N/A' },
    { label: 'Ethnicity', value: model?.ethnicity?.title || 'N/A' },
    { label: 'Best Feature', value: model?.bestFeature?.title || 'N/A' },
    { label: 'Body Art', value: model?.bodyArt?.title || 'N/A' },
  ]

  // Personal Information
  const personalInfo = [
    { label: 'Seeking For', value: model?.seekingFor?.title || 'N/A' },
    { label: 'Sexual Orientation', value: model?.sexualOrientation?.title || 'N/A' },
    { label: 'Relationship Status', value: model?.relationshipStatus?.title || 'N/A' },
    { label: 'Personality', value: model?.personality?.title || 'N/A' },
    { label: 'Kids', value: model?.kids || 'N/A' },
    { label: 'Smoking Habit', value: model?.smokingHabit?.title || 'N/A' },
    { label: 'Drinking Habit', value: model?.drinkingHabit?.title || 'N/A' },
    { label: 'Interests', value: model?.interest || ['N/A'] },
  ]
  const renderSection = (
    title: string,
    data: Array<{ label: string; value: any }>
  ) => (
    <div className="bg-white rounded-lg border overflow-hidden mb-6">
      <div className="px-6 py-4 border-b bg-gray-50">
        <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
      </div>
      <div className="px-6 py-4">
        <div className="space-y-3">
          {data.map((item) => {
            let displayValue = item.value;

            // If the value is an array of objects with title, join titles
            if (Array.isArray(displayValue)) {
              if (displayValue.length > 0) {
                if (typeof displayValue[0] === 'object' && 'title' in displayValue[0]) {
                  displayValue = displayValue.map((i: any) => i.title).join(', ');
                } else {
                  displayValue = displayValue.join(', ');
                }
              } else {
                displayValue = 'N/A';
              }
            }

            // Fallback for empty/undefined
            if (displayValue === null || displayValue === undefined || displayValue === '') {
              displayValue = 'N/A';
            }

            return (
              <div
                key={item.label}
                className="flex justify-between items-center py-1"
              >
                <div className="text-xs text-gray-500 font-medium">{item.label}</div>
                <div className="text-xs text-gray-900 text-right max-w-[60%] truncate" title={displayValue}>
                  {displayValue}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );


  return (
    <div className="space-y-6">
      {/* Profile Header Card */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="p-6 pb-4">
          <div className="flex items-start justify-between mb-4">
            <div className="flex flex-col items-center w-full">
              {/* Avatar with status */}
              <div className="relative mb-3">
                <Avatar className="h-20 w-20">
                  <AvatarImage
                    src={model?.profile ? S3_BASE_URL + model.profile : ""}
                    alt={model?.username || 'Model'}
                  />
                  <AvatarFallback className="text-xl font-semibold bg-gray-200">
                    {initials}
                  </AvatarFallback>
                </Avatar>
                <div className={`absolute -bottom-1 -right-1 h-5 w-5 border-2 border-white rounded-full ${model?.isSuspended ? 'bg-red-500' : 'bg-green-500'
                  }`}></div>
              </div>

              {/* Name and Status */}
              <h2 className="text-lg font-bold text-center mb-2">{model?.username || 'Model'}</h2>
              <div className="flex gap-2 mb-2">
                <Badge variant={model?.isSuspended ? "destructive" : "default"}>
                  {model?.isSuspended ? "Suspended" : "Active"}
                </Badge>
                {model?.botOn && <Badge variant="secondary">Bot On</Badge>}
              </div>
            </div>

            {/* Actions Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className=" top-4 right-4">
                  <IconDotsVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => navigate({ to: `${END_POINTS.UPDATE_MODEL}/${model?.id}` })}>
                  Edit Profile Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate({ to: `${END_POINTS.MODEL_PROFILE_IMAGES}/${model?.id}` })}>
                  Manage Pictures
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">
                  {model?.isSuspended ? "Activate" : "Suspend"} User
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* About Me Section */}
        {model?.aboutMe && (
          <div className="px-6 pb-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-900 mb-2">About Me</h4>
              <p className="text-sm text-gray-700">{model.aboutMe}</p>
            </div>
          </div>
        )}
      </div>

      {/* Basic Information */}
      {renderSection("Basic Information", basicInfo)}

      {/* Physical Attributes */}
      {renderSection("Physical Attributes", physicalAttributes)}

      {/* Personal Information */}
      {renderSection("Personal Information", personalInfo)}
    </div>
  )
}
