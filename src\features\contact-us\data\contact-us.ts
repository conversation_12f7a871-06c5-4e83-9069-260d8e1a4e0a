import { AdminLoginActivity } from './schema'

export const adminLoginActivities: AdminLoginActivity[] = [
  {
    id: '1',
    adminUsername: '<PERSON>',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-05T04:44:00'),
  },
  {
    id: '2',
    adminUsername: '<PERSON>',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-16T23:00'),
  },
  {
    id: '3',
    adminUsername: '<PERSON><PERSON>',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-16T16:35:00'),
  },
  {
    id: '4',
    adminUsername: 'Lin',
    adminType: 'Super Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T13:57:00'),
  },
  {
    id: '5',
    adminUsername: 'Je<PERSON>',
    adminType: 'Super Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T08:15:00'),
  },
  {
    id: '6',
    adminUsername: 'Sora',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T08:15:00'),
  },
  {
    id: '7',
    adminUsername: 'Admin',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T08:15:00'),
  },
  {
    id: '8',
    adminUsername: 'Admin',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T08:15:00'),
  },
  {
    id: '9',
    adminUsername: 'Admin',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T08:15:00'),
  },
  {
    id: '10',
    adminUsername: 'Admin',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T08:15:00'),
  },
  {
    id: '11',
    adminUsername: 'Admin',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T08:15:00'),
  },
  {
    id: '12',
    adminUsername: 'Admin',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T08:15:00'),
  },
  {
    id: '13',
    adminUsername: 'Admin',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T08:15:00'),
  },
  {
    id: '14',
    adminUsername: 'Admin',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T08:15:00'),
  },
  {
    id: '15',
    adminUsername: 'Admin',
    adminType: 'Admin',
    loginIP: '************** (IN)',
    country: 'IN',
    loginTime: new Date('2025-01-26T08:15:00'),
  },
]
