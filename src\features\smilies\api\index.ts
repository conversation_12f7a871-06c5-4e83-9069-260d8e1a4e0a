import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";


export const getSmileyApi = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.SMILIES, {
                params,
            });
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["smiley-list"],
    });


export const addSmileyApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.SMILIES, payload);
        },
    });


export const updateSmileyApi = () =>
    useMutation({
        mutationFn: async ({ id, ...payload }: any) => {
            return await apiClient.put(`${API_ENDPOINTS.SMILIES}/${id}`, payload);
        },
    });

export const getSmileyDetails = (id: any = {}) =>
    useQuery({
        queryFn: async () => {
            if (typeof id === 'string') {
                const response = await apiClient.get(`${API_ENDPOINTS.SMILIES}/${id}`)
                return response?.data ?? {}; // return [] or {} as a fallback
            }
            return {}

        },
        queryKey: ["smiley-details", id],
        enabled: !!id
    });

export const deleteSmileyApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.delete(`${API_ENDPOINTS.SMILIES}/${payload?.id}`);
        },
    });

