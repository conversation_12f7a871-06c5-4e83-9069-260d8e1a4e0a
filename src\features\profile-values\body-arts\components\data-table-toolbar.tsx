import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { X } from 'lucide-react'
import { useState } from 'react'

interface DataTableToolbarProps<TData> { table: Table<TData>; onFilterChanged?: any }

export function DataTableToolbar<TData>({ table, onFilterChanged }: DataTableToolbarProps<TData>) {
    const [searchValue, setSearchValue] = useState('')
    const handle = (v: string) => { setSearchValue(v); onFilterChanged?.({ search: v }, 1) }
    const clear = () => { setSearchValue(''); onFilterChanged?.({ search: '' }, 0) }
    return (
        <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center space-x-2">
                <Input placeholder="Filter by name..." value={searchValue} onChange={(e) => handle(e.target.value)} className="h-8 w-[150px] lg:w-[250px]" />
                {searchValue && (
                    <Button variant="ghost" onClick={clear} className="h-8 px-2 lg:px-3">Reset <X className="ml-2 h-4 w-4" /></Button>
                )}
            </div>
        </div>
    )
}
