import { Main } from "@/components/layout/main";
import { ChatGroupTable } from "./components/chat-group-table";
import { columns } from "./components/chat-group-columns";
import { chatGroups } from "./data/chat-groups";
import { ChatGroupPrimaryButtons } from "./components/chat-group-primary-buttons";
import ChatGroupProvider from "./context/chat-group-context";
import { ChatGroupDialogs } from "./components/chat-group-dialogs";

export default function ChatGroup() {
  return (
    <ChatGroupProvider>
      <Main>
        <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              Moderator Chat Group
            </h2>
          </div>
          <ChatGroupPrimaryButtons />
        </div>

        <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
          <ChatGroupTable columns={columns} data={chatGroups} />
        </div>

        <ChatGroupDialogs />
      </Main>
    </ChatGroupProvider>
  );
}
