// Utility functions to manage notification status updates
// These can be used from anywhere in the application

export interface StatusUpdate {
  id: string;
  count: number;
}

// Status IDs for easy reference
export const STATUS_IDS = {
  WEBSOCKET: 'websocket',
  TRIGGER_WAITING: 'trigger-waiting',
  LOBBY: 'lobby',
  ACTIVE: 'active',
  HOLD: 'hold',
  PROBLEM: 'problem'
} as const;

// Type for status ID
export type StatusId = typeof STATUS_IDS[keyof typeof STATUS_IDS];

// Example usage functions that can be called from your API responses or WebSocket handlers
export const NotificationStatusManager = {
  // Update WebSocket connection status
  updateWebSocketStatus: (isConnected: boolean, count = 0) => ({
    id: STATUS_IDS.WEBSOCKET,
    count: isConnected ? 1 : count
  }),

  // Update trigger waiting count
  updateTriggerWaiting: (count: number) => ({
    id: STATUS_IDS.TRIGGER_WAITING,
    count
  }),

  // Update lobby count
  updateLobby: (count: number) => ({
    id: STATUS_IDS.LOBBY,
    count
  }),

  // Update active sessions count
  updateActive: (count: number) => ({
    id: STATUS_IDS.ACTIVE,
    count
  }),

  // Update hold count
  updateHold: (count: number) => ({
    id: STATUS_IDS.HOLD,
    count
  }),

  // Update problem count
  updateProblem: (count: number) => ({
    id: STATUS_IDS.PROBLEM,
    count
  }),

  // Create multiple updates at once
  createBatchUpdate: (updates: Partial<Record<StatusId, number>>): StatusUpdate[] => {
    return Object.entries(updates)
      .filter(([_, count]) => count !== undefined)
      .map(([id, count]) => ({ id, count: count as number }));
  }
};

// Example API response handler
export const handleSessionStatusUpdate = (apiResponse: {
  websocketConnected?: boolean;
  triggerWaiting?: number;
  lobby?: number;
  active?: number;
  hold?: number;
  problem?: number;
}) => {
  const updates: StatusUpdate[] = [];

  if (apiResponse.websocketConnected !== undefined) {
    updates.push(NotificationStatusManager.updateWebSocketStatus(apiResponse.websocketConnected));
  }
  if (apiResponse.triggerWaiting !== undefined) {
    updates.push(NotificationStatusManager.updateTriggerWaiting(apiResponse.triggerWaiting));
  }
  if (apiResponse.lobby !== undefined) {
    updates.push(NotificationStatusManager.updateLobby(apiResponse.lobby));
  }
  if (apiResponse.active !== undefined) {
    updates.push(NotificationStatusManager.updateActive(apiResponse.active));
  }
  if (apiResponse.hold !== undefined) {
    updates.push(NotificationStatusManager.updateHold(apiResponse.hold));
  }
  if (apiResponse.problem !== undefined) {
    updates.push(NotificationStatusManager.updateProblem(apiResponse.problem));
  }

  return updates;
};
