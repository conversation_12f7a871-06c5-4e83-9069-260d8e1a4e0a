'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { ChatGroup } from '../data/schema'

const formSchema = z.object({
  moderator: z.string().min(1, 'Please select a moderator'),
  groups: z.array(z.string()).min(1, 'Please select at least one group'),
})

type ChatGroupForm = z.infer<typeof formSchema>

const moderatorOptions = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>'
]

const groupOptions = [
  'VIP Members Chat',
  '<PERSON> Discussion',
  'Premium Support',
  'New Members Welcome',
  'Dating Tips & Advice',
  'Success Stories',
  'Technical Support',
  'Community Guidelines',
  'Events & Meetups',
  'Feedback & Suggestions',
  'International Members',
  'Local Connections',
  'Safety & Security',
  'Feature Updates',
  'Weekend Social'
]

interface Props {
  currentRow?: ChatGroup
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ChatGroupActionDialog({ currentRow, open, onOpenChange }: Props) {
  const isEdit = !!currentRow
  const form = useForm<ChatGroupForm>({
    resolver: zodResolver(formSchema),
    defaultValues: isEdit
      ? {
        moderator: currentRow.moderator,
        groups: [currentRow.group],
      }
      : {
        moderator: '',
        groups: [],
      },
  })

  const onSubmit = (values: ChatGroupForm) => {
    form.reset()
    onOpenChange(false)
  }

  const handleCancel = () => {
    form.reset()
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isEdit ? 'Edit Moderator Chat Group' : 'Add Moderator Chat Groups'}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="moderator"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Moderator</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger style={{ width: 'auto' }}>
                        <SelectValue placeholder="Select moderator" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {moderatorOptions.map((moderator) => (
                        <SelectItem key={moderator} value={moderator}>
                          {moderator}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="groups"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Groups</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      const currentGroups = field.value || []
                      if (!currentGroups.includes(value)) {
                        field.onChange([...currentGroups, value])
                      }
                    }}
                  >
                    <FormControl>
                      <SelectTrigger style={{ width: 'auto' }}>
                        <SelectValue placeholder="Select Some Options" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {groupOptions.map((group) => (
                        <SelectItem key={group} value={group}>
                          {group}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {field.value && field.value.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {field.value.map((group) => (
                        <div
                          key={group}
                          className="bg-primary/10 text-primary px-2 py-1 rounded text-sm flex items-center gap-1"
                        >
                          {group}
                          <button
                            type="button"
                            onClick={() => {
                              field.onChange(field.value.filter((g) => g !== group))
                            }}
                            className="ml-1 text-primary hover:text-primary/80"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2">
              <Button type="button" variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button type="submit">
                Add
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
