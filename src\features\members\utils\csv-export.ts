import { Member } from '../data/schema'

/**
 * Utility function to export members data to CSV
 * @param data - Array of Member objects
 * @param filename - Name of the CSV file to download
 */
export function exportToCSV(data: Member[], filename: string = 'membersList.csv') {
  // Define CSV headers
  const headers = [
    'ID',
    'Profile',
    'Username',
    'Email',
    'Domain',
    'Country/City',
    'Joined On',
    'Affiliate',
    'Credits',
    'Last Active',
    'Status',
    'Email Verified',
    'Bot On'
  ]

  // Helper function to escape CSV values
  const escapeCSVValue = (value: any): string => {
    if (value === null || value === undefined) return ''
    const stringValue = String(value)
    // If the value contains comma, newline, or quote, wrap it in quotes and escape internal quotes
    if (stringValue.includes(',') || stringValue.includes('\n') || stringValue.includes('"')) {
      return `"${stringValue.replace(/"/g, '""')}"`
    }
    return stringValue
  }

  // Convert data to CSV format
  const csvContent = [
    headers.join(','), // Header row
    ...data.map((member: Member) => [
      escapeCSVValue(member.id),
      escapeCSVValue(member.profile),
      escapeCSVValue(member.username),
      escapeCSVValue(member.email),
      escapeCSVValue(member.domain),
      escapeCSVValue(member.countryCity),
      escapeCSVValue(member.joinedOn),
      escapeCSVValue(member.affiliate),
      escapeCSVValue(member.credits),
      escapeCSVValue(member.lastActive),
      escapeCSVValue(member.status),
      escapeCSVValue(member.emailVerified),
      escapeCSVValue(member.botOn)
    ].join(','))
  ].join('\n')

  // Create and download the CSV file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)

  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'

  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // Clean up the URL object
  URL.revokeObjectURL(url)
}

/**
 * Export all members data
 * @param data - Array of all Member objects
 */
export function exportAllMembers(data: Member[]) {
  exportToCSV(data, 'membersList.csv')
}

/**
 * Export filtered members data
 * @param data - Array of filtered Member objects
 */
export function exportFilteredMembers(data: Member[]) {
  exportToCSV(data, 'membersListFiltered.csv')
}
