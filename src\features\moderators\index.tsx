import { Main } from "@/components/layout/main";
import { ModeratorsPrimaryButtons } from "./components/moderator-primary-buttons";

import ModeratorsProvider from "./context/moderators-context";
import { ModeratorsTable } from "./components/moderators-table";
import { columns } from "./components/moderators-columns";

export default function List() {
    return (
        <ModeratorsProvider>
            <Main>
                <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                    <div>
                        <h2 className="text-2xl font-bold tracking-tight">
                            Moderator List
                        </h2>
                    </div>
                    <ModeratorsPrimaryButtons />
                </div>
                <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                    <ModeratorsTable columns={columns} />
                </div>
            </Main>
        </ModeratorsProvider>
    );
}
