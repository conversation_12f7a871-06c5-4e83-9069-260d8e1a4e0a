import { useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";


export const getLoginActivityList = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.LOGIN_ACTIVITIES, {
                params,
            });
            return response?.data ?? {};
        },
        queryKey: ["login-activity-list"],
    });
