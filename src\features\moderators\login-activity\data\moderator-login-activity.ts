import { ModeratorLoginActivity } from './schema'

export const moderatorLoginActivities: ModeratorLoginActivity[] = [
  {
    id: '1',
    moderator: '<PERSON>',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'SYSTEM/Windows10',
    totalMessageSent: 12,
  },
  {
    id: '2',
    moderator: '<PERSON><PERSON>',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Jan 23, 2025',
    totalMessageSent: 12,
  },
  {
    id: '3',
    moderator: '<PERSON>',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Jan 27, 2025',
    totalMessageSent: 12,
  },
  {
    id: '4',
    moderator: 'John Dukes',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Jan 19, 2025',
    totalMessageSent: 12,
  },
  {
    id: '5',
    moderator: 'Frances Swann',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Feb 12, 2025',
    totalMessageSent: 12,
  },
  {
    id: '6',
    moderator: 'Iva Ryan',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Jan 26, 2025',
    totalMessageSent: 12,
  },
  {
    id: '7',
    moderator: 'Bradley Lawlor',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Jan 23, 2025',
    totalMessageSent: 12,
  },
  {
    id: '8',
    moderator: 'Ricky Smith',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Feb 7, 2025',
    totalMessageSent: 12,
  },
  {
    id: '9',
    moderator: 'Alex Buckmaster',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Feb 5, 2025',
    totalMessageSent: 12,
  },
  {
    id: '10',
    moderator: 'Autumn Phillips',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Feb 6, 2025',
    totalMessageSent: 12,
  },
  {
    id: '11',
    moderator: 'Dennis Callis',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Feb 6, 2025',
    totalMessageSent: 12,
  },
  {
    id: '12',
    moderator: 'Chris Glasser',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Feb 2, 2025',
    totalMessageSent: 12,
  },
  {
    id: '13',
    moderator: 'Stephanie Nicol',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Jan 16, 2025',
    totalMessageSent: 12,
  },
  {
    id: '14',
    moderator: 'David Elson',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Jan 21, 2025',
    totalMessageSent: 12,
  },
  {
    id: '15',
    moderator: 'Kenneth Allen',
    login: new Date('2024-01-16T10:33:02'),
    logout: new Date('2024-01-16T10:33:02'),
    loginSession: '00:24:55',
    lastActivityTime: new Date('2024-01-16T10:33:02'),
    ipCountry: '188.29127.68 (GB)',
    deviceInfo: 'Jan 31, 2025',
    totalMessageSent: 12,
  },
]
