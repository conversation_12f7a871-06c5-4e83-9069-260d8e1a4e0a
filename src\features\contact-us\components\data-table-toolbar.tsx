import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useState } from 'react'
import { DatePicker } from '@/components/date-picker'
import { SearchIcon } from 'lucide-react'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  onFilterChanged?: any
}

const domainOptions = [
  'example.com',
  'testdomain.net',
  'sample.org',
  'demo.site',
]

const subjectOptions = [
  'General Inquiry',
  'Technical Support',
  'Billing Question',
  'Feature Request',
  'Bug Report',
]

export function DataTableToolbar<TData>({
  table,
  onFilterChanged
}: DataTableToolbarProps<TData>) {
  const [filters, setFilters] = useState({
    search: '',
    domain: undefined as string | undefined,
    subject: undefined as string | undefined,
    fromDate: undefined as Date | undefined,
    toDate: undefined as Date | undefined,
  })

  const [hasSearched, setHasSearched] = useState(false)

  const handleFilterChange = (
    key: 'search' | 'domain' | 'subject' | 'fromDate' | 'toDate',
    value: any
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  const handleSearch = () => {
    // Apply filters to the table
    if (filters.search) {
      table.getColumn('email')?.setFilterValue(filters.search)
    }
    if (filters.domain) {
      table.getColumn('domain')?.setFilterValue(filters.domain)
    }
    if (filters.subject) {
      table.getColumn('subject')?.setFilterValue(filters.subject)
    }
    setHasSearched(true)
    if (onFilterChanged) onFilterChanged(filters, 1)
  }

  const handleReset = () => {
    // Reset all filters
    setFilters({
      search: '',
      domain: undefined,
      subject: undefined,
      fromDate: undefined,
      toDate: undefined,
    })

    // Clear table filters
    table.resetColumnFilters()

    setHasSearched(false)
    if (onFilterChanged) onFilterChanged({}, 0)
  }

  const hasActiveFilters = Boolean(
    (filters.search && filters.search.length > 0) ||
    (filters.domain && filters.domain.length > 0) ||
    (filters.subject && filters.subject.length > 0) ||
    filters.fromDate ||
    filters.toDate
  )

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-2'>
        <div className="flex items-center gap-4 flex-wrap">
          <Input
            placeholder='Search by email...'
            value={filters.search}
            onChange={(event) => handleFilterChange('search', event.target.value)}
            className='h-8 w-[250px]'
          />
          <FilterSelect
            value={filters.domain}
            placeholder="Domain"
            options={domainOptions}
            onChange={(value) => handleFilterChange('domain', value)}
          />
          <FilterSelect
            value={filters.subject}
            placeholder="Subject"
            options={subjectOptions}
            onChange={(value) => handleFilterChange('subject', value)}
          />
          <DatePicker
            value={filters.fromDate}
            placeholder="From Date"
            onChange={(date) => handleFilterChange('fromDate', date)}
            max={filters.toDate}
          />
          <DatePicker
            value={filters.toDate}
            placeholder="To Date"
            onChange={(date) => handleFilterChange('toDate', date)}
            min={filters.fromDate}
          />
          <Button onClick={handleSearch} disabled={!hasActiveFilters}>
            Search
          </Button>
          {hasSearched && (
            <Button
              variant="outline"
              onClick={handleReset}
              className="h-8 px-3"
            >
              Reset
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
