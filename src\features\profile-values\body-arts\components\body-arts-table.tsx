import { useEffect, useState } from 'react'
import { useReactTable, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, flexRender } from '@tanstack/react-table'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { DataTableToolbar } from './data-table-toolbar'
import { PaginationControls } from '@/components/ui/PaginationControls'
import { getBodyArtsApi } from '../api'

export function BodyArtsTable({ columns }: any) {
    const [filters, setFilters] = useState<any>({ search: '' })
    const [pageIndex, setPageIndex] = useState(0)
    const [pageSize, setPageSize] = useState(10)
    const { data = {}, refetch }: any = getBodyArtsApi({ page: pageIndex + 1, limit: pageSize, ...filters })
    const dataWithSerialNumbers = (data?.body_arts || data?.bodyArts || []).map((item: any, idx: number) => ({ ...item, serialNumber: idx + 1 + (pageIndex * pageSize) }))

    const table = useReactTable({ data: dataWithSerialNumbers, columns, getCoreRowModel: getCoreRowModel(), getPaginationRowModel: getPaginationRowModel(), getSortedRowModel: getSortedRowModel(), getFilteredRowModel: getFilteredRowModel() })
    useEffect(() => { refetch() }, [pageIndex, pageSize, filters.search])
    return (
        <div className="space-y-4">
            <DataTableToolbar table={table} onFilterChanged={setFilters} />
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((hg) => (
                            <TableRow key={hg.id}>
                                {hg.headers.map((h) => (<TableHead key={h.id}>{h.isPlaceholder ? null : flexRender(h.column.columnDef.header, h.getContext())}</TableHead>))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                                    {row.getVisibleCells().map((cell) => (<TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow><TableCell colSpan={columns.length} className="h-24 text-center">No results.</TableCell></TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
            <PaginationControls table={table} />
        </div>
    )
}
