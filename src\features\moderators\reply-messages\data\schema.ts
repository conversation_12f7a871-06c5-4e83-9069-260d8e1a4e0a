import { z } from "zod";

const messageTypeSchema = z.union([
  z.literal("text"),
  z.literal("image"),
  z.literal("video"),
  z.literal("audio"),
  z.literal("file"),
]);

export type MessageType = z.infer<typeof messageTypeSchema>;

const replyMessageSchema = z.object({
  id: z.string(),
  domain: z.string(),
  client: z.string(),
  profile: z.string(),
  clientMessage: z.string(),
  moderatorReply: z.string(),
  messageType: messageTypeSchema,
  timestamp: z.coerce.date(),
  country: z.string(),
});

export type ReplyMessage = z.infer<typeof replyMessageSchema>;

export const replyMessageListSchema = z.array(replyMessageSchema);
