import { Main } from "@/components/layout/main";
import { BestFeaturesPrimaryButtons } from "./components/best-features-primary-buttons";
import { BestFeaturesTable } from "./components/best-features-table";
import { columns } from "./components/best-features-columns";

export default function BestFeaturesList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">Best Features List</h2>
                </div>
                <BestFeaturesPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <BestFeaturesTable columns={columns} />
            </div>
        </Main>
    )
}
