'use client'

import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
} from '@/components/ui/dialog'
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
// No close icon in header as per design

const creditFormSchema = z.object({
    username: z.string().min(1, 'Username is required'),
    amount: z
        .string()
        .min(1, 'Amount is required')
        .regex(/^\d+$/, 'Amount must be a number'),
})

export type CreditFormValues = z.infer<typeof creditFormSchema>

interface Props {
    open: boolean
    onOpenChange: (open: boolean) => void
    defaultUsername?: string
    currentBalance?: number
    onSubmit: (data: CreditFormValues) => void
    title?: string
    actionLabel?: string
}

export function CreditAddDialog({
    open,
    onOpenChange,
    defaultUsername = '',
    currentBalance = 0,
    onSubmit,
    title = 'Add Credits',
    actionLabel = 'Add',
}: Props) {
    const form = useForm<CreditFormValues>({
        resolver: zodResolver(creditFormSchema),
        defaultValues: {
            username: defaultUsername,
            amount: '',
        },
    })

    const handleSubmit = (values: CreditFormValues) => {
        onSubmit(values)
        form.reset()
        onOpenChange(false)
    }

    const handleCancel = () => {
        form.reset()
        onOpenChange(false)
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent showClose={false} className="sm:max-w-md bg-white">
                <DialogHeader className="flex-row items-center justify-between gap-2 text-left">
                    <DialogTitle className="text-lg font-semibold">{title}</DialogTitle>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>Credit / Coin Balance:</span>
                        <span className="text-sm font-semibold bg-[#E7E7E7] px-3 py-2 rounded-full flex gap-1 items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 25 25" fill="none">
                                <path d="M21.5 17.0815V18.4385C21.5 19.2525 21.119 19.9485 20.59 20.4955C20.067 21.0375 19.357 21.4795 18.558 21.8295C16.956 22.5295 14.814 22.9385 12.5 22.9385C10.186 22.9385 8.044 22.5305 6.442 21.8295C5.643 21.4795 4.933 21.0375 4.41 20.4955C3.925 19.9955 3.565 19.3675 3.508 18.6395L3.5 18.4385V17.0815C3.96467 17.3468 4.46267 17.5785 4.994 17.7765C7.024 18.5275 9.679 18.9465 12.5 18.9465C15.321 18.9465 17.976 18.5275 20.006 17.7765C20.4047 17.6285 20.7843 17.4608 21.145 17.2735L21.5 17.0815ZM3.5 11.5815C3.96467 11.8468 4.46267 12.0785 4.994 12.2765C7.024 13.0275 9.679 13.4465 12.5 13.4465C15.321 13.4465 17.976 13.0275 20.006 12.2765C20.5223 12.0863 21.022 11.8538 21.5 11.5815V14.6865C20.8362 15.1994 20.0985 15.6087 19.312 15.9005C17.557 16.5505 15.148 16.9475 12.5 16.9475C9.853 16.9475 7.444 16.5505 5.688 15.9005C4.90151 15.6087 4.16379 15.1994 3.5 14.6865V11.5815ZM12.5 3.93848C14.814 3.93848 16.956 4.34648 18.558 5.04748C19.357 5.39748 20.067 5.83948 20.59 6.38148C21.075 6.88148 21.435 7.50948 21.492 8.23748L21.5 8.43848V9.18648C20.8362 9.69939 20.0985 10.1087 19.312 10.4005C17.557 11.0505 15.148 11.4475 12.5 11.4475C9.853 11.4475 7.444 11.0505 5.688 10.4005C5.01199 10.1493 4.3716 9.81115 3.783 9.39448L3.5 9.18648V8.43848C3.5 7.62448 3.881 6.92848 4.41 6.38148C4.933 5.83948 5.643 5.39748 6.442 5.04748C8.044 4.34748 10.186 3.93848 12.5 3.93848Z" fill="#4F4F4F" />
                            </svg>
                            {currentBalance}
                        </span>
                    </div>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="username"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Username</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter username" {...field} disabled />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="amount"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Credit Amount</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter credit amount" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <DialogFooter className="gap-2">
                            <Button style={{ cursor: 'pointer' }} type="submit">{actionLabel}</Button>
                            <Button style={{ cursor: 'pointer' }} variant="outline" type="button" onClick={handleCancel}>
                                Cancel
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    )
}
