import { END_POINTS } from "@/features/members/utils/constant";
import CommonLayout from "@/features/smilies/common-layout";
import { createFileRoute } from "@tanstack/react-router";
import { roleGuards } from '@/utils/route-protection'

export const Route = createFileRoute<any>(`/_authenticated${END_POINTS.SMILIES}`)({
  beforeLoad: ({ location }) => {
    roleGuards.managerAndAbove(location.pathname)
  },
  component: CommonLayout,
});
