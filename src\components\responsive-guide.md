# Responsive Design Guide

This guide outlines the responsive design system implemented across the project to ensure optimal user experience on all devices.

## Breakpoints

The project uses Tailwind CSS's default breakpoints:

- **Mobile**: `< 640px` (default)
- **Small (sm)**: `≥ 640px` (tablets)
- **Medium (md)**: `≥ 768px` (small laptops)
- **Large (lg)**: `≥ 1024px` (desktops)
- **Extra Large (xl)**: `≥ 1280px` (large screens)
- **2XL**: `≥ 1536px` (ultra-wide screens)

## Mobile-First Approach

All components follow a mobile-first design approach:

1. **Base styles** target mobile devices
2. **Responsive modifiers** enhance for larger screens
3. **Progressive enhancement** adds features as screen size increases

## Key Responsive Components

### 1. Layout Components

#### Header
- **Mobile**: Smaller height (56px), compact spacing
- **Desktop**: Standard height (64px), normal spacing
- **Features**: Responsive sidebar trigger, adaptive separator

#### Main Content Area
- **Mobile**: Reduced padding (12px), smaller margins
- **Desktop**: Standard padding (16px-24px), larger margins
- **Features**: Adaptive spacing based on screen size

#### Sidebar
- **Mobile**: Overlay/sheet behavior
- **Desktop**: Fixed sidebar with collapsible states
- **Features**: Automatic mobile detection and behavior switching

### 2. Dashboard Components

#### Stat Cards
- **Mobile**: Single column, compact text, smaller charts
- **Tablet**: Two columns
- **Desktop**: Three columns
- **Large**: Four columns
- **Features**: Responsive text sizes, adaptive chart heights

#### Dashboard Tables
- **Mobile**: Horizontal scroll, compact cells, smaller text
- **Tablet**: Two-column grid for main tables
- **Desktop**: Three-column grid for smaller tables
- **Features**: Responsive column widths, adaptive text sizes

### 3. Table Components

#### Data Tables
- **Mobile**: 
  - Horizontal scroll with minimum width
  - Compact cell padding
  - Smaller text sizes
  - Simplified pagination
- **Desktop**:
  - Full-width display
  - Standard cell padding
  - Normal text sizes
  - Complete pagination controls

#### Table Toolbar
- **Mobile**: Stacked layout, full-width search
- **Desktop**: Horizontal layout, constrained search width
- **Features**: Responsive filter layout, adaptive button sizes

## Responsive Utilities

### Custom Hooks

#### `useResponsive()`
```tsx
const { isMobile, isTablet, isDesktop, windowSize } = useResponsive()
```

#### `useResponsiveValue()`
```tsx
const columns = useResponsiveValue({
  mobile: 1,
  tablet: 2,
  desktop: 3,
  default: 1
})
```

### Custom Components

#### `ResponsiveContainer`
```tsx
<ResponsiveContainer maxWidth="lg" padding="md" center>
  {children}
</ResponsiveContainer>
```

#### `ResponsiveGrid`
```tsx
<ResponsiveGrid 
  cols={{ mobile: 1, tablet: 2, desktop: 3, default: 1 }}
  gap="md"
>
  {children}
</ResponsiveGrid>
```

#### `ResponsiveStack`
```tsx
<ResponsiveStack 
  direction={{ mobile: 'col', desktop: 'row', default: 'col' }}
  spacing="md"
>
  {children}
</ResponsiveStack>
```

## CSS Classes

### Responsive Text Sizes
- `.text-responsive-xs`: xs → sm
- `.text-responsive-sm`: sm → base
- `.text-responsive-base`: sm → base → lg
- `.text-responsive-lg`: base → lg → xl
- `.text-responsive-xl`: lg → xl → 2xl

### Responsive Spacing
- `.space-responsive-sm`: 8px → 12px
- `.space-responsive-md`: 12px → 16px → 24px
- `.space-responsive-lg`: 16px → 24px → 32px

### Responsive Padding
- `.p-responsive-sm`: 8px → 12px → 16px
- `.p-responsive-md`: 12px → 16px → 24px
- `.p-responsive-lg`: 16px → 24px → 32px

## Best Practices

### 1. Mobile-First Design
- Start with mobile styles
- Use responsive modifiers to enhance for larger screens
- Test on actual devices, not just browser dev tools

### 2. Touch Targets
- Minimum 44px touch targets on mobile
- Use `.touch-target` class for interactive elements
- Ensure adequate spacing between clickable elements

### 3. Content Strategy
- Prioritize essential content on mobile
- Use progressive disclosure for complex interfaces
- Consider content hierarchy across screen sizes

### 4. Performance
- Use responsive images with appropriate sizes
- Implement lazy loading for off-screen content
- Optimize for mobile network conditions

### 5. Testing
- Test on multiple device sizes
- Use browser dev tools for initial testing
- Validate on real devices when possible
- Test both portrait and landscape orientations

## Implementation Examples

### Dashboard Grid
```tsx
// Mobile: 1 column, Tablet: 2 columns, Desktop: 3 columns
<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
  {cards.map(card => <Card key={card.id} {...card} />)}
</div>
```

### Responsive Table
```tsx
// Horizontal scroll on mobile, full width on desktop
<div className="overflow-x-auto">
  <table className="min-w-[600px] sm:min-w-0 w-full">
    {/* Table content */}
  </table>
</div>
```

### Adaptive Header
```tsx
// Smaller on mobile, standard on desktop
<header className="h-14 sm:h-16 px-3 sm:px-4">
  {/* Header content */}
</header>
```

This responsive system ensures consistent, accessible, and performant user experiences across all device types and screen sizes.
