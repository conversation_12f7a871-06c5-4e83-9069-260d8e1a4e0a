import { AlertDialog, AlertDialogContent } from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";
import { calculateAge, FormatS3ImgUrl, generateNickName } from "@/utils/common";
import { AlertDialogCancel } from "@radix-ui/react-alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@radix-ui/react-avatar";

export function ConfirmDialog({ open, setOpen, data }) {
  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogContent
        width="max-w-[1250px]"
        className={cn("p-0 radius-md overflow-hidden bg-card")}
      >
        <AlertDialogCancel
          onClick={() => setOpen(false)}
          className="absolute top-3 right-3 rounded-full p-2 transition cursor-pointer"
        >
          ✕
        </AlertDialogCancel>
        <div className="p-6 bg-muted/50">
          <h2 className="text-2xl font-semibold mb-6">Client Profile</h2>
          <div className="grid grid-cols-1 lg:grid-cols-[400px_1fr] gap-4">
            {/* Left Profile Section */}
            <div className="max-w-sm">
              <h2 className="text-lg font-semibold text-foreground mb-3">
                About Client
              </h2>

              <div className="flex flex-col gap-1 max-h-[80dvh] overflow-y-auto">
                {/* Profile Header */}
                <div className="bg-gray-100 rounded-xl p-4">
                  <div className="flex justify-between items-start">
                    <Avatar className="w-[54px] h-[54px] rounded-full bg-[#999] text-white flex items-center justify-center">
                      {data?.avatar ? (
                        <AvatarImage
                          src={FormatS3ImgUrl(data?.avatar)}
                          alt="Avatar"
                          className="object-cover rounded-full w-full h-full"
                        />
                      ) : (
                        <AvatarFallback className="text-lg">
                          {generateNickName(data?.username)}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    {/* <button className="text-gray-400 hover:text-gray-600">
                      <IconHeart size={20} />
                    </button> */}
                  </div>

                  {/* Name and Age */}
                  <div className="flex items-center justify-between mt-3">
                    <div className="font-semibold text-gray-800 text-lg">
                      {data?.username} <span>({calculateAge(data?.dob)})</span>
                    </div>
                    {/* <div className="flex items-center gap-1 bg-gray-100 text-sm text-gray-600">
                      <IconStack2 size={16} />
                      2491
                    </div> */}
                  </div>

                  {/* Last Active and Timezone */}
                  <div className="text-sm text-gray-500">
                    Last Active:{" "}
                    {new Date(data?.lastActivityTime).toLocaleDateString(
                      "en-US",
                      {
                        weekday: "long",
                        year: "numeric",
                        month: "short",
                        day: "numeric",
                        hour: "numeric",
                        minute: "numeric",
                      }
                    )}
                  </div>
                  <div className="text-sm text-gray-500">
                    Timezone: GMT+5:30
                  </div>

                  {/* Description */}
                  <p className="text-sm text-gray-500 mt-2 leading-snug">
                    {data?.customer_profile?.aboutMe}
                  </p>
                </div>

                {/* Section: Personal Details */}
                <div className="bg-gray-100 rounded-xl p-4">
                  <div className="font-semibold flex items-center gap-1">
                    Personal Details
                  </div>
                  <hr className="my-2"></hr>
                  <div className="grid grid-cols-2 gap-y-2 text-sm">
                    <div>
                      <div className="text-gray-500">Gender</div>
                      <div>
                        {
                          data.customer_profile?.gender?.translations?.[0]
                            ?.title
                        }
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">Relation Status</div>
                      <div>
                        {
                          data.customer_profile?.relationshipStatus
                            ?.translations?.[0]?.title
                        }
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">Personality</div>
                      <div>
                        {
                          data.customer_profile?.personality?.translations?.[0]
                            ?.title
                        }
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">Location</div>
                      <div>{data?.city}, {data?.country?.name}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Kids</div>
                      <div>{data?.customer_profile?.kids}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Sexual Orientation</div>
                      <div>
                        {
                          data.customer_profile?.sexualOrientation
                            ?.translations?.[0]?.title
                        }
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">Interests</div>
                      <div>
                        {data.interest?.length > 0
                          ? data?.interest
                              ?.map((item: any) => item.title)
                              .join(", ")
                          : "No Interests"}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Section: Physical Attributes */}
                <div className="bg-muted/50 rounded-xl p-4">
                  <div className="font-semibold flex items-center gap-1">
                    Physical Attributes
                  </div>
                  <hr className="my-2"></hr>
                  <div className="grid grid-cols-2 gap-y-2 text-sm">
                    <div>
                      <div className="text-muted-foreground">Hair Color</div>
                      <div>
                        {
                          data.customer_profile?.hairColor?.translations?.[0]
                            ?.title
                        }
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Eye Color</div>
                      <div>
                        {
                          data.customer_profile?.eyeColor?.translations?.[0]
                            ?.title
                        }
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Height</div>
                      <div>{data.customer_profile?.height} cm</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Weight</div>
                      <div>{data.customer_profile?.weight} kg</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Appearance</div>
                      <div>
                        {
                          data.customer_profile?.appearance?.translations?.[0]
                            ?.title
                        }
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Body Type</div>
                      <div>
                        {
                          data.customer_profile?.bodyType?.translations?.[0]
                            ?.title
                        }
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Body Art</div>
                      <div>
                        {
                          data.customer_profile?.bodyArt?.translations?.[0]
                            ?.title
                        }
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Best Feature</div>
                      <div>
                        {
                          data.customer_profile?.bestFeature?.translations?.[0]
                            ?.title
                        }
                      </div>
                    </div>
                  </div>
                </div>

                {/* Section: Habits */}
                <div className="bg-muted/50 rounded-xl p-4">
                  <div className="font-semibold flex items-center gap-1">
                    Lifestyle
                  </div>
                  <hr className="my-2"></hr>
                  <div className="grid grid-cols-2 gap-y-2 text-sm">
                    <div>
                      <div className="text-muted-foreground">Smoking</div>
                      <div>
                        {
                          data.customer_profile?.smokingHabit?.translations?.[0]
                            ?.title
                        }
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Drinking</div>
                      <div>
                        {
                          data.customer_profile?.drinkingHabit
                            ?.translations?.[0]?.title
                        }
                      </div>
                    </div>
                  </div>
                </div>

                {/* Section: Lifestyle and Preferences (Placeholder for future) */}
                <div className="bg-muted/50 rounded-xl p-4">
                  <div className="font-semibold mb-2 flex items-center gap-1">
                    Other Information
                  </div>
                  <hr className="my-2"></hr>
                  <div className="grid grid-cols-2 gap-y-2 text-sm">
                    <div>
                      <div className="text-muted-foreground">Seeking For</div>
                      <div>
                        {
                          data.customer_profile?.seekingFor?.translations?.[0]
                            ?.title
                        }
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Ethnicity</div>
                      <div>
                        {
                          data.customer_profile?.ethnicity?.translations?.[0]
                            ?.title
                        }
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Gallery Section */}
            <div className="">
              <h2 className="text-lg font-semibold text-foreground mb-3">
                Gallery
              </h2>
              {data?.images?.length > 0 ? (
                <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-5 max-h-[80dvh] overflow-y-auto">
                  {data?.images?.map((item, index) => (
                    <div key={index}>
                      <img
                        src={FormatS3ImgUrl(item?.image)}
                        alt={`Image ${index}`}
                        className="w-full h-auto rounded-lg"
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-gray-500">No Images</div>
              )}
            </div>
          </div>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}
