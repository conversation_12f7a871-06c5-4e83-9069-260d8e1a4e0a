import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { IconArrowLeft, IconDotsVertical } from '@tabler/icons-react'
import { Link } from '@tanstack/react-router'

interface ProfileHeaderProps {
  moderator: {
    id: string
    name: string
    moderatorType: string
    phoneNumber: string
    nickname: string
  }
}

export default function ProfileHeader({ moderator }: ProfileHeaderProps) {
  const initials = moderator.name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()

  return (
    <div className="bg-white rounded-lg border p-6">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
        <Link to="/moderators" className="flex items-center gap-1 hover:text-foreground">
          <IconArrowLeft className="h-4 w-4" />
          Moderators
        </Link>
        <span>/</span>
        <span className="text-foreground">Moderator Profile</span>
      </div>

      {/* Profile Section */}
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-4">
          {/* Avatar with status */}
          <div className="relative">
            <Avatar className="h-20 w-20">
              <AvatarImage src="" alt={moderator.name} />
              <AvatarFallback className="text-lg font-semibold bg-gray-200">
                {initials}
              </AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-1 -right-1 h-5 w-5 bg-green-500 border-2 border-white rounded-full"></div>
          </div>

          {/* Profile Info */}
          <div>
            <h1 className="text-xl font-bold mb-1">{moderator.name}</h1>

            <div className="space-y-1 text-sm">
              <div className="flex gap-8">
                <div>
                  <span className="text-muted-foreground">Role:</span>
                  <span className="ml-2 font-medium">Moderator Use</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Mobile:</span>
                  <span className="ml-2 font-medium">{moderator.phoneNumber}</span>
                </div>
              </div>
              <div className="flex gap-8">
                <div>
                  <span className="text-muted-foreground">Email:</span>
                  <span className="ml-2 font-medium"><EMAIL></span>
                </div>
                <div>
                  <span className="text-muted-foreground">Joined:</span>
                  <span className="ml-2 font-medium">23 Jan 2023</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon">
            <IconDotsVertical className="h-4 w-4" />
          </Button>
          <div className="flex flex-col gap-1 text-xs">
            <button className="text-blue-600 hover:underline">Edit Profile Details</button>
            <button className="text-blue-600 hover:underline">Reset Password</button>
            <button className="text-blue-600 hover:underline">Block User</button>
          </div>
        </div>
      </div>
    </div>
  )
}
