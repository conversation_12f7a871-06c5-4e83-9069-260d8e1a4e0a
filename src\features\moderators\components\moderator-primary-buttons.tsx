import { IconUserPlus } from "@tabler/icons-react";
import { But<PERSON> } from "@/components/ui/button";
import { useModerators } from "../context/moderators-context";
import { useRouter } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";

export function ModeratorsPrimaryButtons() {
  const { setOpen } = useModerators();
  const router = useRouter();
  return (
    <div className="flex gap-2">
      <Button
        className="space-x-1"
        onClick={() =>
          router.navigate({ to: END_POINTS.CREATE_MODERATOR })
        }
      >
        {/* <Button className='space-x-1' onClick={() => setOpen('add')}> */}
        <span>Add Moderator</span> <IconUserPlus size={18} />
      </Button>
    </div>
  );
}