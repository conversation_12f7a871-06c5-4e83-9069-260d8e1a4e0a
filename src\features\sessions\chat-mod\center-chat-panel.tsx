import UserImg from "@/assets/user.png";
import { END_POINTS } from "@/features/members/utils/constant";
import useChatStore from "@/stores/useChatStore";
import { FormatS3ImgUrl } from "@/utils/common";
import {
  IconArrowDown,
  IconArrowUp,
  IconClock,
  IconDashboard,
  IconGift,
  IconLanguage,
  IconList,
  IconMessageReply,
  IconMoodSmile,
  IconPaperclip,
  IconSearch,
  IconShareplay,
  IconUsersGroup,
} from "@tabler/icons-react";
import { useNavigate } from "@tanstack/react-router";
import { useEffect, useRef, useState, useCallback } from "react";

interface CenterChatPanelProps {
  setShowHold: (value: boolean) => void;
  setShowProblem: (value: boolean) => void;
  conversationId: number;
  currentUserId: number;
  modelInfo: Record<string, unknown>;
  customerInfo: Record<string, unknown>;
}

export default function CenterChatPanel({
  setShowHold,
  setShowProblem,
  conversationId,
  modelInfo,
  customerInfo,
}: CenterChatPanelProps) {
  const [messageInput, setMessageInput] = useState("");
  const [timeRemaining, setTimeRemaining] = useState(300); // 300 seconds = 5 minutes
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const { id: modelId, avatar: modelAvatar } = modelInfo || {};
  const { id: customerId, avatar: customerAvatar } = customerInfo || {};
  const navigate = useNavigate();

  const {
    moderatorMessages,
    moderatorMessagesMeta,
    isLoadingMoreMessages,
    "mod-sendMessage": modSendMessage,
  } = useChatStore();

  const messages = moderatorMessages[conversationId] || [];
  const meta = moderatorMessagesMeta[conversationId];

  const startTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev <= 1) {
          navigate({ to: END_POINTS.LOBY });
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, [navigate]);

  const extendTimer = useCallback(() => {
    setTimeRemaining((prev) => prev + 60);
  }, []);

  const clearTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  useEffect(() => {
    startTimer();

    return () => {
      clearTimer();
    };
  }, [startTimer, clearTimer]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleScroll = () => {
    const container = messagesContainerRef.current;
    if (!container || isLoadingMoreMessages) return;
  };

  const handleSendMessage = () => {
    if (messageInput.trim() && conversationId) {
      modSendMessage({
        message: messageInput.trim(),
        receiverId: customerId,
        senderId: modelId,
        conversationId,
      });
      setMessageInput("");
    }
  };

  // const handleKeyDown = (e: React.KeyboardEvent) => {
  //   if (e.key === "Enter" && !e.shiftKey) {
  //     e.preventDefault();
  //     handleSendMessage();
  //   }
  // };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], {
      weekday: "long",
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatTime = (seconds: number) => {
    // const mins = Math.floor(seconds / 60);
    // const secs = seconds % 60;
    // return `${mins}:${secs.toString().padStart(2, "0")}`;
    return `${seconds} sec`;
  };

  return (
    <div className="flex flex-col rounded-2xl">
      <div className="flex items-center gap-3 p-4 bg-[#171717] border-b rounded-t-2xl">
        <div className="flex items-center bg-[#444] px-3 py-2 rounded-md text-white w-full max-w-md">
          <IconSearch size={16} className="mr-2 text-gray-300" />
          <input
            type="text"
            placeholder="Search Keyword"
            className="bg-transparent text-sm text-white placeholder-gray-300 outline-none w-full"
          />
        </div>

        <div className="flex items-center bg-[#252525] text-white text-sm px-4 py-2 rounded-xs gap-3">
          <span className="flex items-center gap-1">
            {meta?.total || 0} <IconList size={14} />
          </span>
          <div className="border-l border-gray-400 h-4" />
          <span className="flex items-center gap-1">
            {meta?.page || 0} <IconArrowDown size={14} />
          </span>
          <div className="border-l border-gray-400 h-4" />
          <span className="flex items-center gap-1">
            {meta?.pages || 0} <IconArrowUp size={14} />
          </span>
        </div>

        <button
          onClick={() => setShowHold(true)}
          className="bg-white text-gray-700 text-sm px-4 py-2 rounded-xs cursor-pointer"
        >
          Hold
        </button>
      </div>

      <div
        ref={messagesContainerRef}
        onScroll={handleScroll}
        className="flex-1 bg-sidebar p-4 space-y-4 overflow-y-auto max-h-[calc(100dvh-350px)]"
      >
        {isLoadingMoreMessages && (
          <div className="text-center text-gray-500 text-sm py-2">
            Loading more messages...
          </div>
        )}

        {messages.length === 0 ? (
          <div className="text-center text-gray-500 text-sm py-8">
            No messages yet. Start the conversation!
          </div>
        ) : (
          messages.map((message, index) => {
            const isCurrentUser = message.senderId === customerId;
            const showDate =
              index === 0 ||
              new Date(message.createdAt).toDateString() !==
                new Date(messages[index - 1].createdAt).toDateString();

            return (
              <div key={message.id}>
                {showDate && (
                  <div className="text-xs text-gray-600 bg-sidebar text-center mt-4 w-fit px-4 py-2 rounded-full mx-auto">
                    {formatDate(message.createdAt)}
                  </div>
                )}

                <div
                  className={`flex gap-2 w-full max-w-[85%] ${
                    isCurrentUser ? "justify-end ms-auto" : ""
                  }`}
                >
                  {!isCurrentUser && (
                    <img
                      src={
                        modelAvatar
                          ? FormatS3ImgUrl(modelAvatar as string)
                          : UserImg
                      }
                      alt="user"
                      className="w-[40px] h-[40px] rounded-full object-cover"
                    />
                  )}

                  <div
                    className={`flex flex-col gap-2 ${
                      isCurrentUser ? "items-end" : ""
                    }`}
                  >
                    <div
                      className={`w-fit break-words px-4 py-3 rounded-xl text-sm ${
                        isCurrentUser
                          ? "rounded-se-none bg-sidebar-primary text-white"
                          : "rounded-ss-none bg-sidebar-accent"
                      }`}
                    >
                      {message.message}
                    </div>
                  </div>

                  {isCurrentUser && (
                    <img
                      src={
                        customerAvatar
                          ? FormatS3ImgUrl(customerAvatar as string)
                          : UserImg
                      }
                      alt="user"
                      className="w-[40px] h-[40px] rounded-full object-cover"
                    />
                  )}
                </div>
              </div>
            );
          })
        )}
        {/* <div ref={messagesEndRef} /> */}
      </div>

      <div className="w-full p-4 space-y-4 bg-sidebar rounded-b-2xl">
        <div className="flex gap-2 mb-2">
          <button
            className={`flex items-center gap-1 px-4 py-2 text-sm rounded-md transition-colors ${
              timeRemaining <= 60
                ? "bg-red-500 text-white animate-pulse"
                : "bg-sidebar-accent"
            }`}
          >
            <IconClock size={16} />
            {formatTime(timeRemaining)}
          </button>
          <button
            onClick={extendTimer}
            className="px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md hover:bg-sidebar-primary/80 transition-colors cursor-pointer"
          >
            + 60 Sec
          </button>
        </div>

        <div className="flex items-center justify-between border rounded-xl px-4 py-3 bg-sidebar mb-2">
          <input
            type="text"
            placeholder="Enter Message"
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            // onKeyDown={handleKeyDown}
            className="flex-1 outline-none text-md bg-transparent placeholder-gray-400"
          />
          <div className="flex items-center gap-3 text-gray-500">
            <IconLanguage size={24} />
            <IconPaperclip size={24} />
            <IconGift size={24} />
            <IconMoodSmile size={24} />
          </div>
        </div>

        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="flex gap-2">
            <button
              className="flex items-center cursor-pointer gap-1 px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleSendMessage}
              disabled={!messageInput.trim()}
            >
              <IconShareplay size={16} />
              Reply & Stay
            </button>
            <button
              onClick={() => {
                handleSendMessage();
                navigate({ to: END_POINTS.LOBY });
              }}
              disabled={!messageInput.trim()}
              className="flex items-center gap-1 px-4 py-2 cursor-pointer text-sm text-white bg-sidebar-primary rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <IconMessageReply size={16} />
              Reply
            </button>
            <button
              className="flex items-center gap-1 px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md cursor-pointer"
              onClick={() => navigate({ to: END_POINTS.LOBY })}
            >
              <IconDashboard size={16} />
              Lobby
            </button>
          </div>
          <button
            onClick={() => setShowProblem(true)}
            className="flex items-center gap-1 px-4 py-2 text-sm border rounded-md cursor-pointer"
          >
            <IconUsersGroup size={16} />
            Problem
          </button>
        </div>
      </div>
    </div>
  );
}
