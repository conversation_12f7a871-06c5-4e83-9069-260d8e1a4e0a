import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import LongText from '@/components/long-text'
import { Model } from '../data/models'
import { DataTableColumnHeader } from './data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'
import { S3_BASE_URL, toTitleCase } from '@/features/members/utils/utilities'

export const columns: ColumnDef<Model>[] = [
  {
    accessorKey: 'serialNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='#' />
    ),
    cell: ({ row }) => (
      <div className='w-8'>{row.getValue('serialNumber')}</div>
    ),
    meta: {
      className: cn(
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'w-8'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'avatar',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Profile' />
    ),
    cell: ({ row }: any) => {
      const profile = row?.getValue('profile') as string

      return (
        <div className='flex items-center gap-3'>
          <Avatar className='h-8 w-8'>
            <AvatarImage src={S3_BASE_URL + row.original.avatar} alt={profile} />
            <AvatarFallback className='text-xs bg-gray-200'>
              {row?.original?.username[0].toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <LongText className='max-w-36'>{profile}</LongText>
        </div>
      )
    },
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-0 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'username',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Username' />
    ),
    cell: ({ row }) => {

      return (
        <LongText className='max-w-36'>{row.getValue('username')}</LongText>
      )
    },
    meta: { className: 'w-36' },
  },
  {
    accessorKey: 'model_profile.country',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Country / City' />
    ),
    cell: ({ row }: any) => {
      const city = row?.original?.city
      const country = row?.original?.country?.name

      const cityDisplay = city && city.trim() !== '' ? city : ''
      const countryDisplay = country?.length ? country : ''

      // If both are available, show "country / city"
      // If only one is available, show just that value
      // If neither is available, show empty
      let displayValue = ''
      if (cityDisplay && countryDisplay) {
        displayValue = toTitleCase(countryDisplay) + ' / ' + cityDisplay
      } else if (cityDisplay) {
        displayValue = cityDisplay
      } else if (countryDisplay) {
        displayValue = countryDisplay
      } else {
        displayValue = 'N/A'
      }

      return (
        <div className='flex space-x-2'>
          <LongText className='max-w-36'>
            {displayValue}
          </LongText>
        </div>
      )
    },
    meta: { className: 'w-36' },
  },
  {
    accessorKey: 'profilePic',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Profile Pic / Extra Pic' />
    ),
    cell: ({ row }) => (
      <div className='text-center'>7/28</div>
    ),
    meta: { className: 'w-32' },
  },
  {
    accessorKey: 'customerMsg',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Customer Msg / Model Msg' />
    ),
    cell: ({ row }) => (
      <div className='text-center'>125/35</div>
    ),
    meta: { className: 'w-32' },
  },
  {
    accessorKey: 'model_profile.domainType',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Domain Type' />
    ),
    cell: ({ row }: any) => (
      <Badge variant="secondary">{row?.original?.model_profile?.domainType || "N/A"}</Badge>
    ),
    meta: { className: 'w-24' },
  },
  {
    accessorKey: 'model_profile.modelGroup',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Group' />
    ),
    cell: ({ row }: any) => (
      <Badge variant="outline">{row?.original?.model_profile?.modelGroup || "N/A"}</Badge>
    ),
    meta: { className: 'w-20' },
  },
  {
    accessorKey: 'isSuspended',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => {
      const isSuspended = row.getValue('isSuspended') as boolean
      return (
        <div className='flex space-x-2'>
          <Badge variant={!isSuspended ? 'default' : 'secondary'}>
            {!isSuspended ? 'Active' : 'Inactive'}
          </Badge>
        </div>
      )
    },
  },
  {
    accessorKey: 'botOn',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Bot On?' />
    ),
    cell: ({ row }) => (
      <div className='text-center'>
        {row.getValue('botOn') ? 'Yes' : 'No'}
      </div>
    ),
    meta: { className: 'w-20' },
  },
  {
    id: 'actions',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Action' />
    ),
    enableSorting: false,
    cell: DataTableRowActions,
    meta: { className: 'w-16' },
  },
]
