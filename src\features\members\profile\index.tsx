import { use<PERSON>ara<PERSON>, Link, useLocation, useRouter } from '@tanstack/react-router'
import ProfileCard from './components/profile-card'
import ProfilePicturesGrid from './components/profile-pictures-grid'
import CreditHistoryTable from './components/credit-history-table'
import MessageHistory from './components/message-history'
import { IconArrowLeft } from '@tabler/icons-react'
import { Main } from '@/components/layout/main'
import MemberProfileForm from './components/member-profile-form'
import ChatProfileView from './components/message-details'
import { END_POINTS } from '../utils/constant'
import { getMemberDetails } from '../api'
import { useEffect, useState } from 'react'

const PATHS: string[] = ['messages', 'pictures', 'credits']

export default function MemberProfile() {
  const { memberId } = useParams({ from: '/_authenticated/members/profile/$memberId' })
  const location = useLocation()
  const router = useRouter()

  const segments = location.pathname.split('/').filter(Boolean)
  const lastSegment = segments[segments.length - 1]
  const secondLastSegment = segments[segments.length - 2]

  const { data: members } = getMemberDetails(memberId)
  const [memberData, setMemberData] = useState(members)

  useEffect(() => {
    setMemberData(members)
  }, [members])

  const selectedSection = PATHS.includes(lastSegment)
    ? lastSegment
    : PATHS.includes(secondLastSegment)
      ? secondLastSegment
      : 'profile'

  const conversationId =
    selectedSection === 'messages' &&
      secondLastSegment === 'messages' &&
      !isNaN(Number(lastSegment))
      ? lastSegment
      : null



  if (!members) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Member not found</p>
      </div>
    )
  }

  const handleSectionChange = (section: string) => {
    router.navigate({
      to: `${END_POINTS.MEMBERS_PROFILE}/$memberId/${section}`,
      params: { memberId }
    })
  }

  const renderRightContent = () => {
    if (selectedSection === 'messages') {
      if (conversationId) {
        return <ChatProfileView />
      }
      return <MessageHistory member={memberData} />
    }

    switch (selectedSection) {
      case 'pictures':
        return <ProfilePicturesGrid member={memberData} />
      case 'credits':
        return <CreditHistoryTable member={memberData} />
      default:
        return <MemberProfileForm member={memberData} />
    }
  }

  const getSectionTitle = () => {
    switch (selectedSection) {
      case 'pictures':
        return 'Profile Pictures'
      case 'credits':
        return 'Credit History'
      case 'messages':
        return conversationId ? 'Conversation View' : 'Message History'
      default:
        return 'View Profile'
    }
  }

  return (
    <Main>
      <div className=" mx-auto">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
          <Link to="/members" className="flex items-center gap-1 hover:text-foreground">
            <IconArrowLeft className="h-4 w-4" />
            Members List
          </Link>
          <span>/</span>
          <span className="text-foreground">{getSectionTitle()}</span>
        </div>

        {/* Main Layout */}
        <div className="grid grid-cols-1 xl:grid-cols-[250px_1fr] gap-6">
          {/* Left Side - Profile Card */}
          <div>
            <ProfileCard
              member={memberData}
              activeSection={selectedSection}
              onSelectSection={handleSectionChange}
            />
          </div>

          {/* Right Side - Dynamic Content */}
          <div>
            {renderRightContent()}
          </div>
        </div>
      </div>
    </Main>
  )
}
