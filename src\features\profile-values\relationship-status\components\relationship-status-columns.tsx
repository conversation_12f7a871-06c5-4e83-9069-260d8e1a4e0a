import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";

export interface RowType { id: string; items: { languageCode: string; message: string }[]; createdAt?: string }

const ActionCell = ({ row }: { row: any }) => {
    const navigate = useNavigate();
    return (
        <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => navigate({ to: END_POINTS.RELATIONSHIP_STATUS + '/update/' + row.original.id })} className="h-8 w-8 p-0">
                <Edit className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                <Trash2 className="h-4 w-4" />
            </Button>
        </div>
    );
};

export const columns: ColumnDef<RowType>[] = [
    { accessorKey: "serialNumber", header: "S.No" },
    { accessorKey: "items", header: "Name", cell: ({ row }) => row.original.items?.[0]?.message || '-' },
    {
        accessorKey: "items", header: "Languages", cell: ({ row }) => (
            <div className="flex flex-wrap gap-1">
                {(row.original.items || []).map((i: any, idx: number) => (
                    <span key={idx} className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">{i.languageCode}</span>
                ))}
            </div>
        )
    },
    { accessorKey: "createdAt", header: "Created At", cell: ({ row }) => row.original.createdAt ? new Date(row.original.createdAt).toLocaleDateString() : '-' },
    { id: "actions", header: "Actions", cell: ActionCell },
];


