# Notification Status Bar System

This system provides a centralized way to manage and display notification status indicators across your application. The status bar shows different status types like WebSocket connection, trigger waiting, lobby, active sessions, hold, and problem counts.

## ✅ Implementation Status

**COMPLETED**: The notification status bar has been successfully implemented across your entire application!

## Components

### 1. NotificationStatusBar

The main component that displays the status indicators in the header with theme-aware styling.

### 2. NotificationStatusProvider

Context provider that manages the global state of all status items.

### 3. NotificationStatusManager

Utility functions to help create status updates.

## Setup

The system is fully set up in your application:

1. **Provider**: Added to `src/main.tsx` wrapping the entire app
2. **Component**: Added everywhere headers are visible
3. **Theme Support**: Full dark/light theme compatibility

## 🎯 Applied Everywhere

The notification status bar is now visible on ALL pages with headers:

### Common Layouts (using SharedCommonLayout):

- ✅ Models (`/models/*`)
- ✅ Bot Messages (`/bot-messages/*`)
- ✅ Flirt Messages (`/flirt-messages/*`)
- ✅ Smilies (`/smilies/*`)
- ✅ Moderators (`/moderators/*`)
- ✅ Packages (`/packages/*`)
- ✅ Members (`/members/*`)
- ✅ Settings (`/settings/*`)
- ✅ Gifts (`/gifts/*`)
- ✅ Sessions (`/sessions/*`)
- ✅ Currencies (`/currencies/*`)

### Individual Pages (directly updated):

- ✅ Dashboard (`/`)
- ✅ Announcements (`/announcements/*`)
- ✅ Users (`/users`)
- ✅ Contact Us (`/contact-us`)
- ✅ Admin Login Activity (`/admin-login-activity`)
- ✅ Chats (`/chats`)

## 🎨 Theme-Aware Design

The notification status bar automatically adapts to your theme:

- **Light Theme**: Clean, bright colors with subtle shadows
- **Dark Theme**: Darker variants with proper contrast
- **Responsive**: Works on all screen sizes
- **Smooth Transitions**: Hover effects and animations

## Usage

### Basic Usage in Components

```tsx
import { useNotificationStatus } from "@/context/notification-status-context";
import { STATUS_IDS } from "@/utils/notification-status-manager";

function MyComponent() {
  const { updateStatusCount, updateMultipleStatus } = useNotificationStatus();

  // Update single status
  const handleUpdateActive = () => {
    updateStatusCount(STATUS_IDS.ACTIVE, 5);
  };

  // Update multiple statuses
  const handleUpdateMultiple = () => {
    updateMultipleStatus([
      { id: STATUS_IDS.LOBBY, count: 3 },
      { id: STATUS_IDS.PROBLEM, count: 1 },
    ]);
  };

  return (
    <div>
      <button onClick={handleUpdateActive}>Update Active Count</button>
      <button onClick={handleUpdateMultiple}>Update Multiple</button>
    </div>
  );
}
```

### Using with API Responses

```tsx
import { handleSessionStatusUpdate } from "@/utils/notification-status-manager";

// In your API call or WebSocket handler
const handleApiResponse = (response) => {
  const updates = handleSessionStatusUpdate({
    websocketConnected: true,
    triggerWaiting: 2,
    lobby: 5,
    active: 10,
    hold: 1,
    problem: 0,
  });

  updateMultipleStatus(updates);
};
```

### Available Status IDs

```tsx
export const STATUS_IDS = {
  WEBSOCKET: "websocket",
  TRIGGER_WAITING: "trigger-waiting",
  LOBBY: "lobby",
  ACTIVE: "active",
  HOLD: "hold",
  PROBLEM: "problem",
};
```

## Customization

### Changing Status Items

To modify the default status items, update the `defaultStatusItems` array in `src/context/notification-status-context.tsx`:

```tsx
const defaultStatusItems: StatusItem[] = [
  {
    id: "websocket",
    label: "Web Socket",
    count: 0,
    variant: "info",
    className: "bg-blue-500 hover:bg-blue-600 text-white",
  },
  // Add or modify items here
];
```

### Styling

Each status item can have custom styling through the `className` property. The component uses Tailwind CSS classes.

## Integration Examples

### WebSocket Integration

```tsx
useEffect(() => {
  const ws = new WebSocket("ws://your-websocket-url");

  ws.onopen = () => {
    updateStatusCount(STATUS_IDS.WEBSOCKET, 1);
  };

  ws.onclose = () => {
    updateStatusCount(STATUS_IDS.WEBSOCKET, 0);
  };

  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    const updates = handleSessionStatusUpdate(data);
    updateMultipleStatus(updates);
  };
}, []);
```

### API Polling

```tsx
useEffect(() => {
  const interval = setInterval(async () => {
    try {
      const response = await fetch("/api/session-status");
      const data = await response.json();
      const updates = handleSessionStatusUpdate(data);
      updateMultipleStatus(updates);
    } catch (error) {
      console.error("Failed to fetch status:", error);
    }
  }, 5000); // Poll every 5 seconds

  return () => clearInterval(interval);
}, []);
```

## Files Structure

```
src/
├── components/
│   ├── notification-status-bar.tsx          # Main component
│   ├── layout/
│   │   └── shared-common-layout.tsx         # Shared layout with status bar
│   └── examples/
│       └── notification-status-example.tsx  # Usage examples
├── context/
│   └── notification-status-context.tsx      # Context provider and hook
└── utils/
    └── notification-status-manager.ts       # Utility functions
```

## Testing

To test the notification status system, you can use the example component:

```tsx
import { NotificationStatusExample } from "@/components/examples/notification-status-example";

// Add this to any page to test the functionality
<NotificationStatusExample />;
```

This will provide buttons to test various update scenarios and see the current status values.
