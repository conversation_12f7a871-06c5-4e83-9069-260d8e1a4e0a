import { Main } from "@/components/layout/main";
import { PackageTable } from "./components/package-table";
import { columns } from "./components/Packages-columns";

export default function List() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">
                        Packages
                    </h2>
                </div>
                {/* <PackagePrimaryButtons /> */}
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <PackageTable columns={columns} />
            </div>
        </Main>
    );
} 