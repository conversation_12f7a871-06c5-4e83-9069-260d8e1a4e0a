'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
    <PERSON><PERSON>,
    Dialog<PERSON>ontent,
    Dialog<PERSON>ooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog'
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { AnnouncementNew } from '../data/schema'
import { createAnnouncementApi } from '../api'
import { toast } from 'sonner'
import { useQueryClient } from '@tanstack/react-query'


const formSchema = z.object({
    type: z.string().min(1, 'Please select a moderator'),
    content: z.string().min(1, 'Please enter announcement content'),
})

type AnnouncementForm = z.infer<typeof formSchema>

const moderatorOptions = [
    { label: 'Whitelabel', value: 'WHITE_LABEL' },
    { label: 'Moderators', value: 'MODERATOR' },
    { label: 'Affiliates', value: 'AFFILIATE' },
]


interface Props {
    currentRow?: AnnouncementNew
    open: boolean
    onOpenChange: (open: boolean) => void
}

export function AnnouncementActionDialog({ currentRow, open, onOpenChange }: Props) {
    const { mutateAsync: createAnnouncementMutation } = createAnnouncementApi()
    const queryClient = useQueryClient()
    const form = useForm<AnnouncementForm>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            type: '',
            content: '',
        }
    })

    const onSubmit = async (values: AnnouncementForm) => {
        const response: any = await createAnnouncementMutation(values)
        queryClient.invalidateQueries({ queryKey: ["active-announcement-list", { type: values.type }] })
        if (response?.success) {
            toast.success("Announcement Created Successfully!")
        }
        form.reset()
        onOpenChange(false)
    }

    const handleCancel = () => {
        form.reset()
        onOpenChange(false)
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[calc(100vw-20px)] lg:max-w-[calc(600px)]">
                <DialogHeader>
                    <DialogTitle>
                        Add Announcement
                    </DialogTitle>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="type"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Moderator</FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger style={{ width: 'auto', border: "1px solid" }}>
                                                <SelectValue placeholder="Select Type" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {moderatorOptions.map((option) => (
                                                <SelectItem key={option.value} value={option.value}>
                                                    {option.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="content"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Announcement Content</FormLabel>
                                    <FormControl>
                                        <Textarea style={{ border: "1px solid" }} {...field} placeholder="Enter announcement content..." />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <DialogFooter className="gap-2">
                            <Button style={{ border: "1px solid", cursor: 'pointer' }} type="button" variant="outline" onClick={handleCancel}>
                                Cancel
                            </Button>
                            <Button type="submit" style={{ cursor: 'pointer' }}>
                                Send
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    )
}
