import {
  CountryDomainData,
  AffiliateData,
  SignupData,
  SalesData,
} from '../components/dashboard-table'

// Sample data for Country/Domain Statistics
export const countryDomainData: CountryDomainData[] = [
  { rank: 1, country: 'United States', sales: '€150', clicks: '5,000', signups: '10' },
  { rank: 2, country: 'China', sales: '€140', clicks: '5,400', signups: '12' },
  { rank: 3, country: 'Japan', sales: '€130', clicks: '5,100', signups: '8' },
  { rank: 4, country: 'Germany', sales: '€120', clicks: '4,800', signups: '15' },
  { rank: 5, country: 'India', sales: '€3,370.15', clicks: '5,000', signups: '300' },
  { rank: 6, country: 'United Kingdom', sales: '€3,320.00', clicks: '5,000', signups: '250' },
  { rank: 7, country: 'France', sales: '€3,300.22', clicks: '5,200', signups: '220' },
  { rank: 8, country: 'Italy', sales: '€3,200.00', clicks: '5,400', signups: '200' },
  { rank: 9, country: 'Brazil', sales: '€3,200.88', clicks: '5,400', signups: '400' },
  { rank: 10, country: 'Canada', sales: '€500.62', clicks: '5,000', signups: '400' },
]

// Sample data for Top 10 Affiliates
export const affiliateData: AffiliateData[] = [
  { rank: 1, name: 'Starlight A', sales: '€5,000 (+3%)', clicks: '5,000 (+3%)', signups: '50 (+3%)' },
  { rank: 2, name: 'Daniel M', sales: '€900 (+5%)', clicks: '5,000 (+3%)', signups: '22 (+3%)' },
  { rank: 3, name: 'Samantha K', sales: '€700 (+2%)', clicks: '5,000 (+3%)', signups: '18 (+3%)' },
  { rank: 4, name: 'Robert L', sales: '€600 (+4%)', clicks: '5,000 (+3%)', signups: '15 (+3%)' },
  { rank: 5, name: 'Jennifer P', sales: '€3,370.15 (+3%)', clicks: '5,000 (+3%)', signups: '300 (+3%)' },
  { rank: 6, name: 'Michael S', sales: '€3,320.00 (+2%)', clicks: '5,000 (+3%)', signups: '250 (+3%)' },
  { rank: 7, name: 'Sarah W', sales: '€3,300.22 (+1%)', clicks: '5,000 (+3%)', signups: '220 (+3%)' },
  { rank: 8, name: 'David B', sales: '€3,200.00 (+5%)', clicks: '5,000 (+3%)', signups: '200 (+3%)' },
  { rank: 9, name: 'Emily R', sales: '€3,200.88 (+3%)', clicks: '5,000 (+3%)', signups: '180 (+3%)' },
  { rank: 10, name: 'Andrew H', sales: '€500.62 (+2%)', clicks: '5,000 (+3%)', signups: '150 (+3%)' },
]

// Sample data for Signups
export const signupData: SignupData[] = [
  { rank: 1, domain: 'flirtzone.com', signups: '150' },
  { rank: 2, domain: 'passionhub.net', signups: '800' },
  { rank: 3, domain: 'lovequest.org', signups: '500' },
  { rank: 4, domain: 'secretlovers.co', signups: '400' },
  { rank: 5, domain: 'adultmatch.com', signups: '180' },
  { rank: 6, domain: 'romanticzone.club', signups: '567' },
  { rank: 7, domain: 'heartconnect.club', signups: '400' },
  { rank: 8, domain: 'lovebirds.club', signups: '350' },
  { rank: 9, domain: 'cupidzone.club', signups: '300' },
  { rank: 10, domain: 'sweethearts.club', signups: '250' },
]

// Sample data for Sales
export const salesData: SalesData[] = [
  { rank: 1, domain: 'flirtzone.com', sales: '€150' },
  { rank: 2, domain: 'passionhub.net', sales: '€150' },
  { rank: 3, domain: 'lovequest.org', sales: '€150' },
  { rank: 4, domain: 'secretlovers.co', sales: '€150' },
  { rank: 5, domain: 'adultmatch.com', sales: '€150' },
  { rank: 6, domain: 'romanticzone.club', sales: '€150' },
  { rank: 7, domain: 'heartconnect.club', sales: '€150' },
  { rank: 8, domain: 'lovebirds.club', sales: '€150' },
  { rank: 9, domain: 'cupidzone.club', sales: '€150' },
  { rank: 10, domain: 'sweethearts.club', sales: '€150' },
]

// Sample data for WL Affiliates
export const wlAffiliateData: SalesData[] = [
  { rank: 1, domain: 'WhiteLabel', sales: '€150' },
  { rank: 2, domain: 'AdultFriends', sales: '€150' },
  { rank: 3, domain: 'CamFriends', sales: '€150' },
  { rank: 4, domain: 'AdultFun', sales: '€150' },
  { rank: 5, domain: 'SexHot', sales: '€150' },
  { rank: 6, domain: 'HotCams', sales: '€150' },
  { rank: 7, domain: 'AdultChat', sales: '€150' },
  { rank: 8, domain: 'SexChat', sales: '€150' },
  { rank: 9, domain: 'HotChat', sales: '€150' },
  { rank: 10, domain: 'AdultVideo', sales: '€150' },
]
