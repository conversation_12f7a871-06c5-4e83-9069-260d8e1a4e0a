import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>Axis,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>lt<PERSON>,
    ResponsiveContainer,
    Legend,
} from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { ChevronDown, ArrowUpRight } from 'lucide-react'
import { useState } from 'react'

const days = Array.from({ length: 30 }, (_, i) => (i + 1).toString().padStart(2, '0'))

const generateData = () =>
    days.map((day) => ({
        day,
        lobby: Math.floor(Math.random() * 20000 + 20000),
        normal: Math.floor(Math.random() * 20000 + 30000),
        push: Math.floor(Math.random() * 20000 + 10000),
        received: Math.floor(Math.random() * 20000 + 40000),
    }))

const options = ['Sign Ups', 'Clicks', 'Sales', 'Messages', 'Country', 'Domain']

export default function MessageStatsChart() {
    const [selected, setSelected] = useState('Messages')
    const data = generateData()

    return (
        <Card>
            <CardHeader className='flex flex-col xl:flex-row items-start justify-between space-y-0'>
                <div className='flex flex-row flex-wrap justify-between flex-1 
                [&_.recharts-default-legend]:text-left 
                [&_.recharts-legend-wrapper]:grid 
                [&_.recharts-legend-wrapper]:grid-cols-[repeat(auto-fit,minmax(135px,1fr))]
                [&_.recharts-legend-wrapper]:gap-[5px]
                lg:[&_.recharts-legend-wrapper]:w-[max-content]
                [&_.recharts-legend-wrapper]:w-full
                [&_.recharts-default-legend]:flex
                [&_.recharts-default-legend]:flex-wrap
                [&_.recharts-default-legend]:gap-2
                gap-[15px]'>
                    <CardTitle className='text-lg font-semibold'>Statistics</CardTitle>
                    <Legend
                        wrapperStyle={{ position: 'initial', justifyContent: 'space-between', marginInline: 'auto' }}
                        payload={[
                            { value: 'Lobby Message', type: 'circle', color: '#333' },
                            { value: 'Normal Message', type: 'circle', color: '#666' },
                            { value: 'Push Message', type: 'circle', color: '#aaa' },
                            { value: 'Received Message', type: 'circle', color: '#ddd' },
                        ]}
                    />
                </div>

                <div className='flex items-center gap-2'>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant='outline' className='text-sm'>
                                {selected} <ChevronDown className='ml-2 h-4 w-4' />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            {options.map((option) => (
                                <DropdownMenuItem key={option} onClick={() => setSelected(option)}>
                                    {option}
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                    <Button variant='ghost' size='icon'>
                        <ArrowUpRight className='h-4 w-4' />
                    </Button>
                </div>
            </CardHeader>
            <CardContent>
                <div className='w-full h-[300px]'>
                    <ResponsiveContainer width='100%' height='100%'>
                        <BarChart data={data}>
                            <XAxis dataKey='day' tickLine={false} axisLine={false} />
                            <YAxis tickLine={false} axisLine={false} tickFormatter={(val) => `${val / 1000}k`} />
                            <Tooltip formatter={(value) => new Intl.NumberFormat().format(Number(value))} />
                            <Bar barSize={8} dataKey='lobby' stackId='a' fill='#333' radius={[2, 2, 0, 0]} />
                            <Bar barSize={8} dataKey='normal' stackId='a' fill='#666' radius={[2, 2, 0, 0]} />
                            <Bar barSize={8} dataKey='push' stackId='a' fill='#aaa' radius={[2, 2, 0, 0]} />
                            <Bar barSize={8} dataKey='received' stackId='a' fill='#ddd' radius={[2, 2, 0, 0]} />
                        </BarChart>
                    </ResponsiveContainer>
                </div>
            </CardContent>
        </Card>
    )
}
