import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "@tanstack/react-router";
import { END_POINTS } from "@/features/members/utils/constant";
import { Gift } from "lucide-react";

export function PackagePrimaryButtons() {
    const router = useRouter();

    return (
        <div className="flex gap-2">
            <Button
                className="space-x-1"
                onClick={() =>
                    router.navigate({ to: END_POINTS.ADD_GIFT })
                }
            >
                <span>Add Gift</span> <Gift size={18} />
            </Button>
        </div>
    );
}
