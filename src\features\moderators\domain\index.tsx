import { Main } from "@/components/layout/main";
import { DomainTable } from "./components/domain-table";
import { columns } from "./components/domain-columns";
import { domains } from "./data/domains";
import { DomainPrimaryButtons } from "./components/moderator-primary-buttons";

export default function Domain() {
  return (
    <Main>
      <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Manage moderator domain assignments and configurations
          </h2>
        </div>
        <DomainPrimaryButtons />
      </div>

      <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
        <DomainTable columns={columns} data={domains} />
      </div>
    </Main>
  );
}