import { Main } from "@/components/layout/main";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate, useParams } from "@tanstack/react-router";
import { useState, useRef, useEffect } from "react";
import { toast } from "sonner";
import { Plus } from "lucide-react";
import { useAuthStore } from "@/stores/authStore";
import { getPresignedUrl, uploadFileToS3 } from "@/features/gifts/api";
import { validateImageFile } from "@/features/members/utils/utilities";
import { addGifApi, getGifDetails, updateGifApi } from "../api";
import { END_POINTS } from "@/features/members/utils/constant";

const gifSchema = z.object({
    image: z.string().min(1, "Image is required"),
    type: z.enum(["Adult", "Non Adult"], { required_error: "GIF type is required." }),
});

type GifFormValues = z.infer<typeof gifSchema>;

const groupOptions = [
    { label: "Adult", value: "Adult" },
    { label: "Non Adult", value: "Non Adult" },
];

export default function AddGif() {
    const navigate = useNavigate()
    const imageInputRef = useRef<HTMLInputElement>(null);
    const { auth: { setShowSpinner } } = useAuthStore((state) => state)

    const [fileName, setFileName] = useState<string>("")
    const [profileImage, setProfileImage] = useState<string>("")

    const { mutateAsync: addGifMutation } = addGifApi()
    const { mutateAsync: updateGifMutation } = updateGifApi()
    const { mutateAsync: getPreSignedUrlMutation } = getPresignedUrl()
    const { mutateAsync: uploadFileToS3Mutation } = uploadFileToS3()

    const { msgId } = useParams({ strict: false });
    const { data = {} } = getGifDetails(msgId);

    const form = useForm<GifFormValues>({
        resolver: zodResolver(gifSchema),
        defaultValues: {
            image: undefined,
            type: "Adult",
        },
    });

    const { control, handleSubmit, setValue, watch } = form;

    useEffect(() => {
        if (data && data.image) {
            setValue("image", data.image);
            setProfileImage(import.meta.env.VITE_S3_BASE_URL + data.image);
        }
        if (data && data.type) {
            setValue("type", data.type);
        }
    }, [data, setValue]);

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;
        const error = validateImageFile(file);
        if (error) {
            toast.error(error);
            return;
        }
        try {
            const ext = file.name.split(".").pop()?.toLowerCase() || "gif";
            const presignedRes: any = await getPreSignedUrlMutation({
                location: "gifs",
                type: ext,
                count: 1,
            });
            const fileData = presignedRes?.data?.files?.[0];
            if (!fileData) {
                toast.error("Failed to get S3 upload URL.");
                return;
            }
            await uploadFileToS3Mutation({
                url: fileData.url,
                file,
            });
            setValue("image", fileData.filename)
            setFileName(fileData.filename)
            setProfileImage(import.meta.env.VITE_S3_BASE_URL + fileData.filename)
        } catch (err: any) {
            console.log(err);
        } finally {
            e.target.value = ""
        }
    }

    const onSubmit = async (values: GifFormValues) => {
        // setShowSpinner(true);

        if (typeof msgId === "string") {
            const response: any = await updateGifMutation({
                ...values,
                id: data?.id
            })
            if (response?.success) {
                toast.success("GIF has been updated!")
            }
        } else {
            const response: any = addGifMutation(values)
            // TODO: Implement API call to upload image and create gif
            toast.success("GIF created successfully!");
            navigate({ to: END_POINTS.GIFS });
        }
    };

    return (
        <Main>
            <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight md:text-3xl">
                    Create GIF
                </h1>
                <p className="text-muted-foreground">Add a new GIF to the store.</p>
            </div>
            <Separator className="my-4 lg:my-3" />
            <div className="flex flex-1">
                <Form {...form}>
                    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4 w-full">
                        <Card>
                            <CardContent className="grid grid-cols-1 gap-4 mt-6">
                                {/* Image Upload Field */}
                                <FormField
                                    control={control}
                                    name="image"
                                    render={() => (
                                        <FormItem>
                                            <FormLabel>GIF Image File</FormLabel>
                                            <FormControl>
                                                <div
                                                    className="w-16 h-16 flex justify-center items-center border-2 border-dashed rounded-lg cursor-pointer relative overflow-hidden bg-muted"
                                                    onClick={() => imageInputRef.current?.click()}
                                                >
                                                    {profileImage ? (
                                                        <img src={profileImage} alt="Image Preview" className="h-full w-full object-cover" />
                                                    ) : (
                                                        <Plus className="h-6 w-6 text-muted-foreground" />
                                                    )}
                                                    <Input
                                                        type="file"
                                                        ref={imageInputRef}
                                                        className="hidden"
                                                        onChange={handleFileChange}
                                                        accept="image/gif,image/*"
                                                    />
                                                </div>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                {/* GIF Type Dropdown */}
                                <FormField
                                    control={control}
                                    name="type"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>GIF Type</FormLabel>
                                            <FormControl>
                                                <select
                                                    {...field}
                                                    className="block w-full border rounded-md px-3 py-2"
                                                >
                                                    <option value="" disabled>Select type</option>
                                                    {groupOptions.map(option => (
                                                        <option key={option.value} value={option.value}>{option.label}</option>
                                                    ))}
                                                </select>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </CardContent>
                        </Card>
                        <div className="flex mt-4 justify-end">
                            <Button type="submit">Save</Button>
                        </div>
                    </form>
                </Form>
            </div>
        </Main>
    );
}
