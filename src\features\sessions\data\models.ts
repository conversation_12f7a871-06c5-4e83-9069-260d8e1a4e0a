import { z } from "zod";

export interface Model {
  id: string
  profile: string
  username: string
  countryCity: string
  profilePic: string
  customerMsg: number
  modelMsg: number
  domainType: string
  group: string
  botOn: boolean
}

export const models: Model[] = [
  {
    id: '1',
    profile: '<PERSON> (31)',
    username: '<EMAIL>',
    countryCity: 'USA / Denver',
    profilePic: '1/38',
    customerMsg: 2,
    modelMsg: 4,
    domainType: 'Fashion',
    group: 'Teen',
    botOn: false
  },
  {
    id: '2',
    profile: '<PERSON> (29)',
    username: '<EMAIL>',
    countryCity: 'Mexico / Guadalajara',
    profilePic: '2/38',
    customerMsg: 5,
    modelMsg: 1,
    domainType: 'Fitness',
    group: 'Latin',
    botOn: true
  },
  {
    id: '3',
    profile: '<PERSON> (26)',
    username: '<EMAIL>',
    countryCity: 'Portugal / Lisbon',
    profilePic: '3/38',
    customerMsg: 3,
    modelMsg: 2,
    domainType: 'Travel',
    group: 'European',
    botOn: false
  },
  {
    id: '4',
    profile: '<PERSON>(32)',
    username: '<EMAIL>',
    countryCity: 'Ireland / Cork',
    profilePic: '4/38',
    customerMsg: 1,
    modelMsg: 6,
    domainType: 'Adult',
    group: 'Gay',
    botOn: true
  },
  {
    id: '5',
    profile: 'Mina Kapoor (27)',
    username: '<EMAIL>',
    countryCity: 'India / Mumbai',
    profilePic: '5/38',
    customerMsg: 4,
    modelMsg: 2,
    domainType: 'Beauty',
    group: 'Desi',
    botOn: false
  },
  {
    id: '6',
    profile: 'John Smith (41)',
    username: '<EMAIL>',
    countryCity: 'Canada / Toronto',
    profilePic: '6/38',
    customerMsg: 6,
    modelMsg: 0,
    domainType: 'Adult',
    group: 'Milf',
    botOn: true
  },
  {
    id: '7',
    profile: 'Emilia Clarke (34)',
    username: '<EMAIL>',
    countryCity: 'UK / London',
    profilePic: '7/38',
    customerMsg: 2,
    modelMsg: 3,
    domainType: 'Cinema',
    group: 'Celebrity',
    botOn: false
  },
  {
    id: '8',
    profile: 'Hiro Tanaka (30)',
    username: '<EMAIL>',
    countryCity: 'Japan / Tokyo',
    profilePic: '8/38',
    customerMsg: 1,
    modelMsg: 5,
    domainType: 'Adult',
    group: 'Asian',
    botOn: true
  },
  {
    id: '9',
    profile: 'Ricky Smith (23)',
    username: '<EMAIL>',
    countryCity: 'Ireland / Youghal',
    profilePic: '9/38',
    customerMsg: 0,
    modelMsg: 0,
    domainType: 'Adult',
    group: 'Milf',
    botOn: true
  },
  {
    id: '10',
    profile: 'Fatima Zahra (28)',
    username: '<EMAIL>',
    countryCity: 'Morocco / Rabat',
    profilePic: '10/38',
    customerMsg: 3,
    modelMsg: 2,
    domainType: 'Education',
    group: 'Arab',
    botOn: false
  },
  {
    id: '11',
    profile: 'Marcus Lee (36)',
    username: '<EMAIL>',
    countryCity: 'Hong Kong / Central',
    profilePic: '11/38',
    customerMsg: 2,
    modelMsg: 2,
    domainType: 'Finance',
    group: 'Asian',
    botOn: true
  },
  {
    id: '12',
    profile: 'Laura Kim (24)',
    username: '<EMAIL>',
    countryCity: 'South Korea / Seoul',
    profilePic: '12/38',
    customerMsg: 0,
    modelMsg: 1,
    domainType: 'Fashion',
    group: 'K-Pop',
    botOn: false
  },
  {
    id: '13',
    profile: 'Nina Petrova (30)',
    username: '<EMAIL>',
    countryCity: 'Russia / Moscow',
    profilePic: '13/38',
    customerMsg: 4,
    modelMsg: 4,
    domainType: 'News',
    group: 'Eastern',
    botOn: true
  },
  {
    id: '14',
    profile: 'Peter Zhang (33)',
    username: '<EMAIL>',
    countryCity: 'China / Shanghai',
    profilePic: '14/38',
    customerMsg: 1,
    modelMsg: 3,
    domainType: 'Adult',
    group: 'Asian',
    botOn: false
  },
  {
    id: '15',
    profile: 'Olivia Brown (29)',
    username: '<EMAIL>',
    countryCity: 'Australia / Sydney',
    profilePic: '15/38',
    customerMsg: 3,
    modelMsg: 3,
    domainType: 'Lifestyle',
    group: 'Model',
    botOn: true
  },
  {
    id: '16',
    profile: 'Amir Khan (38)',
    username: '<EMAIL>',
    countryCity: 'India / Delhi',
    profilePic: '16/38',
    customerMsg: 2,
    modelMsg: 5,
    domainType: 'Politics',
    group: 'Desi',
    botOn: false
  },
  {
    id: '17',
    profile: 'Emma Green (26)',
    username: '<EMAIL>',
    countryCity: 'Germany / Berlin',
    profilePic: '17/38',
    customerMsg: 1,
    modelMsg: 2,
    domainType: 'Adult',
    group: 'Euro',
    botOn: true
  },
  {
    id: '18',
    profile: 'Lucas Silva (27)',
    username: '<EMAIL>',
    countryCity: 'Brazil / São Paulo',
    profilePic: '18/38',
    customerMsg: 0,
    modelMsg: 6,
    domainType: 'Sports',
    group: 'Latin',
    botOn: false
  },
  {
    id: '19',
    profile: 'Jasmine Lee (25)',
    username: '<EMAIL>',
    countryCity: 'Malaysia / Kuala Lumpur',
    profilePic: '19/38',
    customerMsg: 1,
    modelMsg: 1,
    domainType: 'Adult',
    group: 'Asian',
    botOn: true
  },
  {
    id: '20',
    profile: 'Daniel Meier (35)',
    username: '<EMAIL>',
    countryCity: 'Switzerland / Zurich',
    profilePic: '20/38',
    customerMsg: 5,
    modelMsg: 0,
    domainType: 'Finance',
    group: 'European',
    botOn: false
  },

];

export const modelCreateSchema = z.object({
  username: z.string().min(1, "Username is required"),
  gender: z.string().min(1, "Gender is required"),
  seekingFor: z.string().min(1, "Seeking For is required"),
  dob: z.string()
    .min(1, "Date of Birth is required")
    .refine((val) => {
      const dob = new Date(val);
      const today = new Date();
      const age18 = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
      return dob <= age18;
    }, { message: "Model must be at least 18 years old" }),
  domainType: z.string().min(1, "Domain Type is required"),
  modelGroup: z.string().min(1, "Model Group is required"),
  country: z.string().min(1, "Country is required"),
  city: z.string().min(1, "City is required"),
  personality: z.string().min(1, "Personality is required"),
  relationshipStatus: z.string().min(1, "Relationship Status is required"),
  ethnicity: z.string().min(1, "Ethnicity is required"),
  hairColor: z.string().min(1, "Hair Color is required"),
  appearance: z.string().min(1, "Appearance is required"),
  eyeColor: z.string().min(1, "Eye Color is required"),
  bodyType: z.string().min(1, "Body Type is required"),
  smokingHabit: z.string().min(1, "Smoking Habit is required"),
  drinkingHabit: z.string().min(1, "Drinking Habit is required"),
  bestFeature: z.string().min(1, "Best Feature is required"),
  bodyArt: z.string().min(1, "Body Art is required"),
  sexualOrientation: z.string().min(1, "Sexual Orientation is required"),
  height: z.string().min(1, "Height is required"),
  weight: z.string().min(1, "Weight is required"),
  kids: z.string().min(1, "Kids is required"),
  interests: z.string().min(1, "Interests is required"),
  aboutMe: z.string().optional(),
});

export type ModelFormValues = z.infer<typeof modelCreateSchema>;

