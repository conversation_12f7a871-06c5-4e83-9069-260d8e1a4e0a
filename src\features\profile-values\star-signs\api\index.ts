import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getStarSignsApi = (params: any = {}) => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.STAR_SIGNS, { params })).data ?? {},
    queryKey: ["star-signs-list"],
});

export const addStarSignsApi = () => useMutation({
    mutationFn: async (data: any) => (await apiClient.post(API_ENDPOINTS.STAR_SIGNS, data)).data,
});

export const updateStarSignsApi = () => useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => (await apiClient.put(`${API_ENDPOINTS.STAR_SIGNS}/${id}`, data)).data,
});

export const deleteStarSignsApi = () => useMutation({
    mutationFn: async (id: string) => (await apiClient.delete(`${API_ENDPOINTS.STAR_SIGNS}/${id}`)).data,
});

export const getStarSignsDetails = (id: string) => useQuery({
    queryFn: async () => (await apiClient.get(`${API_ENDPOINTS.STAR_SIGNS}/${id}`)).data ?? {},
    queryKey: ["star-signs-details", id],
    enabled: !!id,
});

export const getMasterLanguagesApi = () => useQuery({
    queryFn: async () => (await apiClient.get(API_ENDPOINTS.MASTER_LANGUAGES)).data ?? {},
    queryKey: ["master-languages"],
});
