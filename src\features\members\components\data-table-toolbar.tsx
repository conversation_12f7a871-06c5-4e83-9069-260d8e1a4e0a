import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { FilterSelect } from '@/components/select-dropdown-popover'
import { useEffect, useMemo, useState } from 'react'
// import { DatePicker } from '@/components/date-picker'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Command, CommandEmpty, CommandGroup, CommandInput } from '@/components/ui/command'
import { CommandItem } from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { getCountryList, getMasterApi } from '../api'
import { ADD_CITIES, CITY_PLACEHOLDER } from '@/utils/constant'
import { Input } from '@/components/ui/input'

interface DataTableToolbarProps<TData> {
  readonly table: Table<TData>
  readonly onFilterChanged?: any
}

const domainTypeOptions = [
  'discreetdating.club',
  'passionhub.net',
  'lovequest.org',
  'secretlovers.co',
  'adultmatch.com',
  'flirtzone.com'
]

const buyerStatusOptions = [
  'Buyer',
  'Non Buyer'
]

const userStatusOptions = [
  'Affiliate',
  'Non-affiliate'
]

const onlineStatusOptions = [
  'Online',
  'Offline'
]

const pictureStatusOptions = [
  'Approved',
  'Non Approved'
]

export function DataTableToolbar<TData>({
  table,
  onFilterChanged
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const { data: masterData } = getMasterApi()
  const { data: countryList } = getCountryList()
  const [countries, setCountries] = useState<any>(countryList?.country || [])

  const masterOptions = useMemo(() => {
    const master = masterData?.master || {};
    return {
      model_group: master.model_group || [],
    };
  }, [masterData]);

  // Use model_group from masterOptions as groupOptions
  const groupOptions = masterOptions.model_group || [];

  const [filters, setFilters] = useState<{
    search: string;
    domain: string;
    country: string;
    city: string;
    cityInput: string;
    isPaidUser: string;
    status: string;
    isOnline: string;
    imageApproved: string;
    group: string;
  }>({
    search: "",
    domain: '',
    country: '',
    city: '',
    cityInput: '',
    isPaidUser: '',
    status: '',
    isOnline: '',
    imageApproved: '',
    group: '',
  })

  const [hasSearched, setHasSearched] = useState(false)
  const [cityDropdownOpen, setCityDropdownOpen] = useState(false)

  useEffect(() => {
    setCountries(countryList?.country || [])
  }, [countryList?.country?.length])
  const [initialFilters, setInitialFilters] = useState<{
    search: string;
    domain: string;
    country: string;
    city: string;
    cityInput: string;
    isPaidUser: string;
    status: string;
    isOnline: string;
    imageApproved: string;
    group: string;
  }>({
    search: "",
    domain: '',
    country: '',
    city: '',
    cityInput: '',
    isPaidUser: '',
    status: '',
    isOnline: '',
    imageApproved: '',
    group: '',
  })

  const handleFilterChange = (
    key: 'search' | 'domain' | 'country' | 'city' | 'cityInput' | 'isPaidUser' | 'status' | 'isOnline' | 'imageApproved' | 'group',
    value: string | string[] | number[]
  ) => {
    // For multi-selects, convert array to comma-separated string
    if (["domain", "country", "city", "group"].includes(key)) {
      if (Array.isArray(value)) {
        value = value.filter((v) => v !== '' && v !== undefined && v !== null).join(',');
      }
    }
    setFilters((prev) => {
      const updated = { ...prev, [key]: value };
      return updated;
    });
  }

  const handleSearch = () => {
    // // Apply filters to the table
    // if (filters.search) {
    //   const searchColumn = table.getColumn('username')
    //   if (searchColumn) {
    //     searchColumn.setFilterValue(filters.search)
    //   }
    // }

    // if (filters?.domain && filters?.domain?.length > 0) {
    //   const domainColumn = table.getColumn('domainType')
    //   if (domainColumn) {
    //     domainColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string) => {
    //       return filterValue.split(',').includes(row.original.domainType)
    //     }
    //     domainColumn.setFilterValue(filters.domain)
    //   }
    // }
    // if (filters?.country && filters?.country?.length > 0) {
    //   const countryColumn = table.getColumn('country')
    //   if (countryColumn) {
    //     countryColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string) => {
    //       return filterValue.split(',').map(Number).includes(row.original.country)
    //     }
    //     countryColumn.setFilterValue(filters.country)
    //   }
    // }
    // if (filters?.city && filters?.city?.length > 0) {
    //   const cityColumn = table.getColumn('city')
    //   if (cityColumn) {
    //     cityColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: string) => {
    //       return filterValue.split(',').includes(row.original.city)
    //     }
    //     cityColumn.setFilterValue(filters.city)
    //   }
    // }
    // if (filters?.isPaidUser) {
    //   const buyerStatusColumn = table.getColumn('isPaidUser')
    //   if (buyerStatusColumn) {
    //     buyerStatusColumn.setFilterValue(filters.isPaidUser)
    //   }
    // }
    // if (filters?.status) {
    //   const affiliateStatusColumn = table.getColumn('status')
    //   if (affiliateStatusColumn) {
    //     affiliateStatusColumn.setFilterValue(filters.status)
    //   }
    // }
    // if (filters?.isOnline) {
    //   const onlineStatusColumn = table.getColumn('isOnline')
    //   if (onlineStatusColumn) {
    //     onlineStatusColumn.setFilterValue(filters.isOnline)
    //   }
    // }
    // if (filters.imageApproved) {
    //   const pictureStatusColumn = table.getColumn('pictureStatus')
    //   if (pictureStatusColumn) {
    //     pictureStatusColumn.setFilterValue(filters.imageApproved)
    //   }
    // }
    // if (filters.group && filters.group.length > 0) {
    //   const selected = filters.group.split(',').map(Number).filter(Boolean);
    //   const groupColumn = table.getColumn('group')
    //   if (groupColumn) {
    //     groupColumn.columnDef.filterFn = (row: any, _columnId: string, filterValue: number[]) => {
    //       return filterValue.includes(row.original.group)
    //     }
    //     groupColumn.setFilterValue(selected)
    //   }
    // }
    setHasSearched(true)
    setInitialFilters({ ...filters })

    // Transform filter data for API call
    const { cityInput, ...filtersToSend } = filters

    // Convert boolean filters to true/false
    const transformedFilters = {
      ...filtersToSend,
      isPaidUser: filters.isPaidUser === 'Buyer' ? "true" : filters.isPaidUser === 'Non Buyer' ? "false" : undefined,
      isOnline: filters.isOnline === 'Online' ? "true" : filters.isOnline === 'Offline' ? "false" : undefined,
      imageApproved: filters.imageApproved === 'Approved' ? "true" : filters.imageApproved === 'Non Approved' ? "false" : undefined,
    }

    // Remove undefined values
    const cleanedFilters = Object.fromEntries(
      Object.entries(transformedFilters).filter(([_, value]) => value !== undefined && value !== '')
    )

    onFilterChanged(cleanedFilters, 1)
  }

  const handleReset = () => {
    table.resetColumnFilters()
    const f: any = {
      search: "",
      domain: '',
      country: '',
      city: '',
      isPaidUser: '',
      isOnline: '',
      imageApproved: '',
    }
    setFilters(f)
    setInitialFilters(f)
    setHasSearched(false)
    onFilterChanged(f, 0)
  }

  const hasFilterChanges = Boolean(
    filters.search !== initialFilters.search ||
    filters.domain !== initialFilters.domain ||
    filters.country !== initialFilters.country ||
    filters.city !== initialFilters.city ||
    filters.isPaidUser !== initialFilters.isPaidUser ||
    filters.status !== initialFilters.status ||
    filters.isOnline !== initialFilters.isOnline ||
    filters.imageApproved !== initialFilters.imageApproved ||
    filters.group !== initialFilters.group
  )

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center gap-4 flex-wrap'>

        <Input
          placeholder='Search by user name or email'
          value={filters.search}
          onChange={(event: any) => handleFilterChange('search', event.target.value)}
          className='h-9 w-[250px]'
        />

        {/* Multi-select popover for domain */}
        <Popover>
          <PopoverTrigger asChild>
            <div className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.domain.length && "text-muted-foreground"
            )}>
              {filters.domain.length === 0 && <span>Select domain type</span>}
              {filters.domain.split(',').filter(Boolean).slice(0, 2).map((val: string) => (
                <div className="flex gap-1 items-center bg-muted rounded p-1" key={val}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                    {val}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer" onClick={e => {
                    e.stopPropagation();
                    const arr = filters.domain.split(',').filter(Boolean).filter((v: string) => v !== val)
                    handleFilterChange('domain', arr)
                  }} />
                </div>
              ))}
              {filters.domain.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search domain type..." />
              <CommandEmpty>No domain type found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const selectedArr = filters.domain.split(',').filter(Boolean)
                  const sorteddomains = [
                    ...domainTypeOptions.filter((type) => selectedArr.includes(type)),
                    ...domainTypeOptions.filter((type) => !selectedArr.includes(type)),
                  ];
                  return sorteddomains.map((type) => (
                    <CommandItem
                      key={type}
                      onSelect={() => {
                        const arr = filters.domain.split(',').filter(Boolean)
                        const selected = arr.includes(type)
                          ? arr.filter((v: string) => v !== type)
                          : [...arr, type]
                        handleFilterChange('domain', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{type}</span>
                      {selectedArr.includes(type) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* Multi-select popover for country */}
        <Popover>
          <PopoverTrigger asChild>
            <div className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.country.length && "text-muted-foreground"
            )}>
              {filters.country.length === 0 && <span>Select country</span>}
              {filters.country.split(',').filter(Boolean).slice(0, 2).map((idStr: string) => {
                const id = Number(idStr)
                const country = countries.find((c: any) => c.id === id);
                return (
                  <div className="flex gap-1 items-center bg-muted rounded p-1" key={id}>
                    <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                      {country?.name ?? id}
                    </Badge>
                    <X className="h-3 w-3 cursor-pointer" onClick={e => {
                      e.stopPropagation();
                      const arr = filters.country.split(',').filter(Boolean).filter((v: string) => v !== idStr)
                      handleFilterChange('country', arr)
                    }} />
                  </div>
                )
              })}
              {filters.country.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search country..." />
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const selectedArr = filters.country.split(',').filter(Boolean)
                  const sortedCountries = [
                    ...countries.filter((c: any) => selectedArr.includes(String(c.id))),
                    ...countries.filter((c: any) => !selectedArr.includes(String(c.id))),
                  ];
                  return sortedCountries.map((country: any) => (
                    <CommandItem
                      key={country.id}
                      onSelect={() => {
                        const arr = filters.country.split(',').filter(Boolean)
                        const selected = arr.includes(String(country.id))
                          ? arr.filter((v: string) => v !== String(country.id))
                          : [...arr, String(country.id)]
                        handleFilterChange('country', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{country.name}</span>
                      {selectedArr.includes(String(country.id)) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        {/* Multi-input for cities with dropdown for selected */}
        <div className="relative w-[200px]">
          <input
            type="text"
            className="w-full border border-input bg-card text-foreground rounded-md px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground"
            placeholder={filters.city.split(',').filter(Boolean).length === 0 ? CITY_PLACEHOLDER : ADD_CITIES}
            value={filters.cityInput || ""}
            onFocus={() => setCityDropdownOpen(true)}
            onBlur={() => setTimeout(() => setCityDropdownOpen(false), 150)}
            onChange={e => handleFilterChange('cityInput', e.target.value)}
            onKeyDown={e => {
              if (
                (e.key === 'Enter' || e.key === ',') &&
                filters.cityInput &&
                !filters.city.split(',').filter(Boolean).includes(filters.cityInput.trim())
              ) {
                const arr = [...filters.city.split(',').filter(Boolean), filters.cityInput.trim()]
                handleFilterChange('city', arr);
                handleFilterChange('cityInput', '');
                e.preventDefault();
              }
              if (e.key === 'Backspace' && !filters.cityInput && filters.city.split(',').filter(Boolean).length > 0) {
                // Remove last city on backspace if input is empty
                const arr = filters.city.split(',').filter(Boolean).slice(0, -1)
                handleFilterChange('city', arr);
              }
            }}
          />
          {cityDropdownOpen && filters.city.split(',').filter(Boolean).length > 0 && (
            <div className="absolute left-0 right-0 mt-1 bg-popover border border-input rounded-md shadow-lg z-10 p-2 flex flex-wrap gap-1">
              {filters.city.split(',').filter(Boolean).map((city: string) => (
                <div className="flex gap-1 items-center bg-muted rounded p-1" key={city}>
                  <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                    {city}
                  </Badge>
                  <X className="h-3 w-3 cursor-pointer"
                    onMouseDown={e => e.preventDefault()}
                    onClick={() => {
                      const arr = filters.city.split(',').filter(Boolean).filter((v: string) => v !== city)
                      handleFilterChange('city', arr)
                    }} />
                </div>
              ))}
            </div>
          )}
        </div>
        {/* Group multi-select popover */}
        {/* <Popover>
          <PopoverTrigger asChild>
            <div className={cn(
              "flex min-h-[40px] w-[200px] flex-wrap items-center gap-1 rounded-md border border-input bg-card px-3 py-2 text-sm shadow-sm cursor-pointer",
              !filters.group.length && "text-muted-foreground"
            )}>
              {filters.group.length === 0 && <span>Select group</span>}
              {filters.group.split(',').filter(Boolean).slice(0, 2).map((id: string) => {
                const group = groupOptions.find((g: any) => g.id === Number(id));
                return (
                  <div className="flex gap-1 items-center bg-muted rounded p-1" key={id}>
                    <Badge className="cursor-default border-0 p-0 bg-transparent text-foreground">
                      {group?.title ?? id}
                    </Badge>
                    <X className="h-3 w-3 cursor-pointer" onClick={e => {
                      e.stopPropagation();
                      const arr = filters.group.split(',').filter(Boolean).filter((v: string) => v !== id)
                      handleFilterChange('group', arr)
                    }} />
                  </div>
                )
              })}
              {filters.group.split(',').filter(Boolean).length > 2 && (
                <span className="ml-2 text-muted-foreground">...</span>
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Search group..." />
              <CommandEmpty>No group found.</CommandEmpty>
              <CommandGroup>
                {(() => {
                  const selectedArr = filters.group.split(',').filter(Boolean)
                  const sortedGroups = [
                    ...groupOptions.filter((group: any) => selectedArr.includes(String(group.id))),
                    ...groupOptions.filter((group: any) => !selectedArr.includes(String(group.id))),
                  ];
                  return sortedGroups.map((group: any) => (
                    <CommandItem
                      key={group.id}
                      onSelect={() => {
                        const arr = filters.group.split(',').filter(Boolean)
                        const selected = arr.includes(String(group.id))
                          ? arr.filter((v: string) => v !== String(group.id))
                          : [...arr, String(group.id)]
                        handleFilterChange('group', selected)
                      }}
                      className="cursor-pointer"
                    >
                      <span>{group.title}</span>
                      {selectedArr.includes(String(group.id)) && (
                        <span className="ml-auto text-primary">✓</span>
                      )}
                    </CommandItem>
                  ));
                })()}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover> */}
        {/* Buyer Status single-select dropdown */}
        <FilterSelect
          value={filters.isPaidUser || ''}
          placeholder="Select Buyer Status"
          options={buyerStatusOptions}
          onChange={(value) => handleFilterChange('isPaidUser', value || '')}
          className="w-[200px] bg-card"
        />

        {/* Affiliate Status single-select dropdown */}

        {/* <FilterSelect
          value={filters?.status || ''}
          placeholder="Select Affiliate Status"
          options={userStatusOptions}
          onChange={(value) => handleFilterChange('status', value || '')}
          className="w-[200px] bg-card"
        /> */}
        {/* Online Status single-select dropdown */}
        <FilterSelect
          value={filters?.isOnline || ''}
          placeholder="Select Online Status"
          options={onlineStatusOptions}
          onChange={(value) => handleFilterChange('isOnline', value || '')}
          className="w-[200px] bg-card"
        />
        {/* Picture Status single-select dropdown */}
        <FilterSelect
          value={filters.imageApproved || ''}
          placeholder="Select Picture Status"
          options={pictureStatusOptions}
          onChange={(value) => handleFilterChange('imageApproved', value || '')}
          className="w-[200px] bg-card"
        />
        {/* DatePicker components removed */}
        <Button
          onClick={handleSearch}
          className="h-8 px-3"
          disabled={!hasFilterChanges}
        >
          Search
        </Button>
        {(isFiltered || hasSearched) && (
          <Button
            variant='outline'
            onClick={handleReset}
            className='h-8 px-2 lg:px-3'
          >
            Reset
            {/* <Cross2Icon className='ml-2 h-4 w-4' /> */}
          </Button>
        )}
      </div>
    </div>
  )
}
