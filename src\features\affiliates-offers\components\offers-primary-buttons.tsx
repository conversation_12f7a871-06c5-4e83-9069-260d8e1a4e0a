import { Button } from '@/components/ui/button'
import { END_POINTS } from '@/features/members/utils/constant'
import { useNavigate } from '@tanstack/react-router'

export function OffersPrimaryButtons() {
    const navigate = useNavigate()
    return (
        <div className="flex items-center space-x-2">
            <Button onClick={() => navigate({ to: `/_authenticated${END_POINTS.AFFILIATE_OFFERS}/add` })} className="flex items-center space-x-2">
                <span>Add Affiliate Offer</span>
            </Button>
        </div>
    )
}


