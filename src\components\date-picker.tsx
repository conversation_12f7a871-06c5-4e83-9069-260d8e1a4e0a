import React, { useState } from 'react'
import { Calendar } from '@/components/ui/calendar'
import {
    Popover,
    PopoverTrigger,
    PopoverContent,
} from '@/components/ui/popover'
import { CalendarIcon } from '@radix-ui/react-icons'

interface DatePickerProps {
    value?: Date
    placeholder?: string
    onChange: (date: Date | undefined) => void
    min?: Date
    max?: Date
    className?: string
}

export const DatePicker: React.FC<DatePickerProps> = ({
    value,
    placeholder = 'Select date',
    onChange,
    min,
    max,
    className = 'w-[160px]',
}) => {
    const [open, setOpen] = useState(false)

    const handleDateSelect = (date: Date | undefined) => {
        onChange(date)
        setOpen(false) // Close the popover when a date is selected
    }

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <button
                    className={`border px-3 py-2 h-9 rounded-md text-left flex items-center justify-between bg-card text-sm ${className}`}
                >
                    {value ? value.toLocaleDateString() : placeholder}
                    <CalendarIcon className="ml-2" />
                </button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                    mode="single"
                    selected={value}
                    onSelect={handleDateSelect}
                    disabled={(date) => {
                        if (min && date < min) return true
                        if (max && date > max) return true
                        return false
                    }}
                />
            </PopoverContent>
        </Popover>
    )
}
