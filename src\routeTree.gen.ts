/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as UnauthorizedRouteImport } from './routes/unauthorized'
import { Route as AuthenticatedRouteRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated/index'
import { Route as AuthenticatedContactUsRouteImport } from './routes/_authenticated/contact-us'
import { Route as AuthenticatedAdminLoginActivityRouteImport } from './routes/_authenticated/admin-login-activity'
import { Route as errors503RouteImport } from './routes/(errors)/503'
import { Route as errors500RouteImport } from './routes/(errors)/500'
import { Route as errors404RouteImport } from './routes/(errors)/404'
import { Route as errors403RouteImport } from './routes/(errors)/403'
import { Route as errors401RouteImport } from './routes/(errors)/401'
import { Route as authSignUpRouteImport } from './routes/(auth)/sign-up'
import { Route as authSignInRouteImport } from './routes/(auth)/sign-in'
import { Route as authResetPasswordRouteImport } from './routes/(auth)/reset-password'
import { Route as authOtpRouteImport } from './routes/(auth)/otp'
import { Route as authForgotPasswordRouteImport } from './routes/(auth)/forgot-password'
import { Route as AuthenticatedSmiliesRouteRouteImport } from './routes/_authenticated/smilies/route'
import { Route as AuthenticatedSettingsRouteRouteImport } from './routes/_authenticated/settings/route'
import { Route as AuthenticatedSessionsRouteRouteImport } from './routes/_authenticated/sessions/route'
import { Route as AuthenticatedProfileValuesRouteRouteImport } from './routes/_authenticated/profile-values/route'
import { Route as AuthenticatedPackagesRouteRouteImport } from './routes/_authenticated/packages/route'
import { Route as AuthenticatedModeratorsRouteRouteImport } from './routes/_authenticated/moderators/route'
import { Route as AuthenticatedModelsRouteRouteImport } from './routes/_authenticated/models/route'
import { Route as AuthenticatedMembersRouteRouteImport } from './routes/_authenticated/members/route'
import { Route as AuthenticatedGiftsRouteRouteImport } from './routes/_authenticated/gifts/route'
import { Route as AuthenticatedGifsRouteRouteImport } from './routes/_authenticated/gifs/route'
import { Route as AuthenticatedFlirtMessagesRouteRouteImport } from './routes/_authenticated/flirt-messages/route'
import { Route as AuthenticatedCurrenciesRouteRouteImport } from './routes/_authenticated/currencies/route'
import { Route as AuthenticatedBotMessagesRouteRouteImport } from './routes/_authenticated/bot-messages/route'
import { Route as AuthenticatedAnnouncementsRouteRouteImport } from './routes/_authenticated/announcements/route'
import { Route as AuthenticatedAffiliatesOffersRouteRouteImport } from './routes/_authenticated/affiliates-offers/route'
import { Route as AuthenticatedAffiliatesRouteRouteImport } from './routes/_authenticated/affiliates/route'
import { Route as AuthenticatedUsersIndexRouteImport } from './routes/_authenticated/users/index'
import { Route as AuthenticatedSmiliesIndexRouteImport } from './routes/_authenticated/smilies/index'
import { Route as AuthenticatedSettingsIndexRouteImport } from './routes/_authenticated/settings/index'
import { Route as AuthenticatedSessionsIndexRouteImport } from './routes/_authenticated/sessions/index'
import { Route as AuthenticatedProfileValuesIndexRouteImport } from './routes/_authenticated/profile-values/index'
import { Route as AuthenticatedPackagesIndexRouteImport } from './routes/_authenticated/packages/index'
import { Route as AuthenticatedModeratorsIndexRouteImport } from './routes/_authenticated/moderators/index'
import { Route as AuthenticatedModelsIndexRouteImport } from './routes/_authenticated/models/index'
import { Route as AuthenticatedMembersIndexRouteImport } from './routes/_authenticated/members/index'
import { Route as AuthenticatedHelpCenterIndexRouteImport } from './routes/_authenticated/help-center/index'
import { Route as AuthenticatedGiftsIndexRouteImport } from './routes/_authenticated/gifts/index'
import { Route as AuthenticatedGifsIndexRouteImport } from './routes/_authenticated/gifs/index'
import { Route as AuthenticatedFlirtMessagesIndexRouteImport } from './routes/_authenticated/flirt-messages/index'
import { Route as AuthenticatedCurrenciesIndexRouteImport } from './routes/_authenticated/currencies/index'
import { Route as AuthenticatedChatsIndexRouteImport } from './routes/_authenticated/chats/index'
import { Route as AuthenticatedBotMessagesIndexRouteImport } from './routes/_authenticated/bot-messages/index'
import { Route as AuthenticatedAnnouncementsIndexRouteImport } from './routes/_authenticated/announcements/index'
import { Route as AuthenticatedAffiliatesIndexRouteImport } from './routes/_authenticated/affiliates/index'
import { Route as AuthenticatedAffiliatesOffersIndexRouteImport } from './routes/_authenticated/affiliates-offers/index'
import { Route as AuthenticatedSmiliesAddRouteImport } from './routes/_authenticated/smilies/add'
import { Route as AuthenticatedSettingsNotificationsRouteImport } from './routes/_authenticated/settings/notifications'
import { Route as AuthenticatedSettingsDisplayRouteImport } from './routes/_authenticated/settings/display'
import { Route as AuthenticatedSettingsAppearanceRouteImport } from './routes/_authenticated/settings/appearance'
import { Route as AuthenticatedSettingsAccountRouteImport } from './routes/_authenticated/settings/account'
import { Route as AuthenticatedSessionsLobyRouteImport } from './routes/_authenticated/sessions/loby'
import { Route as AuthenticatedPackagesAddRouteImport } from './routes/_authenticated/packages/add'
import { Route as AuthenticatedModeratorsReplyMessagesRouteImport } from './routes/_authenticated/moderators/reply-messages'
import { Route as AuthenticatedModeratorsLoginActivityRouteImport } from './routes/_authenticated/moderators/login-activity'
import { Route as AuthenticatedModeratorsDomainRouteImport } from './routes/_authenticated/moderators/domain'
import { Route as AuthenticatedModeratorsCreateModeratorRouteImport } from './routes/_authenticated/moderators/create-moderator'
import { Route as AuthenticatedModeratorsChatGroupRouteImport } from './routes/_authenticated/moderators/chat-group'
import { Route as AuthenticatedModelsCreateModelRouteImport } from './routes/_authenticated/models/create-model'
import { Route as AuthenticatedGiftsAddRouteImport } from './routes/_authenticated/gifts/add'
import { Route as AuthenticatedGifsAddRouteImport } from './routes/_authenticated/gifs/add'
import { Route as AuthenticatedFlirtMessagesAddRouteImport } from './routes/_authenticated/flirt-messages/add'
import { Route as AuthenticatedBotMessagesAddRouteImport } from './routes/_authenticated/bot-messages/add'
import { Route as AuthenticatedAnnouncementsWhiteLabelsRouteImport } from './routes/_authenticated/announcements/white-labels'
import { Route as AuthenticatedAnnouncementsModeratorsRouteImport } from './routes/_authenticated/announcements/moderators'
import { Route as AuthenticatedAnnouncementsAffiliatesRouteImport } from './routes/_authenticated/announcements/affiliates'
import { Route as AuthenticatedAffiliatesNewRouteImport } from './routes/_authenticated/affiliates/new'
import { Route as AuthenticatedAffiliatesCreateRouteImport } from './routes/_authenticated/affiliates/create'
import { Route as AuthenticatedAffiliatesApprovedRouteImport } from './routes/_authenticated/affiliates/approved'
import { Route as AuthenticatedAffiliatesOffersAddRouteImport } from './routes/_authenticated/affiliates-offers/add'
import { Route as AuthenticatedProfileValuesStarSignsIndexRouteImport } from './routes/_authenticated/profile-values/star-signs/index'
import { Route as AuthenticatedProfileValuesSmokingHabitsIndexRouteImport } from './routes/_authenticated/profile-values/smoking-habits/index'
import { Route as AuthenticatedProfileValuesSexualOrientationsIndexRouteImport } from './routes/_authenticated/profile-values/sexual-orientations/index'
import { Route as AuthenticatedProfileValuesReligionsIndexRouteImport } from './routes/_authenticated/profile-values/religions/index'
import { Route as AuthenticatedProfileValuesRelationshipStatusIndexRouteImport } from './routes/_authenticated/profile-values/relationship-status/index'
import { Route as AuthenticatedProfileValuesPersonalitiesIndexRouteImport } from './routes/_authenticated/profile-values/personalities/index'
import { Route as AuthenticatedProfileValuesInterestsIndexRouteImport } from './routes/_authenticated/profile-values/interests/index'
import { Route as AuthenticatedProfileValuesHairColorsIndexRouteImport } from './routes/_authenticated/profile-values/hair-colors/index'
import { Route as AuthenticatedProfileValuesEyeColorsIndexRouteImport } from './routes/_authenticated/profile-values/eye-colors/index'
import { Route as AuthenticatedProfileValuesEthnicitiesIndexRouteImport } from './routes/_authenticated/profile-values/ethnicities/index'
import { Route as AuthenticatedProfileValuesDrinkingHabitsIndexRouteImport } from './routes/_authenticated/profile-values/drinking-habits/index'
import { Route as AuthenticatedProfileValuesBodyTypesIndexRouteImport } from './routes/_authenticated/profile-values/body-types/index'
import { Route as AuthenticatedProfileValuesBodyArtsIndexRouteImport } from './routes/_authenticated/profile-values/body-arts/index'
import { Route as AuthenticatedProfileValuesBestFeaturesIndexRouteImport } from './routes/_authenticated/profile-values/best-features/index'
import { Route as AuthenticatedProfileValuesAppearancesIndexRouteImport } from './routes/_authenticated/profile-values/appearances/index'
import { Route as AuthenticatedSmiliesUpdateMsgIdRouteImport } from './routes/_authenticated/smilies/update.$msgId'
import { Route as AuthenticatedSessionsChatModConversationIdRouteImport } from './routes/_authenticated/sessions/chat-mod.$conversationId'
import { Route as AuthenticatedProfileValuesStarSignsAddRouteImport } from './routes/_authenticated/profile-values/star-signs/add'
import { Route as AuthenticatedProfileValuesSmokingHabitsAddRouteImport } from './routes/_authenticated/profile-values/smoking-habits/add'
import { Route as AuthenticatedProfileValuesSexualOrientationsAddRouteImport } from './routes/_authenticated/profile-values/sexual-orientations/add'
import { Route as AuthenticatedProfileValuesReligionsAddRouteImport } from './routes/_authenticated/profile-values/religions/add'
import { Route as AuthenticatedProfileValuesRelationshipStatusAddRouteImport } from './routes/_authenticated/profile-values/relationship-status/add'
import { Route as AuthenticatedProfileValuesPersonalitiesAddRouteImport } from './routes/_authenticated/profile-values/personalities/add'
import { Route as AuthenticatedProfileValuesInterestsAddRouteImport } from './routes/_authenticated/profile-values/interests/add'
import { Route as AuthenticatedProfileValuesHairColorsAddRouteImport } from './routes/_authenticated/profile-values/hair-colors/add'
import { Route as AuthenticatedProfileValuesEyeColorsAddRouteImport } from './routes/_authenticated/profile-values/eye-colors/add'
import { Route as AuthenticatedProfileValuesEthnicitiesAddRouteImport } from './routes/_authenticated/profile-values/ethnicities/add'
import { Route as AuthenticatedProfileValuesDrinkingHabitsAddRouteImport } from './routes/_authenticated/profile-values/drinking-habits/add'
import { Route as AuthenticatedProfileValuesBodyTypesAddRouteImport } from './routes/_authenticated/profile-values/body-types/add'
import { Route as AuthenticatedProfileValuesBodyArtsAddRouteImport } from './routes/_authenticated/profile-values/body-arts/add'
import { Route as AuthenticatedProfileValuesBestFeaturesAddRouteImport } from './routes/_authenticated/profile-values/best-features/add'
import { Route as AuthenticatedProfileValuesAppearancesAddRouteImport } from './routes/_authenticated/profile-values/appearances/add'
import { Route as AuthenticatedPackagesUpdateMsgIdRouteImport } from './routes/_authenticated/packages/update.$msgId'
import { Route as AuthenticatedModeratorsUpdateModeratorModeratorIdRouteImport } from './routes/_authenticated/moderators/update-moderator.$moderatorId'
import { Route as AuthenticatedModeratorsProfileModeratorIdRouteImport } from './routes/_authenticated/moderators/profile.$moderatorId'
import { Route as AuthenticatedModelsUpdateModelModelIdRouteImport } from './routes/_authenticated/models/update-model.$modelId'
import { Route as AuthenticatedModelsProfileModelIdRouteImport } from './routes/_authenticated/models/profile.$modelId'
import { Route as AuthenticatedModelsProfileImagesModelIdRouteImport } from './routes/_authenticated/models/profile-images.$modelId'
import { Route as AuthenticatedMembersProfileMemberIdRouteImport } from './routes/_authenticated/members/profile.$memberId'
import { Route as AuthenticatedGiftsUpdateMsgIdRouteImport } from './routes/_authenticated/gifts/update.$msgId'
import { Route as AuthenticatedGifsUpdateMsgIdRouteImport } from './routes/_authenticated/gifs/update.$msgId'
import { Route as AuthenticatedFlirtMessagesUpdateMsgIdRouteImport } from './routes/_authenticated/flirt-messages/update.$msgId'
import { Route as AuthenticatedBotMessagesUpdateMsgIdRouteImport } from './routes/_authenticated/bot-messages/update.$msgId'
import { Route as AuthenticatedProfileValuesStarSignsUpdateStarsignsIdRouteImport } from './routes/_authenticated/profile-values/star-signs/update.$starsignsId'
import { Route as AuthenticatedProfileValuesSmokingHabitsUpdateSmokinghabitsIdRouteImport } from './routes/_authenticated/profile-values/smoking-habits/update.$smokinghabitsId'
import { Route as AuthenticatedProfileValuesSexualOrientationsUpdateSexualorientationsIdRouteImport } from './routes/_authenticated/profile-values/sexual-orientations/update.$sexualorientationsId'
import { Route as AuthenticatedProfileValuesReligionsUpdateReligionsIdRouteImport } from './routes/_authenticated/profile-values/religions/update.$religionsId'
import { Route as AuthenticatedProfileValuesRelationshipStatusUpdateRelationshipStatusIdRouteImport } from './routes/_authenticated/profile-values/relationship-status/update.$relationshipStatusId'
import { Route as AuthenticatedProfileValuesPersonalitiesUpdatePersonalityIdRouteImport } from './routes/_authenticated/profile-values/personalities/update/$personalityId'
import { Route as AuthenticatedProfileValuesInterestsUpdateInterestsIdRouteImport } from './routes/_authenticated/profile-values/interests/update.$interestsId'
import { Route as AuthenticatedProfileValuesHairColorsUpdateHaircolorsIdRouteImport } from './routes/_authenticated/profile-values/hair-colors/update.$haircolorsId'
import { Route as AuthenticatedProfileValuesEyeColorsUpdateEyecolorsIdRouteImport } from './routes/_authenticated/profile-values/eye-colors/update.$eyecolorsId'
import { Route as AuthenticatedProfileValuesEthnicitiesUpdateEthnicitiesIdRouteImport } from './routes/_authenticated/profile-values/ethnicities/update.$ethnicitiesId'
import { Route as AuthenticatedProfileValuesDrinkingHabitsUpdateDrinkinghabitsIdRouteImport } from './routes/_authenticated/profile-values/drinking-habits/update.$drinkinghabitsId'
import { Route as AuthenticatedProfileValuesBodyTypesUpdateBodytypesIdRouteImport } from './routes/_authenticated/profile-values/body-types/update.$bodytypesId'
import { Route as AuthenticatedProfileValuesBodyArtsUpdateBodyartsIdRouteImport } from './routes/_authenticated/profile-values/body-arts/update.$bodyartsId'
import { Route as AuthenticatedProfileValuesBestFeaturesUpdateBestfeaturesIdRouteImport } from './routes/_authenticated/profile-values/best-features/update.$bestfeaturesId'
import { Route as AuthenticatedProfileValuesAppearancesUpdateAppearancesIdRouteImport } from './routes/_authenticated/profile-values/appearances/update.$appearancesId'
import { Route as AuthenticatedMembersProfileMemberIdPicturesRouteImport } from './routes/_authenticated/members/profile.$memberId.pictures'
import { Route as AuthenticatedMembersProfileMemberIdMessagesRouteImport } from './routes/_authenticated/members/profile.$memberId.messages'
import { Route as AuthenticatedMembersProfileMemberIdCreditsRouteImport } from './routes/_authenticated/members/profile.$memberId.credits'
import { Route as AuthenticatedMembersProfileMemberIdMessagesConversationIdRouteImport } from './routes/_authenticated/members/profile.$memberId.messages.$conversationId'

const UnauthorizedRoute = UnauthorizedRouteImport.update({
  id: '/unauthorized',
  path: '/unauthorized',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRouteRoute = AuthenticatedRouteRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedContactUsRoute = AuthenticatedContactUsRouteImport.update({
  id: '/contact-us',
  path: '/contact-us',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedAdminLoginActivityRoute =
  AuthenticatedAdminLoginActivityRouteImport.update({
    id: '/admin-login-activity',
    path: '/admin-login-activity',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const errors503Route = errors503RouteImport.update({
  id: '/(errors)/503',
  path: '/503',
  getParentRoute: () => rootRouteImport,
} as any)
const errors500Route = errors500RouteImport.update({
  id: '/(errors)/500',
  path: '/500',
  getParentRoute: () => rootRouteImport,
} as any)
const errors404Route = errors404RouteImport.update({
  id: '/(errors)/404',
  path: '/404',
  getParentRoute: () => rootRouteImport,
} as any)
const errors403Route = errors403RouteImport.update({
  id: '/(errors)/403',
  path: '/403',
  getParentRoute: () => rootRouteImport,
} as any)
const errors401Route = errors401RouteImport.update({
  id: '/(errors)/401',
  path: '/401',
  getParentRoute: () => rootRouteImport,
} as any)
const authSignUpRoute = authSignUpRouteImport.update({
  id: '/(auth)/sign-up',
  path: '/sign-up',
  getParentRoute: () => rootRouteImport,
} as any)
const authSignInRoute = authSignInRouteImport.update({
  id: '/(auth)/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRouteImport,
} as any)
const authResetPasswordRoute = authResetPasswordRouteImport.update({
  id: '/(auth)/reset-password',
  path: '/reset-password',
  getParentRoute: () => rootRouteImport,
} as any)
const authOtpRoute = authOtpRouteImport.update({
  id: '/(auth)/otp',
  path: '/otp',
  getParentRoute: () => rootRouteImport,
} as any)
const authForgotPasswordRoute = authForgotPasswordRouteImport.update({
  id: '/(auth)/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedSmiliesRouteRoute =
  AuthenticatedSmiliesRouteRouteImport.update({
    id: '/smilies',
    path: '/smilies',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedSettingsRouteRoute =
  AuthenticatedSettingsRouteRouteImport.update({
    id: '/settings',
    path: '/settings',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedSessionsRouteRoute =
  AuthenticatedSessionsRouteRouteImport.update({
    id: '/sessions',
    path: '/sessions',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedProfileValuesRouteRoute =
  AuthenticatedProfileValuesRouteRouteImport.update({
    id: '/profile-values',
    path: '/profile-values',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedPackagesRouteRoute =
  AuthenticatedPackagesRouteRouteImport.update({
    id: '/packages',
    path: '/packages',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedModeratorsRouteRoute =
  AuthenticatedModeratorsRouteRouteImport.update({
    id: '/moderators',
    path: '/moderators',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedModelsRouteRoute =
  AuthenticatedModelsRouteRouteImport.update({
    id: '/models',
    path: '/models',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedMembersRouteRoute =
  AuthenticatedMembersRouteRouteImport.update({
    id: '/members',
    path: '/members',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedGiftsRouteRoute = AuthenticatedGiftsRouteRouteImport.update({
  id: '/gifts',
  path: '/gifts',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedGifsRouteRoute = AuthenticatedGifsRouteRouteImport.update({
  id: '/gifs',
  path: '/gifs',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedFlirtMessagesRouteRoute =
  AuthenticatedFlirtMessagesRouteRouteImport.update({
    id: '/flirt-messages',
    path: '/flirt-messages',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedCurrenciesRouteRoute =
  AuthenticatedCurrenciesRouteRouteImport.update({
    id: '/currencies',
    path: '/currencies',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedBotMessagesRouteRoute =
  AuthenticatedBotMessagesRouteRouteImport.update({
    id: '/bot-messages',
    path: '/bot-messages',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedAnnouncementsRouteRoute =
  AuthenticatedAnnouncementsRouteRouteImport.update({
    id: '/announcements',
    path: '/announcements',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedAffiliatesOffersRouteRoute =
  AuthenticatedAffiliatesOffersRouteRouteImport.update({
    id: '/affiliates-offers',
    path: '/affiliates-offers',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedAffiliatesRouteRoute =
  AuthenticatedAffiliatesRouteRouteImport.update({
    id: '/affiliates',
    path: '/affiliates',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedUsersIndexRoute = AuthenticatedUsersIndexRouteImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedSmiliesIndexRoute =
  AuthenticatedSmiliesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSmiliesRouteRoute,
  } as any)
const AuthenticatedSettingsIndexRoute =
  AuthenticatedSettingsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSessionsIndexRoute =
  AuthenticatedSessionsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedSessionsRouteRoute,
  } as any)
const AuthenticatedProfileValuesIndexRoute =
  AuthenticatedProfileValuesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedPackagesIndexRoute =
  AuthenticatedPackagesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedPackagesRouteRoute,
  } as any)
const AuthenticatedModeratorsIndexRoute =
  AuthenticatedModeratorsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModelsIndexRoute =
  AuthenticatedModelsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedModelsRouteRoute,
  } as any)
const AuthenticatedMembersIndexRoute =
  AuthenticatedMembersIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedMembersRouteRoute,
  } as any)
const AuthenticatedHelpCenterIndexRoute =
  AuthenticatedHelpCenterIndexRouteImport.update({
    id: '/help-center/',
    path: '/help-center/',
    getParentRoute: () => AuthenticatedRouteRoute,
  } as any)
const AuthenticatedGiftsIndexRoute = AuthenticatedGiftsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedGiftsRouteRoute,
} as any)
const AuthenticatedGifsIndexRoute = AuthenticatedGifsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedGifsRouteRoute,
} as any)
const AuthenticatedFlirtMessagesIndexRoute =
  AuthenticatedFlirtMessagesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedFlirtMessagesRouteRoute,
  } as any)
const AuthenticatedCurrenciesIndexRoute =
  AuthenticatedCurrenciesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedCurrenciesRouteRoute,
  } as any)
const AuthenticatedChatsIndexRoute = AuthenticatedChatsIndexRouteImport.update({
  id: '/chats/',
  path: '/chats/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)
const AuthenticatedBotMessagesIndexRoute =
  AuthenticatedBotMessagesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedBotMessagesRouteRoute,
  } as any)
const AuthenticatedAnnouncementsIndexRoute =
  AuthenticatedAnnouncementsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedAnnouncementsRouteRoute,
  } as any)
const AuthenticatedAffiliatesIndexRoute =
  AuthenticatedAffiliatesIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedAffiliatesRouteRoute,
  } as any)
const AuthenticatedAffiliatesOffersIndexRoute =
  AuthenticatedAffiliatesOffersIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedAffiliatesOffersRouteRoute,
  } as any)
const AuthenticatedSmiliesAddRoute = AuthenticatedSmiliesAddRouteImport.update({
  id: '/add',
  path: '/add',
  getParentRoute: () => AuthenticatedSmiliesRouteRoute,
} as any)
const AuthenticatedSettingsNotificationsRoute =
  AuthenticatedSettingsNotificationsRouteImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSettingsDisplayRoute =
  AuthenticatedSettingsDisplayRouteImport.update({
    id: '/display',
    path: '/display',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSettingsAppearanceRoute =
  AuthenticatedSettingsAppearanceRouteImport.update({
    id: '/appearance',
    path: '/appearance',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSettingsAccountRoute =
  AuthenticatedSettingsAccountRouteImport.update({
    id: '/account',
    path: '/account',
    getParentRoute: () => AuthenticatedSettingsRouteRoute,
  } as any)
const AuthenticatedSessionsLobyRoute =
  AuthenticatedSessionsLobyRouteImport.update({
    id: '/loby',
    path: '/loby',
    getParentRoute: () => AuthenticatedSessionsRouteRoute,
  } as any)
const AuthenticatedPackagesAddRoute =
  AuthenticatedPackagesAddRouteImport.update({
    id: '/add',
    path: '/add',
    getParentRoute: () => AuthenticatedPackagesRouteRoute,
  } as any)
const AuthenticatedModeratorsReplyMessagesRoute =
  AuthenticatedModeratorsReplyMessagesRouteImport.update({
    id: '/reply-messages',
    path: '/reply-messages',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModeratorsLoginActivityRoute =
  AuthenticatedModeratorsLoginActivityRouteImport.update({
    id: '/login-activity',
    path: '/login-activity',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModeratorsDomainRoute =
  AuthenticatedModeratorsDomainRouteImport.update({
    id: '/domain',
    path: '/domain',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModeratorsCreateModeratorRoute =
  AuthenticatedModeratorsCreateModeratorRouteImport.update({
    id: '/create-moderator',
    path: '/create-moderator',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModeratorsChatGroupRoute =
  AuthenticatedModeratorsChatGroupRouteImport.update({
    id: '/chat-group',
    path: '/chat-group',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModelsCreateModelRoute =
  AuthenticatedModelsCreateModelRouteImport.update({
    id: '/create-model',
    path: '/create-model',
    getParentRoute: () => AuthenticatedModelsRouteRoute,
  } as any)
const AuthenticatedGiftsAddRoute = AuthenticatedGiftsAddRouteImport.update({
  id: '/add',
  path: '/add',
  getParentRoute: () => AuthenticatedGiftsRouteRoute,
} as any)
const AuthenticatedGifsAddRoute = AuthenticatedGifsAddRouteImport.update({
  id: '/add',
  path: '/add',
  getParentRoute: () => AuthenticatedGifsRouteRoute,
} as any)
const AuthenticatedFlirtMessagesAddRoute =
  AuthenticatedFlirtMessagesAddRouteImport.update({
    id: '/add',
    path: '/add',
    getParentRoute: () => AuthenticatedFlirtMessagesRouteRoute,
  } as any)
const AuthenticatedBotMessagesAddRoute =
  AuthenticatedBotMessagesAddRouteImport.update({
    id: '/add',
    path: '/add',
    getParentRoute: () => AuthenticatedBotMessagesRouteRoute,
  } as any)
const AuthenticatedAnnouncementsWhiteLabelsRoute =
  AuthenticatedAnnouncementsWhiteLabelsRouteImport.update({
    id: '/white-labels',
    path: '/white-labels',
    getParentRoute: () => AuthenticatedAnnouncementsRouteRoute,
  } as any)
const AuthenticatedAnnouncementsModeratorsRoute =
  AuthenticatedAnnouncementsModeratorsRouteImport.update({
    id: '/moderators',
    path: '/moderators',
    getParentRoute: () => AuthenticatedAnnouncementsRouteRoute,
  } as any)
const AuthenticatedAnnouncementsAffiliatesRoute =
  AuthenticatedAnnouncementsAffiliatesRouteImport.update({
    id: '/affiliates',
    path: '/affiliates',
    getParentRoute: () => AuthenticatedAnnouncementsRouteRoute,
  } as any)
const AuthenticatedAffiliatesNewRoute =
  AuthenticatedAffiliatesNewRouteImport.update({
    id: '/new',
    path: '/new',
    getParentRoute: () => AuthenticatedAffiliatesRouteRoute,
  } as any)
const AuthenticatedAffiliatesCreateRoute =
  AuthenticatedAffiliatesCreateRouteImport.update({
    id: '/create',
    path: '/create',
    getParentRoute: () => AuthenticatedAffiliatesRouteRoute,
  } as any)
const AuthenticatedAffiliatesApprovedRoute =
  AuthenticatedAffiliatesApprovedRouteImport.update({
    id: '/approved',
    path: '/approved',
    getParentRoute: () => AuthenticatedAffiliatesRouteRoute,
  } as any)
const AuthenticatedAffiliatesOffersAddRoute =
  AuthenticatedAffiliatesOffersAddRouteImport.update({
    id: '/add',
    path: '/add',
    getParentRoute: () => AuthenticatedAffiliatesOffersRouteRoute,
  } as any)
const AuthenticatedProfileValuesStarSignsIndexRoute =
  AuthenticatedProfileValuesStarSignsIndexRouteImport.update({
    id: '/star-signs/',
    path: '/star-signs/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesSmokingHabitsIndexRoute =
  AuthenticatedProfileValuesSmokingHabitsIndexRouteImport.update({
    id: '/smoking-habits/',
    path: '/smoking-habits/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesSexualOrientationsIndexRoute =
  AuthenticatedProfileValuesSexualOrientationsIndexRouteImport.update({
    id: '/sexual-orientations/',
    path: '/sexual-orientations/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesReligionsIndexRoute =
  AuthenticatedProfileValuesReligionsIndexRouteImport.update({
    id: '/religions/',
    path: '/religions/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesRelationshipStatusIndexRoute =
  AuthenticatedProfileValuesRelationshipStatusIndexRouteImport.update({
    id: '/relationship-status/',
    path: '/relationship-status/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesPersonalitiesIndexRoute =
  AuthenticatedProfileValuesPersonalitiesIndexRouteImport.update({
    id: '/personalities/',
    path: '/personalities/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesInterestsIndexRoute =
  AuthenticatedProfileValuesInterestsIndexRouteImport.update({
    id: '/interests/',
    path: '/interests/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesHairColorsIndexRoute =
  AuthenticatedProfileValuesHairColorsIndexRouteImport.update({
    id: '/hair-colors/',
    path: '/hair-colors/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesEyeColorsIndexRoute =
  AuthenticatedProfileValuesEyeColorsIndexRouteImport.update({
    id: '/eye-colors/',
    path: '/eye-colors/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesEthnicitiesIndexRoute =
  AuthenticatedProfileValuesEthnicitiesIndexRouteImport.update({
    id: '/ethnicities/',
    path: '/ethnicities/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesDrinkingHabitsIndexRoute =
  AuthenticatedProfileValuesDrinkingHabitsIndexRouteImport.update({
    id: '/drinking-habits/',
    path: '/drinking-habits/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesBodyTypesIndexRoute =
  AuthenticatedProfileValuesBodyTypesIndexRouteImport.update({
    id: '/body-types/',
    path: '/body-types/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesBodyArtsIndexRoute =
  AuthenticatedProfileValuesBodyArtsIndexRouteImport.update({
    id: '/body-arts/',
    path: '/body-arts/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesBestFeaturesIndexRoute =
  AuthenticatedProfileValuesBestFeaturesIndexRouteImport.update({
    id: '/best-features/',
    path: '/best-features/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesAppearancesIndexRoute =
  AuthenticatedProfileValuesAppearancesIndexRouteImport.update({
    id: '/appearances/',
    path: '/appearances/',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedSmiliesUpdateMsgIdRoute =
  AuthenticatedSmiliesUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedSmiliesRouteRoute,
  } as any)
const AuthenticatedSessionsChatModConversationIdRoute =
  AuthenticatedSessionsChatModConversationIdRouteImport.update({
    id: '/chat-mod/$conversationId',
    path: '/chat-mod/$conversationId',
    getParentRoute: () => AuthenticatedSessionsRouteRoute,
  } as any)
const AuthenticatedProfileValuesStarSignsAddRoute =
  AuthenticatedProfileValuesStarSignsAddRouteImport.update({
    id: '/star-signs/add',
    path: '/star-signs/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesSmokingHabitsAddRoute =
  AuthenticatedProfileValuesSmokingHabitsAddRouteImport.update({
    id: '/smoking-habits/add',
    path: '/smoking-habits/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesSexualOrientationsAddRoute =
  AuthenticatedProfileValuesSexualOrientationsAddRouteImport.update({
    id: '/sexual-orientations/add',
    path: '/sexual-orientations/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesReligionsAddRoute =
  AuthenticatedProfileValuesReligionsAddRouteImport.update({
    id: '/religions/add',
    path: '/religions/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesRelationshipStatusAddRoute =
  AuthenticatedProfileValuesRelationshipStatusAddRouteImport.update({
    id: '/relationship-status/add',
    path: '/relationship-status/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesPersonalitiesAddRoute =
  AuthenticatedProfileValuesPersonalitiesAddRouteImport.update({
    id: '/personalities/add',
    path: '/personalities/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesInterestsAddRoute =
  AuthenticatedProfileValuesInterestsAddRouteImport.update({
    id: '/interests/add',
    path: '/interests/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesHairColorsAddRoute =
  AuthenticatedProfileValuesHairColorsAddRouteImport.update({
    id: '/hair-colors/add',
    path: '/hair-colors/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesEyeColorsAddRoute =
  AuthenticatedProfileValuesEyeColorsAddRouteImport.update({
    id: '/eye-colors/add',
    path: '/eye-colors/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesEthnicitiesAddRoute =
  AuthenticatedProfileValuesEthnicitiesAddRouteImport.update({
    id: '/ethnicities/add',
    path: '/ethnicities/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesDrinkingHabitsAddRoute =
  AuthenticatedProfileValuesDrinkingHabitsAddRouteImport.update({
    id: '/drinking-habits/add',
    path: '/drinking-habits/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesBodyTypesAddRoute =
  AuthenticatedProfileValuesBodyTypesAddRouteImport.update({
    id: '/body-types/add',
    path: '/body-types/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesBodyArtsAddRoute =
  AuthenticatedProfileValuesBodyArtsAddRouteImport.update({
    id: '/body-arts/add',
    path: '/body-arts/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesBestFeaturesAddRoute =
  AuthenticatedProfileValuesBestFeaturesAddRouteImport.update({
    id: '/best-features/add',
    path: '/best-features/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesAppearancesAddRoute =
  AuthenticatedProfileValuesAppearancesAddRouteImport.update({
    id: '/appearances/add',
    path: '/appearances/add',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedPackagesUpdateMsgIdRoute =
  AuthenticatedPackagesUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedPackagesRouteRoute,
  } as any)
const AuthenticatedModeratorsUpdateModeratorModeratorIdRoute =
  AuthenticatedModeratorsUpdateModeratorModeratorIdRouteImport.update({
    id: '/update-moderator/$moderatorId',
    path: '/update-moderator/$moderatorId',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModeratorsProfileModeratorIdRoute =
  AuthenticatedModeratorsProfileModeratorIdRouteImport.update({
    id: '/profile/$moderatorId',
    path: '/profile/$moderatorId',
    getParentRoute: () => AuthenticatedModeratorsRouteRoute,
  } as any)
const AuthenticatedModelsUpdateModelModelIdRoute =
  AuthenticatedModelsUpdateModelModelIdRouteImport.update({
    id: '/update-model/$modelId',
    path: '/update-model/$modelId',
    getParentRoute: () => AuthenticatedModelsRouteRoute,
  } as any)
const AuthenticatedModelsProfileModelIdRoute =
  AuthenticatedModelsProfileModelIdRouteImport.update({
    id: '/profile/$modelId',
    path: '/profile/$modelId',
    getParentRoute: () => AuthenticatedModelsRouteRoute,
  } as any)
const AuthenticatedModelsProfileImagesModelIdRoute =
  AuthenticatedModelsProfileImagesModelIdRouteImport.update({
    id: '/profile-images/$modelId',
    path: '/profile-images/$modelId',
    getParentRoute: () => AuthenticatedModelsRouteRoute,
  } as any)
const AuthenticatedMembersProfileMemberIdRoute =
  AuthenticatedMembersProfileMemberIdRouteImport.update({
    id: '/profile/$memberId',
    path: '/profile/$memberId',
    getParentRoute: () => AuthenticatedMembersRouteRoute,
  } as any)
const AuthenticatedGiftsUpdateMsgIdRoute =
  AuthenticatedGiftsUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedGiftsRouteRoute,
  } as any)
const AuthenticatedGifsUpdateMsgIdRoute =
  AuthenticatedGifsUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedGifsRouteRoute,
  } as any)
const AuthenticatedFlirtMessagesUpdateMsgIdRoute =
  AuthenticatedFlirtMessagesUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedFlirtMessagesRouteRoute,
  } as any)
const AuthenticatedBotMessagesUpdateMsgIdRoute =
  AuthenticatedBotMessagesUpdateMsgIdRouteImport.update({
    id: '/update/$msgId',
    path: '/update/$msgId',
    getParentRoute: () => AuthenticatedBotMessagesRouteRoute,
  } as any)
const AuthenticatedProfileValuesStarSignsUpdateStarsignsIdRoute =
  AuthenticatedProfileValuesStarSignsUpdateStarsignsIdRouteImport.update({
    id: '/star-signs/update/$starsignsId',
    path: '/star-signs/update/$starsignsId',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesSmokingHabitsUpdateSmokinghabitsIdRoute =
  AuthenticatedProfileValuesSmokingHabitsUpdateSmokinghabitsIdRouteImport.update(
    {
      id: '/smoking-habits/update/$smokinghabitsId',
      path: '/smoking-habits/update/$smokinghabitsId',
      getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
    } as any,
  )
const AuthenticatedProfileValuesSexualOrientationsUpdateSexualorientationsIdRoute =
  AuthenticatedProfileValuesSexualOrientationsUpdateSexualorientationsIdRouteImport.update(
    {
      id: '/sexual-orientations/update/$sexualorientationsId',
      path: '/sexual-orientations/update/$sexualorientationsId',
      getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
    } as any,
  )
const AuthenticatedProfileValuesReligionsUpdateReligionsIdRoute =
  AuthenticatedProfileValuesReligionsUpdateReligionsIdRouteImport.update({
    id: '/religions/update/$religionsId',
    path: '/religions/update/$religionsId',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesRelationshipStatusUpdateRelationshipStatusIdRoute =
  AuthenticatedProfileValuesRelationshipStatusUpdateRelationshipStatusIdRouteImport.update(
    {
      id: '/relationship-status/update/$relationshipStatusId',
      path: '/relationship-status/update/$relationshipStatusId',
      getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
    } as any,
  )
const AuthenticatedProfileValuesPersonalitiesUpdatePersonalityIdRoute =
  AuthenticatedProfileValuesPersonalitiesUpdatePersonalityIdRouteImport.update({
    id: '/personalities/update/$personalityId',
    path: '/personalities/update/$personalityId',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesInterestsUpdateInterestsIdRoute =
  AuthenticatedProfileValuesInterestsUpdateInterestsIdRouteImport.update({
    id: '/interests/update/$interestsId',
    path: '/interests/update/$interestsId',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesHairColorsUpdateHaircolorsIdRoute =
  AuthenticatedProfileValuesHairColorsUpdateHaircolorsIdRouteImport.update({
    id: '/hair-colors/update/$haircolorsId',
    path: '/hair-colors/update/$haircolorsId',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesEyeColorsUpdateEyecolorsIdRoute =
  AuthenticatedProfileValuesEyeColorsUpdateEyecolorsIdRouteImport.update({
    id: '/eye-colors/update/$eyecolorsId',
    path: '/eye-colors/update/$eyecolorsId',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesEthnicitiesUpdateEthnicitiesIdRoute =
  AuthenticatedProfileValuesEthnicitiesUpdateEthnicitiesIdRouteImport.update({
    id: '/ethnicities/update/$ethnicitiesId',
    path: '/ethnicities/update/$ethnicitiesId',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesDrinkingHabitsUpdateDrinkinghabitsIdRoute =
  AuthenticatedProfileValuesDrinkingHabitsUpdateDrinkinghabitsIdRouteImport.update(
    {
      id: '/drinking-habits/update/$drinkinghabitsId',
      path: '/drinking-habits/update/$drinkinghabitsId',
      getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
    } as any,
  )
const AuthenticatedProfileValuesBodyTypesUpdateBodytypesIdRoute =
  AuthenticatedProfileValuesBodyTypesUpdateBodytypesIdRouteImport.update({
    id: '/body-types/update/$bodytypesId',
    path: '/body-types/update/$bodytypesId',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesBodyArtsUpdateBodyartsIdRoute =
  AuthenticatedProfileValuesBodyArtsUpdateBodyartsIdRouteImport.update({
    id: '/body-arts/update/$bodyartsId',
    path: '/body-arts/update/$bodyartsId',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesBestFeaturesUpdateBestfeaturesIdRoute =
  AuthenticatedProfileValuesBestFeaturesUpdateBestfeaturesIdRouteImport.update({
    id: '/best-features/update/$bestfeaturesId',
    path: '/best-features/update/$bestfeaturesId',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedProfileValuesAppearancesUpdateAppearancesIdRoute =
  AuthenticatedProfileValuesAppearancesUpdateAppearancesIdRouteImport.update({
    id: '/appearances/update/$appearancesId',
    path: '/appearances/update/$appearancesId',
    getParentRoute: () => AuthenticatedProfileValuesRouteRoute,
  } as any)
const AuthenticatedMembersProfileMemberIdPicturesRoute =
  AuthenticatedMembersProfileMemberIdPicturesRouteImport.update({
    id: '/pictures',
    path: '/pictures',
    getParentRoute: () => AuthenticatedMembersProfileMemberIdRoute,
  } as any)
const AuthenticatedMembersProfileMemberIdMessagesRoute =
  AuthenticatedMembersProfileMemberIdMessagesRouteImport.update({
    id: '/messages',
    path: '/messages',
    getParentRoute: () => AuthenticatedMembersProfileMemberIdRoute,
  } as any)
const AuthenticatedMembersProfileMemberIdCreditsRoute =
  AuthenticatedMembersProfileMemberIdCreditsRouteImport.update({
    id: '/credits',
    path: '/credits',
    getParentRoute: () => AuthenticatedMembersProfileMemberIdRoute,
  } as any)
const AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute =
  AuthenticatedMembersProfileMemberIdMessagesConversationIdRouteImport.update({
    id: '/$conversationId',
    path: '/$conversationId',
    getParentRoute: () => AuthenticatedMembersProfileMemberIdMessagesRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/unauthorized': typeof UnauthorizedRoute
  '/affiliates': typeof AuthenticatedAffiliatesRouteRouteWithChildren
  '/affiliates-offers': typeof AuthenticatedAffiliatesOffersRouteRouteWithChildren
  '/announcements': typeof AuthenticatedAnnouncementsRouteRouteWithChildren
  '/bot-messages': typeof AuthenticatedBotMessagesRouteRouteWithChildren
  '/currencies': typeof AuthenticatedCurrenciesRouteRouteWithChildren
  '/flirt-messages': typeof AuthenticatedFlirtMessagesRouteRouteWithChildren
  '/gifs': typeof AuthenticatedGifsRouteRouteWithChildren
  '/gifts': typeof AuthenticatedGiftsRouteRouteWithChildren
  '/members': typeof AuthenticatedMembersRouteRouteWithChildren
  '/models': typeof AuthenticatedModelsRouteRouteWithChildren
  '/moderators': typeof AuthenticatedModeratorsRouteRouteWithChildren
  '/packages': typeof AuthenticatedPackagesRouteRouteWithChildren
  '/profile-values': typeof AuthenticatedProfileValuesRouteRouteWithChildren
  '/sessions': typeof AuthenticatedSessionsRouteRouteWithChildren
  '/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/smilies': typeof AuthenticatedSmiliesRouteRouteWithChildren
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/reset-password': typeof authResetPasswordRoute
  '/sign-in': typeof authSignInRoute
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/admin-login-activity': typeof AuthenticatedAdminLoginActivityRoute
  '/contact-us': typeof AuthenticatedContactUsRoute
  '/': typeof AuthenticatedIndexRoute
  '/affiliates-offers/add': typeof AuthenticatedAffiliatesOffersAddRoute
  '/affiliates/approved': typeof AuthenticatedAffiliatesApprovedRoute
  '/affiliates/create': typeof AuthenticatedAffiliatesCreateRoute
  '/affiliates/new': typeof AuthenticatedAffiliatesNewRoute
  '/announcements/affiliates': typeof AuthenticatedAnnouncementsAffiliatesRoute
  '/announcements/moderators': typeof AuthenticatedAnnouncementsModeratorsRoute
  '/announcements/white-labels': typeof AuthenticatedAnnouncementsWhiteLabelsRoute
  '/bot-messages/add': typeof AuthenticatedBotMessagesAddRoute
  '/flirt-messages/add': typeof AuthenticatedFlirtMessagesAddRoute
  '/gifs/add': typeof AuthenticatedGifsAddRoute
  '/gifts/add': typeof AuthenticatedGiftsAddRoute
  '/models/create-model': typeof AuthenticatedModelsCreateModelRoute
  '/moderators/chat-group': typeof AuthenticatedModeratorsChatGroupRoute
  '/moderators/create-moderator': typeof AuthenticatedModeratorsCreateModeratorRoute
  '/moderators/domain': typeof AuthenticatedModeratorsDomainRoute
  '/moderators/login-activity': typeof AuthenticatedModeratorsLoginActivityRoute
  '/moderators/reply-messages': typeof AuthenticatedModeratorsReplyMessagesRoute
  '/packages/add': typeof AuthenticatedPackagesAddRoute
  '/sessions/loby': typeof AuthenticatedSessionsLobyRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/smilies/add': typeof AuthenticatedSmiliesAddRoute
  '/affiliates-offers/': typeof AuthenticatedAffiliatesOffersIndexRoute
  '/affiliates/': typeof AuthenticatedAffiliatesIndexRoute
  '/announcements/': typeof AuthenticatedAnnouncementsIndexRoute
  '/bot-messages/': typeof AuthenticatedBotMessagesIndexRoute
  '/chats': typeof AuthenticatedChatsIndexRoute
  '/currencies/': typeof AuthenticatedCurrenciesIndexRoute
  '/flirt-messages/': typeof AuthenticatedFlirtMessagesIndexRoute
  '/gifs/': typeof AuthenticatedGifsIndexRoute
  '/gifts/': typeof AuthenticatedGiftsIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/members/': typeof AuthenticatedMembersIndexRoute
  '/models/': typeof AuthenticatedModelsIndexRoute
  '/moderators/': typeof AuthenticatedModeratorsIndexRoute
  '/packages/': typeof AuthenticatedPackagesIndexRoute
  '/profile-values/': typeof AuthenticatedProfileValuesIndexRoute
  '/sessions/': typeof AuthenticatedSessionsIndexRoute
  '/settings/': typeof AuthenticatedSettingsIndexRoute
  '/smilies/': typeof AuthenticatedSmiliesIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
  '/bot-messages/update/$msgId': typeof AuthenticatedBotMessagesUpdateMsgIdRoute
  '/flirt-messages/update/$msgId': typeof AuthenticatedFlirtMessagesUpdateMsgIdRoute
  '/gifs/update/$msgId': typeof AuthenticatedGifsUpdateMsgIdRoute
  '/gifts/update/$msgId': typeof AuthenticatedGiftsUpdateMsgIdRoute
  '/members/profile/$memberId': typeof AuthenticatedMembersProfileMemberIdRouteWithChildren
  '/models/profile-images/$modelId': typeof AuthenticatedModelsProfileImagesModelIdRoute
  '/models/profile/$modelId': typeof AuthenticatedModelsProfileModelIdRoute
  '/models/update-model/$modelId': typeof AuthenticatedModelsUpdateModelModelIdRoute
  '/moderators/profile/$moderatorId': typeof AuthenticatedModeratorsProfileModeratorIdRoute
  '/moderators/update-moderator/$moderatorId': typeof AuthenticatedModeratorsUpdateModeratorModeratorIdRoute
  '/packages/update/$msgId': typeof AuthenticatedPackagesUpdateMsgIdRoute
  '/profile-values/appearances/add': typeof AuthenticatedProfileValuesAppearancesAddRoute
  '/profile-values/best-features/add': typeof AuthenticatedProfileValuesBestFeaturesAddRoute
  '/profile-values/body-arts/add': typeof AuthenticatedProfileValuesBodyArtsAddRoute
  '/profile-values/body-types/add': typeof AuthenticatedProfileValuesBodyTypesAddRoute
  '/profile-values/drinking-habits/add': typeof AuthenticatedProfileValuesDrinkingHabitsAddRoute
  '/profile-values/ethnicities/add': typeof AuthenticatedProfileValuesEthnicitiesAddRoute
  '/profile-values/eye-colors/add': typeof AuthenticatedProfileValuesEyeColorsAddRoute
  '/profile-values/hair-colors/add': typeof AuthenticatedProfileValuesHairColorsAddRoute
  '/profile-values/interests/add': typeof AuthenticatedProfileValuesInterestsAddRoute
  '/profile-values/personalities/add': typeof AuthenticatedProfileValuesPersonalitiesAddRoute
  '/profile-values/relationship-status/add': typeof AuthenticatedProfileValuesRelationshipStatusAddRoute
  '/profile-values/religions/add': typeof AuthenticatedProfileValuesReligionsAddRoute
  '/profile-values/sexual-orientations/add': typeof AuthenticatedProfileValuesSexualOrientationsAddRoute
  '/profile-values/smoking-habits/add': typeof AuthenticatedProfileValuesSmokingHabitsAddRoute
  '/profile-values/star-signs/add': typeof AuthenticatedProfileValuesStarSignsAddRoute
  '/sessions/chat-mod/$conversationId': typeof AuthenticatedSessionsChatModConversationIdRoute
  '/smilies/update/$msgId': typeof AuthenticatedSmiliesUpdateMsgIdRoute
  '/profile-values/appearances': typeof AuthenticatedProfileValuesAppearancesIndexRoute
  '/profile-values/best-features': typeof AuthenticatedProfileValuesBestFeaturesIndexRoute
  '/profile-values/body-arts': typeof AuthenticatedProfileValuesBodyArtsIndexRoute
  '/profile-values/body-types': typeof AuthenticatedProfileValuesBodyTypesIndexRoute
  '/profile-values/drinking-habits': typeof AuthenticatedProfileValuesDrinkingHabitsIndexRoute
  '/profile-values/ethnicities': typeof AuthenticatedProfileValuesEthnicitiesIndexRoute
  '/profile-values/eye-colors': typeof AuthenticatedProfileValuesEyeColorsIndexRoute
  '/profile-values/hair-colors': typeof AuthenticatedProfileValuesHairColorsIndexRoute
  '/profile-values/interests': typeof AuthenticatedProfileValuesInterestsIndexRoute
  '/profile-values/personalities': typeof AuthenticatedProfileValuesPersonalitiesIndexRoute
  '/profile-values/relationship-status': typeof AuthenticatedProfileValuesRelationshipStatusIndexRoute
  '/profile-values/religions': typeof AuthenticatedProfileValuesReligionsIndexRoute
  '/profile-values/sexual-orientations': typeof AuthenticatedProfileValuesSexualOrientationsIndexRoute
  '/profile-values/smoking-habits': typeof AuthenticatedProfileValuesSmokingHabitsIndexRoute
  '/profile-values/star-signs': typeof AuthenticatedProfileValuesStarSignsIndexRoute
  '/members/profile/$memberId/credits': typeof AuthenticatedMembersProfileMemberIdCreditsRoute
  '/members/profile/$memberId/messages': typeof AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren
  '/members/profile/$memberId/pictures': typeof AuthenticatedMembersProfileMemberIdPicturesRoute
  '/profile-values/appearances/update/$appearancesId': typeof AuthenticatedProfileValuesAppearancesUpdateAppearancesIdRoute
  '/profile-values/best-features/update/$bestfeaturesId': typeof AuthenticatedProfileValuesBestFeaturesUpdateBestfeaturesIdRoute
  '/profile-values/body-arts/update/$bodyartsId': typeof AuthenticatedProfileValuesBodyArtsUpdateBodyartsIdRoute
  '/profile-values/body-types/update/$bodytypesId': typeof AuthenticatedProfileValuesBodyTypesUpdateBodytypesIdRoute
  '/profile-values/drinking-habits/update/$drinkinghabitsId': typeof AuthenticatedProfileValuesDrinkingHabitsUpdateDrinkinghabitsIdRoute
  '/profile-values/ethnicities/update/$ethnicitiesId': typeof AuthenticatedProfileValuesEthnicitiesUpdateEthnicitiesIdRoute
  '/profile-values/eye-colors/update/$eyecolorsId': typeof AuthenticatedProfileValuesEyeColorsUpdateEyecolorsIdRoute
  '/profile-values/hair-colors/update/$haircolorsId': typeof AuthenticatedProfileValuesHairColorsUpdateHaircolorsIdRoute
  '/profile-values/interests/update/$interestsId': typeof AuthenticatedProfileValuesInterestsUpdateInterestsIdRoute
  '/profile-values/personalities/update/$personalityId': typeof AuthenticatedProfileValuesPersonalitiesUpdatePersonalityIdRoute
  '/profile-values/relationship-status/update/$relationshipStatusId': typeof AuthenticatedProfileValuesRelationshipStatusUpdateRelationshipStatusIdRoute
  '/profile-values/religions/update/$religionsId': typeof AuthenticatedProfileValuesReligionsUpdateReligionsIdRoute
  '/profile-values/sexual-orientations/update/$sexualorientationsId': typeof AuthenticatedProfileValuesSexualOrientationsUpdateSexualorientationsIdRoute
  '/profile-values/smoking-habits/update/$smokinghabitsId': typeof AuthenticatedProfileValuesSmokingHabitsUpdateSmokinghabitsIdRoute
  '/profile-values/star-signs/update/$starsignsId': typeof AuthenticatedProfileValuesStarSignsUpdateStarsignsIdRoute
  '/members/profile/$memberId/messages/$conversationId': typeof AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute
}
export interface FileRoutesByTo {
  '/unauthorized': typeof UnauthorizedRoute
  '/forgot-password': typeof authForgotPasswordRoute
  '/otp': typeof authOtpRoute
  '/reset-password': typeof authResetPasswordRoute
  '/sign-in': typeof authSignInRoute
  '/sign-up': typeof authSignUpRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/admin-login-activity': typeof AuthenticatedAdminLoginActivityRoute
  '/contact-us': typeof AuthenticatedContactUsRoute
  '/': typeof AuthenticatedIndexRoute
  '/affiliates-offers/add': typeof AuthenticatedAffiliatesOffersAddRoute
  '/affiliates/approved': typeof AuthenticatedAffiliatesApprovedRoute
  '/affiliates/create': typeof AuthenticatedAffiliatesCreateRoute
  '/affiliates/new': typeof AuthenticatedAffiliatesNewRoute
  '/announcements/affiliates': typeof AuthenticatedAnnouncementsAffiliatesRoute
  '/announcements/moderators': typeof AuthenticatedAnnouncementsModeratorsRoute
  '/announcements/white-labels': typeof AuthenticatedAnnouncementsWhiteLabelsRoute
  '/bot-messages/add': typeof AuthenticatedBotMessagesAddRoute
  '/flirt-messages/add': typeof AuthenticatedFlirtMessagesAddRoute
  '/gifs/add': typeof AuthenticatedGifsAddRoute
  '/gifts/add': typeof AuthenticatedGiftsAddRoute
  '/models/create-model': typeof AuthenticatedModelsCreateModelRoute
  '/moderators/chat-group': typeof AuthenticatedModeratorsChatGroupRoute
  '/moderators/create-moderator': typeof AuthenticatedModeratorsCreateModeratorRoute
  '/moderators/domain': typeof AuthenticatedModeratorsDomainRoute
  '/moderators/login-activity': typeof AuthenticatedModeratorsLoginActivityRoute
  '/moderators/reply-messages': typeof AuthenticatedModeratorsReplyMessagesRoute
  '/packages/add': typeof AuthenticatedPackagesAddRoute
  '/sessions/loby': typeof AuthenticatedSessionsLobyRoute
  '/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/smilies/add': typeof AuthenticatedSmiliesAddRoute
  '/affiliates-offers': typeof AuthenticatedAffiliatesOffersIndexRoute
  '/affiliates': typeof AuthenticatedAffiliatesIndexRoute
  '/announcements': typeof AuthenticatedAnnouncementsIndexRoute
  '/bot-messages': typeof AuthenticatedBotMessagesIndexRoute
  '/chats': typeof AuthenticatedChatsIndexRoute
  '/currencies': typeof AuthenticatedCurrenciesIndexRoute
  '/flirt-messages': typeof AuthenticatedFlirtMessagesIndexRoute
  '/gifs': typeof AuthenticatedGifsIndexRoute
  '/gifts': typeof AuthenticatedGiftsIndexRoute
  '/help-center': typeof AuthenticatedHelpCenterIndexRoute
  '/members': typeof AuthenticatedMembersIndexRoute
  '/models': typeof AuthenticatedModelsIndexRoute
  '/moderators': typeof AuthenticatedModeratorsIndexRoute
  '/packages': typeof AuthenticatedPackagesIndexRoute
  '/profile-values': typeof AuthenticatedProfileValuesIndexRoute
  '/sessions': typeof AuthenticatedSessionsIndexRoute
  '/settings': typeof AuthenticatedSettingsIndexRoute
  '/smilies': typeof AuthenticatedSmiliesIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
  '/bot-messages/update/$msgId': typeof AuthenticatedBotMessagesUpdateMsgIdRoute
  '/flirt-messages/update/$msgId': typeof AuthenticatedFlirtMessagesUpdateMsgIdRoute
  '/gifs/update/$msgId': typeof AuthenticatedGifsUpdateMsgIdRoute
  '/gifts/update/$msgId': typeof AuthenticatedGiftsUpdateMsgIdRoute
  '/members/profile/$memberId': typeof AuthenticatedMembersProfileMemberIdRouteWithChildren
  '/models/profile-images/$modelId': typeof AuthenticatedModelsProfileImagesModelIdRoute
  '/models/profile/$modelId': typeof AuthenticatedModelsProfileModelIdRoute
  '/models/update-model/$modelId': typeof AuthenticatedModelsUpdateModelModelIdRoute
  '/moderators/profile/$moderatorId': typeof AuthenticatedModeratorsProfileModeratorIdRoute
  '/moderators/update-moderator/$moderatorId': typeof AuthenticatedModeratorsUpdateModeratorModeratorIdRoute
  '/packages/update/$msgId': typeof AuthenticatedPackagesUpdateMsgIdRoute
  '/profile-values/appearances/add': typeof AuthenticatedProfileValuesAppearancesAddRoute
  '/profile-values/best-features/add': typeof AuthenticatedProfileValuesBestFeaturesAddRoute
  '/profile-values/body-arts/add': typeof AuthenticatedProfileValuesBodyArtsAddRoute
  '/profile-values/body-types/add': typeof AuthenticatedProfileValuesBodyTypesAddRoute
  '/profile-values/drinking-habits/add': typeof AuthenticatedProfileValuesDrinkingHabitsAddRoute
  '/profile-values/ethnicities/add': typeof AuthenticatedProfileValuesEthnicitiesAddRoute
  '/profile-values/eye-colors/add': typeof AuthenticatedProfileValuesEyeColorsAddRoute
  '/profile-values/hair-colors/add': typeof AuthenticatedProfileValuesHairColorsAddRoute
  '/profile-values/interests/add': typeof AuthenticatedProfileValuesInterestsAddRoute
  '/profile-values/personalities/add': typeof AuthenticatedProfileValuesPersonalitiesAddRoute
  '/profile-values/relationship-status/add': typeof AuthenticatedProfileValuesRelationshipStatusAddRoute
  '/profile-values/religions/add': typeof AuthenticatedProfileValuesReligionsAddRoute
  '/profile-values/sexual-orientations/add': typeof AuthenticatedProfileValuesSexualOrientationsAddRoute
  '/profile-values/smoking-habits/add': typeof AuthenticatedProfileValuesSmokingHabitsAddRoute
  '/profile-values/star-signs/add': typeof AuthenticatedProfileValuesStarSignsAddRoute
  '/sessions/chat-mod/$conversationId': typeof AuthenticatedSessionsChatModConversationIdRoute
  '/smilies/update/$msgId': typeof AuthenticatedSmiliesUpdateMsgIdRoute
  '/profile-values/appearances': typeof AuthenticatedProfileValuesAppearancesIndexRoute
  '/profile-values/best-features': typeof AuthenticatedProfileValuesBestFeaturesIndexRoute
  '/profile-values/body-arts': typeof AuthenticatedProfileValuesBodyArtsIndexRoute
  '/profile-values/body-types': typeof AuthenticatedProfileValuesBodyTypesIndexRoute
  '/profile-values/drinking-habits': typeof AuthenticatedProfileValuesDrinkingHabitsIndexRoute
  '/profile-values/ethnicities': typeof AuthenticatedProfileValuesEthnicitiesIndexRoute
  '/profile-values/eye-colors': typeof AuthenticatedProfileValuesEyeColorsIndexRoute
  '/profile-values/hair-colors': typeof AuthenticatedProfileValuesHairColorsIndexRoute
  '/profile-values/interests': typeof AuthenticatedProfileValuesInterestsIndexRoute
  '/profile-values/personalities': typeof AuthenticatedProfileValuesPersonalitiesIndexRoute
  '/profile-values/relationship-status': typeof AuthenticatedProfileValuesRelationshipStatusIndexRoute
  '/profile-values/religions': typeof AuthenticatedProfileValuesReligionsIndexRoute
  '/profile-values/sexual-orientations': typeof AuthenticatedProfileValuesSexualOrientationsIndexRoute
  '/profile-values/smoking-habits': typeof AuthenticatedProfileValuesSmokingHabitsIndexRoute
  '/profile-values/star-signs': typeof AuthenticatedProfileValuesStarSignsIndexRoute
  '/members/profile/$memberId/credits': typeof AuthenticatedMembersProfileMemberIdCreditsRoute
  '/members/profile/$memberId/messages': typeof AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren
  '/members/profile/$memberId/pictures': typeof AuthenticatedMembersProfileMemberIdPicturesRoute
  '/profile-values/appearances/update/$appearancesId': typeof AuthenticatedProfileValuesAppearancesUpdateAppearancesIdRoute
  '/profile-values/best-features/update/$bestfeaturesId': typeof AuthenticatedProfileValuesBestFeaturesUpdateBestfeaturesIdRoute
  '/profile-values/body-arts/update/$bodyartsId': typeof AuthenticatedProfileValuesBodyArtsUpdateBodyartsIdRoute
  '/profile-values/body-types/update/$bodytypesId': typeof AuthenticatedProfileValuesBodyTypesUpdateBodytypesIdRoute
  '/profile-values/drinking-habits/update/$drinkinghabitsId': typeof AuthenticatedProfileValuesDrinkingHabitsUpdateDrinkinghabitsIdRoute
  '/profile-values/ethnicities/update/$ethnicitiesId': typeof AuthenticatedProfileValuesEthnicitiesUpdateEthnicitiesIdRoute
  '/profile-values/eye-colors/update/$eyecolorsId': typeof AuthenticatedProfileValuesEyeColorsUpdateEyecolorsIdRoute
  '/profile-values/hair-colors/update/$haircolorsId': typeof AuthenticatedProfileValuesHairColorsUpdateHaircolorsIdRoute
  '/profile-values/interests/update/$interestsId': typeof AuthenticatedProfileValuesInterestsUpdateInterestsIdRoute
  '/profile-values/personalities/update/$personalityId': typeof AuthenticatedProfileValuesPersonalitiesUpdatePersonalityIdRoute
  '/profile-values/relationship-status/update/$relationshipStatusId': typeof AuthenticatedProfileValuesRelationshipStatusUpdateRelationshipStatusIdRoute
  '/profile-values/religions/update/$religionsId': typeof AuthenticatedProfileValuesReligionsUpdateReligionsIdRoute
  '/profile-values/sexual-orientations/update/$sexualorientationsId': typeof AuthenticatedProfileValuesSexualOrientationsUpdateSexualorientationsIdRoute
  '/profile-values/smoking-habits/update/$smokinghabitsId': typeof AuthenticatedProfileValuesSmokingHabitsUpdateSmokinghabitsIdRoute
  '/profile-values/star-signs/update/$starsignsId': typeof AuthenticatedProfileValuesStarSignsUpdateStarsignsIdRoute
  '/members/profile/$memberId/messages/$conversationId': typeof AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/unauthorized': typeof UnauthorizedRoute
  '/_authenticated/affiliates': typeof AuthenticatedAffiliatesRouteRouteWithChildren
  '/_authenticated/affiliates-offers': typeof AuthenticatedAffiliatesOffersRouteRouteWithChildren
  '/_authenticated/announcements': typeof AuthenticatedAnnouncementsRouteRouteWithChildren
  '/_authenticated/bot-messages': typeof AuthenticatedBotMessagesRouteRouteWithChildren
  '/_authenticated/currencies': typeof AuthenticatedCurrenciesRouteRouteWithChildren
  '/_authenticated/flirt-messages': typeof AuthenticatedFlirtMessagesRouteRouteWithChildren
  '/_authenticated/gifs': typeof AuthenticatedGifsRouteRouteWithChildren
  '/_authenticated/gifts': typeof AuthenticatedGiftsRouteRouteWithChildren
  '/_authenticated/members': typeof AuthenticatedMembersRouteRouteWithChildren
  '/_authenticated/models': typeof AuthenticatedModelsRouteRouteWithChildren
  '/_authenticated/moderators': typeof AuthenticatedModeratorsRouteRouteWithChildren
  '/_authenticated/packages': typeof AuthenticatedPackagesRouteRouteWithChildren
  '/_authenticated/profile-values': typeof AuthenticatedProfileValuesRouteRouteWithChildren
  '/_authenticated/sessions': typeof AuthenticatedSessionsRouteRouteWithChildren
  '/_authenticated/settings': typeof AuthenticatedSettingsRouteRouteWithChildren
  '/_authenticated/smilies': typeof AuthenticatedSmiliesRouteRouteWithChildren
  '/(auth)/forgot-password': typeof authForgotPasswordRoute
  '/(auth)/otp': typeof authOtpRoute
  '/(auth)/reset-password': typeof authResetPasswordRoute
  '/(auth)/sign-in': typeof authSignInRoute
  '/(auth)/sign-up': typeof authSignUpRoute
  '/(errors)/401': typeof errors401Route
  '/(errors)/403': typeof errors403Route
  '/(errors)/404': typeof errors404Route
  '/(errors)/500': typeof errors500Route
  '/(errors)/503': typeof errors503Route
  '/_authenticated/admin-login-activity': typeof AuthenticatedAdminLoginActivityRoute
  '/_authenticated/contact-us': typeof AuthenticatedContactUsRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/affiliates-offers/add': typeof AuthenticatedAffiliatesOffersAddRoute
  '/_authenticated/affiliates/approved': typeof AuthenticatedAffiliatesApprovedRoute
  '/_authenticated/affiliates/create': typeof AuthenticatedAffiliatesCreateRoute
  '/_authenticated/affiliates/new': typeof AuthenticatedAffiliatesNewRoute
  '/_authenticated/announcements/affiliates': typeof AuthenticatedAnnouncementsAffiliatesRoute
  '/_authenticated/announcements/moderators': typeof AuthenticatedAnnouncementsModeratorsRoute
  '/_authenticated/announcements/white-labels': typeof AuthenticatedAnnouncementsWhiteLabelsRoute
  '/_authenticated/bot-messages/add': typeof AuthenticatedBotMessagesAddRoute
  '/_authenticated/flirt-messages/add': typeof AuthenticatedFlirtMessagesAddRoute
  '/_authenticated/gifs/add': typeof AuthenticatedGifsAddRoute
  '/_authenticated/gifts/add': typeof AuthenticatedGiftsAddRoute
  '/_authenticated/models/create-model': typeof AuthenticatedModelsCreateModelRoute
  '/_authenticated/moderators/chat-group': typeof AuthenticatedModeratorsChatGroupRoute
  '/_authenticated/moderators/create-moderator': typeof AuthenticatedModeratorsCreateModeratorRoute
  '/_authenticated/moderators/domain': typeof AuthenticatedModeratorsDomainRoute
  '/_authenticated/moderators/login-activity': typeof AuthenticatedModeratorsLoginActivityRoute
  '/_authenticated/moderators/reply-messages': typeof AuthenticatedModeratorsReplyMessagesRoute
  '/_authenticated/packages/add': typeof AuthenticatedPackagesAddRoute
  '/_authenticated/sessions/loby': typeof AuthenticatedSessionsLobyRoute
  '/_authenticated/settings/account': typeof AuthenticatedSettingsAccountRoute
  '/_authenticated/settings/appearance': typeof AuthenticatedSettingsAppearanceRoute
  '/_authenticated/settings/display': typeof AuthenticatedSettingsDisplayRoute
  '/_authenticated/settings/notifications': typeof AuthenticatedSettingsNotificationsRoute
  '/_authenticated/smilies/add': typeof AuthenticatedSmiliesAddRoute
  '/_authenticated/affiliates-offers/': typeof AuthenticatedAffiliatesOffersIndexRoute
  '/_authenticated/affiliates/': typeof AuthenticatedAffiliatesIndexRoute
  '/_authenticated/announcements/': typeof AuthenticatedAnnouncementsIndexRoute
  '/_authenticated/bot-messages/': typeof AuthenticatedBotMessagesIndexRoute
  '/_authenticated/chats/': typeof AuthenticatedChatsIndexRoute
  '/_authenticated/currencies/': typeof AuthenticatedCurrenciesIndexRoute
  '/_authenticated/flirt-messages/': typeof AuthenticatedFlirtMessagesIndexRoute
  '/_authenticated/gifs/': typeof AuthenticatedGifsIndexRoute
  '/_authenticated/gifts/': typeof AuthenticatedGiftsIndexRoute
  '/_authenticated/help-center/': typeof AuthenticatedHelpCenterIndexRoute
  '/_authenticated/members/': typeof AuthenticatedMembersIndexRoute
  '/_authenticated/models/': typeof AuthenticatedModelsIndexRoute
  '/_authenticated/moderators/': typeof AuthenticatedModeratorsIndexRoute
  '/_authenticated/packages/': typeof AuthenticatedPackagesIndexRoute
  '/_authenticated/profile-values/': typeof AuthenticatedProfileValuesIndexRoute
  '/_authenticated/sessions/': typeof AuthenticatedSessionsIndexRoute
  '/_authenticated/settings/': typeof AuthenticatedSettingsIndexRoute
  '/_authenticated/smilies/': typeof AuthenticatedSmiliesIndexRoute
  '/_authenticated/users/': typeof AuthenticatedUsersIndexRoute
  '/_authenticated/bot-messages/update/$msgId': typeof AuthenticatedBotMessagesUpdateMsgIdRoute
  '/_authenticated/flirt-messages/update/$msgId': typeof AuthenticatedFlirtMessagesUpdateMsgIdRoute
  '/_authenticated/gifs/update/$msgId': typeof AuthenticatedGifsUpdateMsgIdRoute
  '/_authenticated/gifts/update/$msgId': typeof AuthenticatedGiftsUpdateMsgIdRoute
  '/_authenticated/members/profile/$memberId': typeof AuthenticatedMembersProfileMemberIdRouteWithChildren
  '/_authenticated/models/profile-images/$modelId': typeof AuthenticatedModelsProfileImagesModelIdRoute
  '/_authenticated/models/profile/$modelId': typeof AuthenticatedModelsProfileModelIdRoute
  '/_authenticated/models/update-model/$modelId': typeof AuthenticatedModelsUpdateModelModelIdRoute
  '/_authenticated/moderators/profile/$moderatorId': typeof AuthenticatedModeratorsProfileModeratorIdRoute
  '/_authenticated/moderators/update-moderator/$moderatorId': typeof AuthenticatedModeratorsUpdateModeratorModeratorIdRoute
  '/_authenticated/packages/update/$msgId': typeof AuthenticatedPackagesUpdateMsgIdRoute
  '/_authenticated/profile-values/appearances/add': typeof AuthenticatedProfileValuesAppearancesAddRoute
  '/_authenticated/profile-values/best-features/add': typeof AuthenticatedProfileValuesBestFeaturesAddRoute
  '/_authenticated/profile-values/body-arts/add': typeof AuthenticatedProfileValuesBodyArtsAddRoute
  '/_authenticated/profile-values/body-types/add': typeof AuthenticatedProfileValuesBodyTypesAddRoute
  '/_authenticated/profile-values/drinking-habits/add': typeof AuthenticatedProfileValuesDrinkingHabitsAddRoute
  '/_authenticated/profile-values/ethnicities/add': typeof AuthenticatedProfileValuesEthnicitiesAddRoute
  '/_authenticated/profile-values/eye-colors/add': typeof AuthenticatedProfileValuesEyeColorsAddRoute
  '/_authenticated/profile-values/hair-colors/add': typeof AuthenticatedProfileValuesHairColorsAddRoute
  '/_authenticated/profile-values/interests/add': typeof AuthenticatedProfileValuesInterestsAddRoute
  '/_authenticated/profile-values/personalities/add': typeof AuthenticatedProfileValuesPersonalitiesAddRoute
  '/_authenticated/profile-values/relationship-status/add': typeof AuthenticatedProfileValuesRelationshipStatusAddRoute
  '/_authenticated/profile-values/religions/add': typeof AuthenticatedProfileValuesReligionsAddRoute
  '/_authenticated/profile-values/sexual-orientations/add': typeof AuthenticatedProfileValuesSexualOrientationsAddRoute
  '/_authenticated/profile-values/smoking-habits/add': typeof AuthenticatedProfileValuesSmokingHabitsAddRoute
  '/_authenticated/profile-values/star-signs/add': typeof AuthenticatedProfileValuesStarSignsAddRoute
  '/_authenticated/sessions/chat-mod/$conversationId': typeof AuthenticatedSessionsChatModConversationIdRoute
  '/_authenticated/smilies/update/$msgId': typeof AuthenticatedSmiliesUpdateMsgIdRoute
  '/_authenticated/profile-values/appearances/': typeof AuthenticatedProfileValuesAppearancesIndexRoute
  '/_authenticated/profile-values/best-features/': typeof AuthenticatedProfileValuesBestFeaturesIndexRoute
  '/_authenticated/profile-values/body-arts/': typeof AuthenticatedProfileValuesBodyArtsIndexRoute
  '/_authenticated/profile-values/body-types/': typeof AuthenticatedProfileValuesBodyTypesIndexRoute
  '/_authenticated/profile-values/drinking-habits/': typeof AuthenticatedProfileValuesDrinkingHabitsIndexRoute
  '/_authenticated/profile-values/ethnicities/': typeof AuthenticatedProfileValuesEthnicitiesIndexRoute
  '/_authenticated/profile-values/eye-colors/': typeof AuthenticatedProfileValuesEyeColorsIndexRoute
  '/_authenticated/profile-values/hair-colors/': typeof AuthenticatedProfileValuesHairColorsIndexRoute
  '/_authenticated/profile-values/interests/': typeof AuthenticatedProfileValuesInterestsIndexRoute
  '/_authenticated/profile-values/personalities/': typeof AuthenticatedProfileValuesPersonalitiesIndexRoute
  '/_authenticated/profile-values/relationship-status/': typeof AuthenticatedProfileValuesRelationshipStatusIndexRoute
  '/_authenticated/profile-values/religions/': typeof AuthenticatedProfileValuesReligionsIndexRoute
  '/_authenticated/profile-values/sexual-orientations/': typeof AuthenticatedProfileValuesSexualOrientationsIndexRoute
  '/_authenticated/profile-values/smoking-habits/': typeof AuthenticatedProfileValuesSmokingHabitsIndexRoute
  '/_authenticated/profile-values/star-signs/': typeof AuthenticatedProfileValuesStarSignsIndexRoute
  '/_authenticated/members/profile/$memberId/credits': typeof AuthenticatedMembersProfileMemberIdCreditsRoute
  '/_authenticated/members/profile/$memberId/messages': typeof AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren
  '/_authenticated/members/profile/$memberId/pictures': typeof AuthenticatedMembersProfileMemberIdPicturesRoute
  '/_authenticated/profile-values/appearances/update/$appearancesId': typeof AuthenticatedProfileValuesAppearancesUpdateAppearancesIdRoute
  '/_authenticated/profile-values/best-features/update/$bestfeaturesId': typeof AuthenticatedProfileValuesBestFeaturesUpdateBestfeaturesIdRoute
  '/_authenticated/profile-values/body-arts/update/$bodyartsId': typeof AuthenticatedProfileValuesBodyArtsUpdateBodyartsIdRoute
  '/_authenticated/profile-values/body-types/update/$bodytypesId': typeof AuthenticatedProfileValuesBodyTypesUpdateBodytypesIdRoute
  '/_authenticated/profile-values/drinking-habits/update/$drinkinghabitsId': typeof AuthenticatedProfileValuesDrinkingHabitsUpdateDrinkinghabitsIdRoute
  '/_authenticated/profile-values/ethnicities/update/$ethnicitiesId': typeof AuthenticatedProfileValuesEthnicitiesUpdateEthnicitiesIdRoute
  '/_authenticated/profile-values/eye-colors/update/$eyecolorsId': typeof AuthenticatedProfileValuesEyeColorsUpdateEyecolorsIdRoute
  '/_authenticated/profile-values/hair-colors/update/$haircolorsId': typeof AuthenticatedProfileValuesHairColorsUpdateHaircolorsIdRoute
  '/_authenticated/profile-values/interests/update/$interestsId': typeof AuthenticatedProfileValuesInterestsUpdateInterestsIdRoute
  '/_authenticated/profile-values/personalities/update/$personalityId': typeof AuthenticatedProfileValuesPersonalitiesUpdatePersonalityIdRoute
  '/_authenticated/profile-values/relationship-status/update/$relationshipStatusId': typeof AuthenticatedProfileValuesRelationshipStatusUpdateRelationshipStatusIdRoute
  '/_authenticated/profile-values/religions/update/$religionsId': typeof AuthenticatedProfileValuesReligionsUpdateReligionsIdRoute
  '/_authenticated/profile-values/sexual-orientations/update/$sexualorientationsId': typeof AuthenticatedProfileValuesSexualOrientationsUpdateSexualorientationsIdRoute
  '/_authenticated/profile-values/smoking-habits/update/$smokinghabitsId': typeof AuthenticatedProfileValuesSmokingHabitsUpdateSmokinghabitsIdRoute
  '/_authenticated/profile-values/star-signs/update/$starsignsId': typeof AuthenticatedProfileValuesStarSignsUpdateStarsignsIdRoute
  '/_authenticated/members/profile/$memberId/messages/$conversationId': typeof AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/unauthorized'
    | '/affiliates'
    | '/affiliates-offers'
    | '/announcements'
    | '/bot-messages'
    | '/currencies'
    | '/flirt-messages'
    | '/gifs'
    | '/gifts'
    | '/members'
    | '/models'
    | '/moderators'
    | '/packages'
    | '/profile-values'
    | '/sessions'
    | '/settings'
    | '/smilies'
    | '/forgot-password'
    | '/otp'
    | '/reset-password'
    | '/sign-in'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/admin-login-activity'
    | '/contact-us'
    | '/'
    | '/affiliates-offers/add'
    | '/affiliates/approved'
    | '/affiliates/create'
    | '/affiliates/new'
    | '/announcements/affiliates'
    | '/announcements/moderators'
    | '/announcements/white-labels'
    | '/bot-messages/add'
    | '/flirt-messages/add'
    | '/gifs/add'
    | '/gifts/add'
    | '/models/create-model'
    | '/moderators/chat-group'
    | '/moderators/create-moderator'
    | '/moderators/domain'
    | '/moderators/login-activity'
    | '/moderators/reply-messages'
    | '/packages/add'
    | '/sessions/loby'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/smilies/add'
    | '/affiliates-offers/'
    | '/affiliates/'
    | '/announcements/'
    | '/bot-messages/'
    | '/chats'
    | '/currencies/'
    | '/flirt-messages/'
    | '/gifs/'
    | '/gifts/'
    | '/help-center'
    | '/members/'
    | '/models/'
    | '/moderators/'
    | '/packages/'
    | '/profile-values/'
    | '/sessions/'
    | '/settings/'
    | '/smilies/'
    | '/users'
    | '/bot-messages/update/$msgId'
    | '/flirt-messages/update/$msgId'
    | '/gifs/update/$msgId'
    | '/gifts/update/$msgId'
    | '/members/profile/$memberId'
    | '/models/profile-images/$modelId'
    | '/models/profile/$modelId'
    | '/models/update-model/$modelId'
    | '/moderators/profile/$moderatorId'
    | '/moderators/update-moderator/$moderatorId'
    | '/packages/update/$msgId'
    | '/profile-values/appearances/add'
    | '/profile-values/best-features/add'
    | '/profile-values/body-arts/add'
    | '/profile-values/body-types/add'
    | '/profile-values/drinking-habits/add'
    | '/profile-values/ethnicities/add'
    | '/profile-values/eye-colors/add'
    | '/profile-values/hair-colors/add'
    | '/profile-values/interests/add'
    | '/profile-values/personalities/add'
    | '/profile-values/relationship-status/add'
    | '/profile-values/religions/add'
    | '/profile-values/sexual-orientations/add'
    | '/profile-values/smoking-habits/add'
    | '/profile-values/star-signs/add'
    | '/sessions/chat-mod/$conversationId'
    | '/smilies/update/$msgId'
    | '/profile-values/appearances'
    | '/profile-values/best-features'
    | '/profile-values/body-arts'
    | '/profile-values/body-types'
    | '/profile-values/drinking-habits'
    | '/profile-values/ethnicities'
    | '/profile-values/eye-colors'
    | '/profile-values/hair-colors'
    | '/profile-values/interests'
    | '/profile-values/personalities'
    | '/profile-values/relationship-status'
    | '/profile-values/religions'
    | '/profile-values/sexual-orientations'
    | '/profile-values/smoking-habits'
    | '/profile-values/star-signs'
    | '/members/profile/$memberId/credits'
    | '/members/profile/$memberId/messages'
    | '/members/profile/$memberId/pictures'
    | '/profile-values/appearances/update/$appearancesId'
    | '/profile-values/best-features/update/$bestfeaturesId'
    | '/profile-values/body-arts/update/$bodyartsId'
    | '/profile-values/body-types/update/$bodytypesId'
    | '/profile-values/drinking-habits/update/$drinkinghabitsId'
    | '/profile-values/ethnicities/update/$ethnicitiesId'
    | '/profile-values/eye-colors/update/$eyecolorsId'
    | '/profile-values/hair-colors/update/$haircolorsId'
    | '/profile-values/interests/update/$interestsId'
    | '/profile-values/personalities/update/$personalityId'
    | '/profile-values/relationship-status/update/$relationshipStatusId'
    | '/profile-values/religions/update/$religionsId'
    | '/profile-values/sexual-orientations/update/$sexualorientationsId'
    | '/profile-values/smoking-habits/update/$smokinghabitsId'
    | '/profile-values/star-signs/update/$starsignsId'
    | '/members/profile/$memberId/messages/$conversationId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/unauthorized'
    | '/forgot-password'
    | '/otp'
    | '/reset-password'
    | '/sign-in'
    | '/sign-up'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/admin-login-activity'
    | '/contact-us'
    | '/'
    | '/affiliates-offers/add'
    | '/affiliates/approved'
    | '/affiliates/create'
    | '/affiliates/new'
    | '/announcements/affiliates'
    | '/announcements/moderators'
    | '/announcements/white-labels'
    | '/bot-messages/add'
    | '/flirt-messages/add'
    | '/gifs/add'
    | '/gifts/add'
    | '/models/create-model'
    | '/moderators/chat-group'
    | '/moderators/create-moderator'
    | '/moderators/domain'
    | '/moderators/login-activity'
    | '/moderators/reply-messages'
    | '/packages/add'
    | '/sessions/loby'
    | '/settings/account'
    | '/settings/appearance'
    | '/settings/display'
    | '/settings/notifications'
    | '/smilies/add'
    | '/affiliates-offers'
    | '/affiliates'
    | '/announcements'
    | '/bot-messages'
    | '/chats'
    | '/currencies'
    | '/flirt-messages'
    | '/gifs'
    | '/gifts'
    | '/help-center'
    | '/members'
    | '/models'
    | '/moderators'
    | '/packages'
    | '/profile-values'
    | '/sessions'
    | '/settings'
    | '/smilies'
    | '/users'
    | '/bot-messages/update/$msgId'
    | '/flirt-messages/update/$msgId'
    | '/gifs/update/$msgId'
    | '/gifts/update/$msgId'
    | '/members/profile/$memberId'
    | '/models/profile-images/$modelId'
    | '/models/profile/$modelId'
    | '/models/update-model/$modelId'
    | '/moderators/profile/$moderatorId'
    | '/moderators/update-moderator/$moderatorId'
    | '/packages/update/$msgId'
    | '/profile-values/appearances/add'
    | '/profile-values/best-features/add'
    | '/profile-values/body-arts/add'
    | '/profile-values/body-types/add'
    | '/profile-values/drinking-habits/add'
    | '/profile-values/ethnicities/add'
    | '/profile-values/eye-colors/add'
    | '/profile-values/hair-colors/add'
    | '/profile-values/interests/add'
    | '/profile-values/personalities/add'
    | '/profile-values/relationship-status/add'
    | '/profile-values/religions/add'
    | '/profile-values/sexual-orientations/add'
    | '/profile-values/smoking-habits/add'
    | '/profile-values/star-signs/add'
    | '/sessions/chat-mod/$conversationId'
    | '/smilies/update/$msgId'
    | '/profile-values/appearances'
    | '/profile-values/best-features'
    | '/profile-values/body-arts'
    | '/profile-values/body-types'
    | '/profile-values/drinking-habits'
    | '/profile-values/ethnicities'
    | '/profile-values/eye-colors'
    | '/profile-values/hair-colors'
    | '/profile-values/interests'
    | '/profile-values/personalities'
    | '/profile-values/relationship-status'
    | '/profile-values/religions'
    | '/profile-values/sexual-orientations'
    | '/profile-values/smoking-habits'
    | '/profile-values/star-signs'
    | '/members/profile/$memberId/credits'
    | '/members/profile/$memberId/messages'
    | '/members/profile/$memberId/pictures'
    | '/profile-values/appearances/update/$appearancesId'
    | '/profile-values/best-features/update/$bestfeaturesId'
    | '/profile-values/body-arts/update/$bodyartsId'
    | '/profile-values/body-types/update/$bodytypesId'
    | '/profile-values/drinking-habits/update/$drinkinghabitsId'
    | '/profile-values/ethnicities/update/$ethnicitiesId'
    | '/profile-values/eye-colors/update/$eyecolorsId'
    | '/profile-values/hair-colors/update/$haircolorsId'
    | '/profile-values/interests/update/$interestsId'
    | '/profile-values/personalities/update/$personalityId'
    | '/profile-values/relationship-status/update/$relationshipStatusId'
    | '/profile-values/religions/update/$religionsId'
    | '/profile-values/sexual-orientations/update/$sexualorientationsId'
    | '/profile-values/smoking-habits/update/$smokinghabitsId'
    | '/profile-values/star-signs/update/$starsignsId'
    | '/members/profile/$memberId/messages/$conversationId'
  id:
    | '__root__'
    | '/_authenticated'
    | '/unauthorized'
    | '/_authenticated/affiliates'
    | '/_authenticated/affiliates-offers'
    | '/_authenticated/announcements'
    | '/_authenticated/bot-messages'
    | '/_authenticated/currencies'
    | '/_authenticated/flirt-messages'
    | '/_authenticated/gifs'
    | '/_authenticated/gifts'
    | '/_authenticated/members'
    | '/_authenticated/models'
    | '/_authenticated/moderators'
    | '/_authenticated/packages'
    | '/_authenticated/profile-values'
    | '/_authenticated/sessions'
    | '/_authenticated/settings'
    | '/_authenticated/smilies'
    | '/(auth)/forgot-password'
    | '/(auth)/otp'
    | '/(auth)/reset-password'
    | '/(auth)/sign-in'
    | '/(auth)/sign-up'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/admin-login-activity'
    | '/_authenticated/contact-us'
    | '/_authenticated/'
    | '/_authenticated/affiliates-offers/add'
    | '/_authenticated/affiliates/approved'
    | '/_authenticated/affiliates/create'
    | '/_authenticated/affiliates/new'
    | '/_authenticated/announcements/affiliates'
    | '/_authenticated/announcements/moderators'
    | '/_authenticated/announcements/white-labels'
    | '/_authenticated/bot-messages/add'
    | '/_authenticated/flirt-messages/add'
    | '/_authenticated/gifs/add'
    | '/_authenticated/gifts/add'
    | '/_authenticated/models/create-model'
    | '/_authenticated/moderators/chat-group'
    | '/_authenticated/moderators/create-moderator'
    | '/_authenticated/moderators/domain'
    | '/_authenticated/moderators/login-activity'
    | '/_authenticated/moderators/reply-messages'
    | '/_authenticated/packages/add'
    | '/_authenticated/sessions/loby'
    | '/_authenticated/settings/account'
    | '/_authenticated/settings/appearance'
    | '/_authenticated/settings/display'
    | '/_authenticated/settings/notifications'
    | '/_authenticated/smilies/add'
    | '/_authenticated/affiliates-offers/'
    | '/_authenticated/affiliates/'
    | '/_authenticated/announcements/'
    | '/_authenticated/bot-messages/'
    | '/_authenticated/chats/'
    | '/_authenticated/currencies/'
    | '/_authenticated/flirt-messages/'
    | '/_authenticated/gifs/'
    | '/_authenticated/gifts/'
    | '/_authenticated/help-center/'
    | '/_authenticated/members/'
    | '/_authenticated/models/'
    | '/_authenticated/moderators/'
    | '/_authenticated/packages/'
    | '/_authenticated/profile-values/'
    | '/_authenticated/sessions/'
    | '/_authenticated/settings/'
    | '/_authenticated/smilies/'
    | '/_authenticated/users/'
    | '/_authenticated/bot-messages/update/$msgId'
    | '/_authenticated/flirt-messages/update/$msgId'
    | '/_authenticated/gifs/update/$msgId'
    | '/_authenticated/gifts/update/$msgId'
    | '/_authenticated/members/profile/$memberId'
    | '/_authenticated/models/profile-images/$modelId'
    | '/_authenticated/models/profile/$modelId'
    | '/_authenticated/models/update-model/$modelId'
    | '/_authenticated/moderators/profile/$moderatorId'
    | '/_authenticated/moderators/update-moderator/$moderatorId'
    | '/_authenticated/packages/update/$msgId'
    | '/_authenticated/profile-values/appearances/add'
    | '/_authenticated/profile-values/best-features/add'
    | '/_authenticated/profile-values/body-arts/add'
    | '/_authenticated/profile-values/body-types/add'
    | '/_authenticated/profile-values/drinking-habits/add'
    | '/_authenticated/profile-values/ethnicities/add'
    | '/_authenticated/profile-values/eye-colors/add'
    | '/_authenticated/profile-values/hair-colors/add'
    | '/_authenticated/profile-values/interests/add'
    | '/_authenticated/profile-values/personalities/add'
    | '/_authenticated/profile-values/relationship-status/add'
    | '/_authenticated/profile-values/religions/add'
    | '/_authenticated/profile-values/sexual-orientations/add'
    | '/_authenticated/profile-values/smoking-habits/add'
    | '/_authenticated/profile-values/star-signs/add'
    | '/_authenticated/sessions/chat-mod/$conversationId'
    | '/_authenticated/smilies/update/$msgId'
    | '/_authenticated/profile-values/appearances/'
    | '/_authenticated/profile-values/best-features/'
    | '/_authenticated/profile-values/body-arts/'
    | '/_authenticated/profile-values/body-types/'
    | '/_authenticated/profile-values/drinking-habits/'
    | '/_authenticated/profile-values/ethnicities/'
    | '/_authenticated/profile-values/eye-colors/'
    | '/_authenticated/profile-values/hair-colors/'
    | '/_authenticated/profile-values/interests/'
    | '/_authenticated/profile-values/personalities/'
    | '/_authenticated/profile-values/relationship-status/'
    | '/_authenticated/profile-values/religions/'
    | '/_authenticated/profile-values/sexual-orientations/'
    | '/_authenticated/profile-values/smoking-habits/'
    | '/_authenticated/profile-values/star-signs/'
    | '/_authenticated/members/profile/$memberId/credits'
    | '/_authenticated/members/profile/$memberId/messages'
    | '/_authenticated/members/profile/$memberId/pictures'
    | '/_authenticated/profile-values/appearances/update/$appearancesId'
    | '/_authenticated/profile-values/best-features/update/$bestfeaturesId'
    | '/_authenticated/profile-values/body-arts/update/$bodyartsId'
    | '/_authenticated/profile-values/body-types/update/$bodytypesId'
    | '/_authenticated/profile-values/drinking-habits/update/$drinkinghabitsId'
    | '/_authenticated/profile-values/ethnicities/update/$ethnicitiesId'
    | '/_authenticated/profile-values/eye-colors/update/$eyecolorsId'
    | '/_authenticated/profile-values/hair-colors/update/$haircolorsId'
    | '/_authenticated/profile-values/interests/update/$interestsId'
    | '/_authenticated/profile-values/personalities/update/$personalityId'
    | '/_authenticated/profile-values/relationship-status/update/$relationshipStatusId'
    | '/_authenticated/profile-values/religions/update/$religionsId'
    | '/_authenticated/profile-values/sexual-orientations/update/$sexualorientationsId'
    | '/_authenticated/profile-values/smoking-habits/update/$smokinghabitsId'
    | '/_authenticated/profile-values/star-signs/update/$starsignsId'
    | '/_authenticated/members/profile/$memberId/messages/$conversationId'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  UnauthorizedRoute: typeof UnauthorizedRoute
  authForgotPasswordRoute: typeof authForgotPasswordRoute
  authOtpRoute: typeof authOtpRoute
  authResetPasswordRoute: typeof authResetPasswordRoute
  authSignInRoute: typeof authSignInRoute
  authSignUpRoute: typeof authSignUpRoute
  errors401Route: typeof errors401Route
  errors403Route: typeof errors403Route
  errors404Route: typeof errors404Route
  errors500Route: typeof errors500Route
  errors503Route: typeof errors503Route
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/unauthorized': {
      id: '/unauthorized'
      path: '/unauthorized'
      fullPath: '/unauthorized'
      preLoaderRoute: typeof UnauthorizedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/contact-us': {
      id: '/_authenticated/contact-us'
      path: '/contact-us'
      fullPath: '/contact-us'
      preLoaderRoute: typeof AuthenticatedContactUsRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/admin-login-activity': {
      id: '/_authenticated/admin-login-activity'
      path: '/admin-login-activity'
      fullPath: '/admin-login-activity'
      preLoaderRoute: typeof AuthenticatedAdminLoginActivityRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/sign-up': {
      id: '/(auth)/sign-up'
      path: '/sign-up'
      fullPath: '/sign-up'
      preLoaderRoute: typeof authSignUpRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/sign-in': {
      id: '/(auth)/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof authSignInRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/reset-password': {
      id: '/(auth)/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof authResetPasswordRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/otp': {
      id: '/(auth)/otp'
      path: '/otp'
      fullPath: '/otp'
      preLoaderRoute: typeof authOtpRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(auth)/forgot-password': {
      id: '/(auth)/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof authForgotPasswordRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/smilies': {
      id: '/_authenticated/smilies'
      path: '/smilies'
      fullPath: '/smilies'
      preLoaderRoute: typeof AuthenticatedSmiliesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/sessions': {
      id: '/_authenticated/sessions'
      path: '/sessions'
      fullPath: '/sessions'
      preLoaderRoute: typeof AuthenticatedSessionsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/profile-values': {
      id: '/_authenticated/profile-values'
      path: '/profile-values'
      fullPath: '/profile-values'
      preLoaderRoute: typeof AuthenticatedProfileValuesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/packages': {
      id: '/_authenticated/packages'
      path: '/packages'
      fullPath: '/packages'
      preLoaderRoute: typeof AuthenticatedPackagesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/moderators': {
      id: '/_authenticated/moderators'
      path: '/moderators'
      fullPath: '/moderators'
      preLoaderRoute: typeof AuthenticatedModeratorsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/models': {
      id: '/_authenticated/models'
      path: '/models'
      fullPath: '/models'
      preLoaderRoute: typeof AuthenticatedModelsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/members': {
      id: '/_authenticated/members'
      path: '/members'
      fullPath: '/members'
      preLoaderRoute: typeof AuthenticatedMembersRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/gifts': {
      id: '/_authenticated/gifts'
      path: '/gifts'
      fullPath: '/gifts'
      preLoaderRoute: typeof AuthenticatedGiftsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/gifs': {
      id: '/_authenticated/gifs'
      path: '/gifs'
      fullPath: '/gifs'
      preLoaderRoute: typeof AuthenticatedGifsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/flirt-messages': {
      id: '/_authenticated/flirt-messages'
      path: '/flirt-messages'
      fullPath: '/flirt-messages'
      preLoaderRoute: typeof AuthenticatedFlirtMessagesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/currencies': {
      id: '/_authenticated/currencies'
      path: '/currencies'
      fullPath: '/currencies'
      preLoaderRoute: typeof AuthenticatedCurrenciesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/bot-messages': {
      id: '/_authenticated/bot-messages'
      path: '/bot-messages'
      fullPath: '/bot-messages'
      preLoaderRoute: typeof AuthenticatedBotMessagesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/announcements': {
      id: '/_authenticated/announcements'
      path: '/announcements'
      fullPath: '/announcements'
      preLoaderRoute: typeof AuthenticatedAnnouncementsRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/affiliates-offers': {
      id: '/_authenticated/affiliates-offers'
      path: '/affiliates-offers'
      fullPath: '/affiliates-offers'
      preLoaderRoute: typeof AuthenticatedAffiliatesOffersRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/affiliates': {
      id: '/_authenticated/affiliates'
      path: '/affiliates'
      fullPath: '/affiliates'
      preLoaderRoute: typeof AuthenticatedAffiliatesRouteRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/users/': {
      id: '/_authenticated/users/'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof AuthenticatedUsersIndexRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/smilies/': {
      id: '/_authenticated/smilies/'
      path: '/'
      fullPath: '/smilies/'
      preLoaderRoute: typeof AuthenticatedSmiliesIndexRouteImport
      parentRoute: typeof AuthenticatedSmiliesRouteRoute
    }
    '/_authenticated/settings/': {
      id: '/_authenticated/settings/'
      path: '/'
      fullPath: '/settings/'
      preLoaderRoute: typeof AuthenticatedSettingsIndexRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/sessions/': {
      id: '/_authenticated/sessions/'
      path: '/'
      fullPath: '/sessions/'
      preLoaderRoute: typeof AuthenticatedSessionsIndexRouteImport
      parentRoute: typeof AuthenticatedSessionsRouteRoute
    }
    '/_authenticated/profile-values/': {
      id: '/_authenticated/profile-values/'
      path: '/'
      fullPath: '/profile-values/'
      preLoaderRoute: typeof AuthenticatedProfileValuesIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/packages/': {
      id: '/_authenticated/packages/'
      path: '/'
      fullPath: '/packages/'
      preLoaderRoute: typeof AuthenticatedPackagesIndexRouteImport
      parentRoute: typeof AuthenticatedPackagesRouteRoute
    }
    '/_authenticated/moderators/': {
      id: '/_authenticated/moderators/'
      path: '/'
      fullPath: '/moderators/'
      preLoaderRoute: typeof AuthenticatedModeratorsIndexRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/models/': {
      id: '/_authenticated/models/'
      path: '/'
      fullPath: '/models/'
      preLoaderRoute: typeof AuthenticatedModelsIndexRouteImport
      parentRoute: typeof AuthenticatedModelsRouteRoute
    }
    '/_authenticated/members/': {
      id: '/_authenticated/members/'
      path: '/'
      fullPath: '/members/'
      preLoaderRoute: typeof AuthenticatedMembersIndexRouteImport
      parentRoute: typeof AuthenticatedMembersRouteRoute
    }
    '/_authenticated/help-center/': {
      id: '/_authenticated/help-center/'
      path: '/help-center'
      fullPath: '/help-center'
      preLoaderRoute: typeof AuthenticatedHelpCenterIndexRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/gifts/': {
      id: '/_authenticated/gifts/'
      path: '/'
      fullPath: '/gifts/'
      preLoaderRoute: typeof AuthenticatedGiftsIndexRouteImport
      parentRoute: typeof AuthenticatedGiftsRouteRoute
    }
    '/_authenticated/gifs/': {
      id: '/_authenticated/gifs/'
      path: '/'
      fullPath: '/gifs/'
      preLoaderRoute: typeof AuthenticatedGifsIndexRouteImport
      parentRoute: typeof AuthenticatedGifsRouteRoute
    }
    '/_authenticated/flirt-messages/': {
      id: '/_authenticated/flirt-messages/'
      path: '/'
      fullPath: '/flirt-messages/'
      preLoaderRoute: typeof AuthenticatedFlirtMessagesIndexRouteImport
      parentRoute: typeof AuthenticatedFlirtMessagesRouteRoute
    }
    '/_authenticated/currencies/': {
      id: '/_authenticated/currencies/'
      path: '/'
      fullPath: '/currencies/'
      preLoaderRoute: typeof AuthenticatedCurrenciesIndexRouteImport
      parentRoute: typeof AuthenticatedCurrenciesRouteRoute
    }
    '/_authenticated/chats/': {
      id: '/_authenticated/chats/'
      path: '/chats'
      fullPath: '/chats'
      preLoaderRoute: typeof AuthenticatedChatsIndexRouteImport
      parentRoute: typeof AuthenticatedRouteRoute
    }
    '/_authenticated/bot-messages/': {
      id: '/_authenticated/bot-messages/'
      path: '/'
      fullPath: '/bot-messages/'
      preLoaderRoute: typeof AuthenticatedBotMessagesIndexRouteImport
      parentRoute: typeof AuthenticatedBotMessagesRouteRoute
    }
    '/_authenticated/announcements/': {
      id: '/_authenticated/announcements/'
      path: '/'
      fullPath: '/announcements/'
      preLoaderRoute: typeof AuthenticatedAnnouncementsIndexRouteImport
      parentRoute: typeof AuthenticatedAnnouncementsRouteRoute
    }
    '/_authenticated/affiliates/': {
      id: '/_authenticated/affiliates/'
      path: '/'
      fullPath: '/affiliates/'
      preLoaderRoute: typeof AuthenticatedAffiliatesIndexRouteImport
      parentRoute: typeof AuthenticatedAffiliatesRouteRoute
    }
    '/_authenticated/affiliates-offers/': {
      id: '/_authenticated/affiliates-offers/'
      path: '/'
      fullPath: '/affiliates-offers/'
      preLoaderRoute: typeof AuthenticatedAffiliatesOffersIndexRouteImport
      parentRoute: typeof AuthenticatedAffiliatesOffersRouteRoute
    }
    '/_authenticated/smilies/add': {
      id: '/_authenticated/smilies/add'
      path: '/add'
      fullPath: '/smilies/add'
      preLoaderRoute: typeof AuthenticatedSmiliesAddRouteImport
      parentRoute: typeof AuthenticatedSmiliesRouteRoute
    }
    '/_authenticated/settings/notifications': {
      id: '/_authenticated/settings/notifications'
      path: '/notifications'
      fullPath: '/settings/notifications'
      preLoaderRoute: typeof AuthenticatedSettingsNotificationsRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/settings/display': {
      id: '/_authenticated/settings/display'
      path: '/display'
      fullPath: '/settings/display'
      preLoaderRoute: typeof AuthenticatedSettingsDisplayRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/settings/appearance': {
      id: '/_authenticated/settings/appearance'
      path: '/appearance'
      fullPath: '/settings/appearance'
      preLoaderRoute: typeof AuthenticatedSettingsAppearanceRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/settings/account': {
      id: '/_authenticated/settings/account'
      path: '/account'
      fullPath: '/settings/account'
      preLoaderRoute: typeof AuthenticatedSettingsAccountRouteImport
      parentRoute: typeof AuthenticatedSettingsRouteRoute
    }
    '/_authenticated/sessions/loby': {
      id: '/_authenticated/sessions/loby'
      path: '/loby'
      fullPath: '/sessions/loby'
      preLoaderRoute: typeof AuthenticatedSessionsLobyRouteImport
      parentRoute: typeof AuthenticatedSessionsRouteRoute
    }
    '/_authenticated/packages/add': {
      id: '/_authenticated/packages/add'
      path: '/add'
      fullPath: '/packages/add'
      preLoaderRoute: typeof AuthenticatedPackagesAddRouteImport
      parentRoute: typeof AuthenticatedPackagesRouteRoute
    }
    '/_authenticated/moderators/reply-messages': {
      id: '/_authenticated/moderators/reply-messages'
      path: '/reply-messages'
      fullPath: '/moderators/reply-messages'
      preLoaderRoute: typeof AuthenticatedModeratorsReplyMessagesRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/moderators/login-activity': {
      id: '/_authenticated/moderators/login-activity'
      path: '/login-activity'
      fullPath: '/moderators/login-activity'
      preLoaderRoute: typeof AuthenticatedModeratorsLoginActivityRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/moderators/domain': {
      id: '/_authenticated/moderators/domain'
      path: '/domain'
      fullPath: '/moderators/domain'
      preLoaderRoute: typeof AuthenticatedModeratorsDomainRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/moderators/create-moderator': {
      id: '/_authenticated/moderators/create-moderator'
      path: '/create-moderator'
      fullPath: '/moderators/create-moderator'
      preLoaderRoute: typeof AuthenticatedModeratorsCreateModeratorRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/moderators/chat-group': {
      id: '/_authenticated/moderators/chat-group'
      path: '/chat-group'
      fullPath: '/moderators/chat-group'
      preLoaderRoute: typeof AuthenticatedModeratorsChatGroupRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/models/create-model': {
      id: '/_authenticated/models/create-model'
      path: '/create-model'
      fullPath: '/models/create-model'
      preLoaderRoute: typeof AuthenticatedModelsCreateModelRouteImport
      parentRoute: typeof AuthenticatedModelsRouteRoute
    }
    '/_authenticated/gifts/add': {
      id: '/_authenticated/gifts/add'
      path: '/add'
      fullPath: '/gifts/add'
      preLoaderRoute: typeof AuthenticatedGiftsAddRouteImport
      parentRoute: typeof AuthenticatedGiftsRouteRoute
    }
    '/_authenticated/gifs/add': {
      id: '/_authenticated/gifs/add'
      path: '/add'
      fullPath: '/gifs/add'
      preLoaderRoute: typeof AuthenticatedGifsAddRouteImport
      parentRoute: typeof AuthenticatedGifsRouteRoute
    }
    '/_authenticated/flirt-messages/add': {
      id: '/_authenticated/flirt-messages/add'
      path: '/add'
      fullPath: '/flirt-messages/add'
      preLoaderRoute: typeof AuthenticatedFlirtMessagesAddRouteImport
      parentRoute: typeof AuthenticatedFlirtMessagesRouteRoute
    }
    '/_authenticated/bot-messages/add': {
      id: '/_authenticated/bot-messages/add'
      path: '/add'
      fullPath: '/bot-messages/add'
      preLoaderRoute: typeof AuthenticatedBotMessagesAddRouteImport
      parentRoute: typeof AuthenticatedBotMessagesRouteRoute
    }
    '/_authenticated/announcements/white-labels': {
      id: '/_authenticated/announcements/white-labels'
      path: '/white-labels'
      fullPath: '/announcements/white-labels'
      preLoaderRoute: typeof AuthenticatedAnnouncementsWhiteLabelsRouteImport
      parentRoute: typeof AuthenticatedAnnouncementsRouteRoute
    }
    '/_authenticated/announcements/moderators': {
      id: '/_authenticated/announcements/moderators'
      path: '/moderators'
      fullPath: '/announcements/moderators'
      preLoaderRoute: typeof AuthenticatedAnnouncementsModeratorsRouteImport
      parentRoute: typeof AuthenticatedAnnouncementsRouteRoute
    }
    '/_authenticated/announcements/affiliates': {
      id: '/_authenticated/announcements/affiliates'
      path: '/affiliates'
      fullPath: '/announcements/affiliates'
      preLoaderRoute: typeof AuthenticatedAnnouncementsAffiliatesRouteImport
      parentRoute: typeof AuthenticatedAnnouncementsRouteRoute
    }
    '/_authenticated/affiliates/new': {
      id: '/_authenticated/affiliates/new'
      path: '/new'
      fullPath: '/affiliates/new'
      preLoaderRoute: typeof AuthenticatedAffiliatesNewRouteImport
      parentRoute: typeof AuthenticatedAffiliatesRouteRoute
    }
    '/_authenticated/affiliates/create': {
      id: '/_authenticated/affiliates/create'
      path: '/create'
      fullPath: '/affiliates/create'
      preLoaderRoute: typeof AuthenticatedAffiliatesCreateRouteImport
      parentRoute: typeof AuthenticatedAffiliatesRouteRoute
    }
    '/_authenticated/affiliates/approved': {
      id: '/_authenticated/affiliates/approved'
      path: '/approved'
      fullPath: '/affiliates/approved'
      preLoaderRoute: typeof AuthenticatedAffiliatesApprovedRouteImport
      parentRoute: typeof AuthenticatedAffiliatesRouteRoute
    }
    '/_authenticated/affiliates-offers/add': {
      id: '/_authenticated/affiliates-offers/add'
      path: '/add'
      fullPath: '/affiliates-offers/add'
      preLoaderRoute: typeof AuthenticatedAffiliatesOffersAddRouteImport
      parentRoute: typeof AuthenticatedAffiliatesOffersRouteRoute
    }
    '/_authenticated/profile-values/star-signs/': {
      id: '/_authenticated/profile-values/star-signs/'
      path: '/star-signs'
      fullPath: '/profile-values/star-signs'
      preLoaderRoute: typeof AuthenticatedProfileValuesStarSignsIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/smoking-habits/': {
      id: '/_authenticated/profile-values/smoking-habits/'
      path: '/smoking-habits'
      fullPath: '/profile-values/smoking-habits'
      preLoaderRoute: typeof AuthenticatedProfileValuesSmokingHabitsIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/sexual-orientations/': {
      id: '/_authenticated/profile-values/sexual-orientations/'
      path: '/sexual-orientations'
      fullPath: '/profile-values/sexual-orientations'
      preLoaderRoute: typeof AuthenticatedProfileValuesSexualOrientationsIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/religions/': {
      id: '/_authenticated/profile-values/religions/'
      path: '/religions'
      fullPath: '/profile-values/religions'
      preLoaderRoute: typeof AuthenticatedProfileValuesReligionsIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/relationship-status/': {
      id: '/_authenticated/profile-values/relationship-status/'
      path: '/relationship-status'
      fullPath: '/profile-values/relationship-status'
      preLoaderRoute: typeof AuthenticatedProfileValuesRelationshipStatusIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/personalities/': {
      id: '/_authenticated/profile-values/personalities/'
      path: '/personalities'
      fullPath: '/profile-values/personalities'
      preLoaderRoute: typeof AuthenticatedProfileValuesPersonalitiesIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/interests/': {
      id: '/_authenticated/profile-values/interests/'
      path: '/interests'
      fullPath: '/profile-values/interests'
      preLoaderRoute: typeof AuthenticatedProfileValuesInterestsIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/hair-colors/': {
      id: '/_authenticated/profile-values/hair-colors/'
      path: '/hair-colors'
      fullPath: '/profile-values/hair-colors'
      preLoaderRoute: typeof AuthenticatedProfileValuesHairColorsIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/eye-colors/': {
      id: '/_authenticated/profile-values/eye-colors/'
      path: '/eye-colors'
      fullPath: '/profile-values/eye-colors'
      preLoaderRoute: typeof AuthenticatedProfileValuesEyeColorsIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/ethnicities/': {
      id: '/_authenticated/profile-values/ethnicities/'
      path: '/ethnicities'
      fullPath: '/profile-values/ethnicities'
      preLoaderRoute: typeof AuthenticatedProfileValuesEthnicitiesIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/drinking-habits/': {
      id: '/_authenticated/profile-values/drinking-habits/'
      path: '/drinking-habits'
      fullPath: '/profile-values/drinking-habits'
      preLoaderRoute: typeof AuthenticatedProfileValuesDrinkingHabitsIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/body-types/': {
      id: '/_authenticated/profile-values/body-types/'
      path: '/body-types'
      fullPath: '/profile-values/body-types'
      preLoaderRoute: typeof AuthenticatedProfileValuesBodyTypesIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/body-arts/': {
      id: '/_authenticated/profile-values/body-arts/'
      path: '/body-arts'
      fullPath: '/profile-values/body-arts'
      preLoaderRoute: typeof AuthenticatedProfileValuesBodyArtsIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/best-features/': {
      id: '/_authenticated/profile-values/best-features/'
      path: '/best-features'
      fullPath: '/profile-values/best-features'
      preLoaderRoute: typeof AuthenticatedProfileValuesBestFeaturesIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/appearances/': {
      id: '/_authenticated/profile-values/appearances/'
      path: '/appearances'
      fullPath: '/profile-values/appearances'
      preLoaderRoute: typeof AuthenticatedProfileValuesAppearancesIndexRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/smilies/update/$msgId': {
      id: '/_authenticated/smilies/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/smilies/update/$msgId'
      preLoaderRoute: typeof AuthenticatedSmiliesUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedSmiliesRouteRoute
    }
    '/_authenticated/sessions/chat-mod/$conversationId': {
      id: '/_authenticated/sessions/chat-mod/$conversationId'
      path: '/chat-mod/$conversationId'
      fullPath: '/sessions/chat-mod/$conversationId'
      preLoaderRoute: typeof AuthenticatedSessionsChatModConversationIdRouteImport
      parentRoute: typeof AuthenticatedSessionsRouteRoute
    }
    '/_authenticated/profile-values/star-signs/add': {
      id: '/_authenticated/profile-values/star-signs/add'
      path: '/star-signs/add'
      fullPath: '/profile-values/star-signs/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesStarSignsAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/smoking-habits/add': {
      id: '/_authenticated/profile-values/smoking-habits/add'
      path: '/smoking-habits/add'
      fullPath: '/profile-values/smoking-habits/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesSmokingHabitsAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/sexual-orientations/add': {
      id: '/_authenticated/profile-values/sexual-orientations/add'
      path: '/sexual-orientations/add'
      fullPath: '/profile-values/sexual-orientations/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesSexualOrientationsAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/religions/add': {
      id: '/_authenticated/profile-values/religions/add'
      path: '/religions/add'
      fullPath: '/profile-values/religions/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesReligionsAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/relationship-status/add': {
      id: '/_authenticated/profile-values/relationship-status/add'
      path: '/relationship-status/add'
      fullPath: '/profile-values/relationship-status/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesRelationshipStatusAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/personalities/add': {
      id: '/_authenticated/profile-values/personalities/add'
      path: '/personalities/add'
      fullPath: '/profile-values/personalities/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesPersonalitiesAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/interests/add': {
      id: '/_authenticated/profile-values/interests/add'
      path: '/interests/add'
      fullPath: '/profile-values/interests/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesInterestsAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/hair-colors/add': {
      id: '/_authenticated/profile-values/hair-colors/add'
      path: '/hair-colors/add'
      fullPath: '/profile-values/hair-colors/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesHairColorsAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/eye-colors/add': {
      id: '/_authenticated/profile-values/eye-colors/add'
      path: '/eye-colors/add'
      fullPath: '/profile-values/eye-colors/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesEyeColorsAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/ethnicities/add': {
      id: '/_authenticated/profile-values/ethnicities/add'
      path: '/ethnicities/add'
      fullPath: '/profile-values/ethnicities/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesEthnicitiesAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/drinking-habits/add': {
      id: '/_authenticated/profile-values/drinking-habits/add'
      path: '/drinking-habits/add'
      fullPath: '/profile-values/drinking-habits/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesDrinkingHabitsAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/body-types/add': {
      id: '/_authenticated/profile-values/body-types/add'
      path: '/body-types/add'
      fullPath: '/profile-values/body-types/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesBodyTypesAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/body-arts/add': {
      id: '/_authenticated/profile-values/body-arts/add'
      path: '/body-arts/add'
      fullPath: '/profile-values/body-arts/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesBodyArtsAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/best-features/add': {
      id: '/_authenticated/profile-values/best-features/add'
      path: '/best-features/add'
      fullPath: '/profile-values/best-features/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesBestFeaturesAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/appearances/add': {
      id: '/_authenticated/profile-values/appearances/add'
      path: '/appearances/add'
      fullPath: '/profile-values/appearances/add'
      preLoaderRoute: typeof AuthenticatedProfileValuesAppearancesAddRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/packages/update/$msgId': {
      id: '/_authenticated/packages/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/packages/update/$msgId'
      preLoaderRoute: typeof AuthenticatedPackagesUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedPackagesRouteRoute
    }
    '/_authenticated/moderators/update-moderator/$moderatorId': {
      id: '/_authenticated/moderators/update-moderator/$moderatorId'
      path: '/update-moderator/$moderatorId'
      fullPath: '/moderators/update-moderator/$moderatorId'
      preLoaderRoute: typeof AuthenticatedModeratorsUpdateModeratorModeratorIdRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/moderators/profile/$moderatorId': {
      id: '/_authenticated/moderators/profile/$moderatorId'
      path: '/profile/$moderatorId'
      fullPath: '/moderators/profile/$moderatorId'
      preLoaderRoute: typeof AuthenticatedModeratorsProfileModeratorIdRouteImport
      parentRoute: typeof AuthenticatedModeratorsRouteRoute
    }
    '/_authenticated/models/update-model/$modelId': {
      id: '/_authenticated/models/update-model/$modelId'
      path: '/update-model/$modelId'
      fullPath: '/models/update-model/$modelId'
      preLoaderRoute: typeof AuthenticatedModelsUpdateModelModelIdRouteImport
      parentRoute: typeof AuthenticatedModelsRouteRoute
    }
    '/_authenticated/models/profile/$modelId': {
      id: '/_authenticated/models/profile/$modelId'
      path: '/profile/$modelId'
      fullPath: '/models/profile/$modelId'
      preLoaderRoute: typeof AuthenticatedModelsProfileModelIdRouteImport
      parentRoute: typeof AuthenticatedModelsRouteRoute
    }
    '/_authenticated/models/profile-images/$modelId': {
      id: '/_authenticated/models/profile-images/$modelId'
      path: '/profile-images/$modelId'
      fullPath: '/models/profile-images/$modelId'
      preLoaderRoute: typeof AuthenticatedModelsProfileImagesModelIdRouteImport
      parentRoute: typeof AuthenticatedModelsRouteRoute
    }
    '/_authenticated/members/profile/$memberId': {
      id: '/_authenticated/members/profile/$memberId'
      path: '/profile/$memberId'
      fullPath: '/members/profile/$memberId'
      preLoaderRoute: typeof AuthenticatedMembersProfileMemberIdRouteImport
      parentRoute: typeof AuthenticatedMembersRouteRoute
    }
    '/_authenticated/gifts/update/$msgId': {
      id: '/_authenticated/gifts/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/gifts/update/$msgId'
      preLoaderRoute: typeof AuthenticatedGiftsUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedGiftsRouteRoute
    }
    '/_authenticated/gifs/update/$msgId': {
      id: '/_authenticated/gifs/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/gifs/update/$msgId'
      preLoaderRoute: typeof AuthenticatedGifsUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedGifsRouteRoute
    }
    '/_authenticated/flirt-messages/update/$msgId': {
      id: '/_authenticated/flirt-messages/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/flirt-messages/update/$msgId'
      preLoaderRoute: typeof AuthenticatedFlirtMessagesUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedFlirtMessagesRouteRoute
    }
    '/_authenticated/bot-messages/update/$msgId': {
      id: '/_authenticated/bot-messages/update/$msgId'
      path: '/update/$msgId'
      fullPath: '/bot-messages/update/$msgId'
      preLoaderRoute: typeof AuthenticatedBotMessagesUpdateMsgIdRouteImport
      parentRoute: typeof AuthenticatedBotMessagesRouteRoute
    }
    '/_authenticated/profile-values/star-signs/update/$starsignsId': {
      id: '/_authenticated/profile-values/star-signs/update/$starsignsId'
      path: '/star-signs/update/$starsignsId'
      fullPath: '/profile-values/star-signs/update/$starsignsId'
      preLoaderRoute: typeof AuthenticatedProfileValuesStarSignsUpdateStarsignsIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/smoking-habits/update/$smokinghabitsId': {
      id: '/_authenticated/profile-values/smoking-habits/update/$smokinghabitsId'
      path: '/smoking-habits/update/$smokinghabitsId'
      fullPath: '/profile-values/smoking-habits/update/$smokinghabitsId'
      preLoaderRoute: typeof AuthenticatedProfileValuesSmokingHabitsUpdateSmokinghabitsIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/sexual-orientations/update/$sexualorientationsId': {
      id: '/_authenticated/profile-values/sexual-orientations/update/$sexualorientationsId'
      path: '/sexual-orientations/update/$sexualorientationsId'
      fullPath: '/profile-values/sexual-orientations/update/$sexualorientationsId'
      preLoaderRoute: typeof AuthenticatedProfileValuesSexualOrientationsUpdateSexualorientationsIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/religions/update/$religionsId': {
      id: '/_authenticated/profile-values/religions/update/$religionsId'
      path: '/religions/update/$religionsId'
      fullPath: '/profile-values/religions/update/$religionsId'
      preLoaderRoute: typeof AuthenticatedProfileValuesReligionsUpdateReligionsIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/relationship-status/update/$relationshipStatusId': {
      id: '/_authenticated/profile-values/relationship-status/update/$relationshipStatusId'
      path: '/relationship-status/update/$relationshipStatusId'
      fullPath: '/profile-values/relationship-status/update/$relationshipStatusId'
      preLoaderRoute: typeof AuthenticatedProfileValuesRelationshipStatusUpdateRelationshipStatusIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/personalities/update/$personalityId': {
      id: '/_authenticated/profile-values/personalities/update/$personalityId'
      path: '/personalities/update/$personalityId'
      fullPath: '/profile-values/personalities/update/$personalityId'
      preLoaderRoute: typeof AuthenticatedProfileValuesPersonalitiesUpdatePersonalityIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/interests/update/$interestsId': {
      id: '/_authenticated/profile-values/interests/update/$interestsId'
      path: '/interests/update/$interestsId'
      fullPath: '/profile-values/interests/update/$interestsId'
      preLoaderRoute: typeof AuthenticatedProfileValuesInterestsUpdateInterestsIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/hair-colors/update/$haircolorsId': {
      id: '/_authenticated/profile-values/hair-colors/update/$haircolorsId'
      path: '/hair-colors/update/$haircolorsId'
      fullPath: '/profile-values/hair-colors/update/$haircolorsId'
      preLoaderRoute: typeof AuthenticatedProfileValuesHairColorsUpdateHaircolorsIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/eye-colors/update/$eyecolorsId': {
      id: '/_authenticated/profile-values/eye-colors/update/$eyecolorsId'
      path: '/eye-colors/update/$eyecolorsId'
      fullPath: '/profile-values/eye-colors/update/$eyecolorsId'
      preLoaderRoute: typeof AuthenticatedProfileValuesEyeColorsUpdateEyecolorsIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/ethnicities/update/$ethnicitiesId': {
      id: '/_authenticated/profile-values/ethnicities/update/$ethnicitiesId'
      path: '/ethnicities/update/$ethnicitiesId'
      fullPath: '/profile-values/ethnicities/update/$ethnicitiesId'
      preLoaderRoute: typeof AuthenticatedProfileValuesEthnicitiesUpdateEthnicitiesIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/drinking-habits/update/$drinkinghabitsId': {
      id: '/_authenticated/profile-values/drinking-habits/update/$drinkinghabitsId'
      path: '/drinking-habits/update/$drinkinghabitsId'
      fullPath: '/profile-values/drinking-habits/update/$drinkinghabitsId'
      preLoaderRoute: typeof AuthenticatedProfileValuesDrinkingHabitsUpdateDrinkinghabitsIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/body-types/update/$bodytypesId': {
      id: '/_authenticated/profile-values/body-types/update/$bodytypesId'
      path: '/body-types/update/$bodytypesId'
      fullPath: '/profile-values/body-types/update/$bodytypesId'
      preLoaderRoute: typeof AuthenticatedProfileValuesBodyTypesUpdateBodytypesIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/body-arts/update/$bodyartsId': {
      id: '/_authenticated/profile-values/body-arts/update/$bodyartsId'
      path: '/body-arts/update/$bodyartsId'
      fullPath: '/profile-values/body-arts/update/$bodyartsId'
      preLoaderRoute: typeof AuthenticatedProfileValuesBodyArtsUpdateBodyartsIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/best-features/update/$bestfeaturesId': {
      id: '/_authenticated/profile-values/best-features/update/$bestfeaturesId'
      path: '/best-features/update/$bestfeaturesId'
      fullPath: '/profile-values/best-features/update/$bestfeaturesId'
      preLoaderRoute: typeof AuthenticatedProfileValuesBestFeaturesUpdateBestfeaturesIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/profile-values/appearances/update/$appearancesId': {
      id: '/_authenticated/profile-values/appearances/update/$appearancesId'
      path: '/appearances/update/$appearancesId'
      fullPath: '/profile-values/appearances/update/$appearancesId'
      preLoaderRoute: typeof AuthenticatedProfileValuesAppearancesUpdateAppearancesIdRouteImport
      parentRoute: typeof AuthenticatedProfileValuesRouteRoute
    }
    '/_authenticated/members/profile/$memberId/pictures': {
      id: '/_authenticated/members/profile/$memberId/pictures'
      path: '/pictures'
      fullPath: '/members/profile/$memberId/pictures'
      preLoaderRoute: typeof AuthenticatedMembersProfileMemberIdPicturesRouteImport
      parentRoute: typeof AuthenticatedMembersProfileMemberIdRoute
    }
    '/_authenticated/members/profile/$memberId/messages': {
      id: '/_authenticated/members/profile/$memberId/messages'
      path: '/messages'
      fullPath: '/members/profile/$memberId/messages'
      preLoaderRoute: typeof AuthenticatedMembersProfileMemberIdMessagesRouteImport
      parentRoute: typeof AuthenticatedMembersProfileMemberIdRoute
    }
    '/_authenticated/members/profile/$memberId/credits': {
      id: '/_authenticated/members/profile/$memberId/credits'
      path: '/credits'
      fullPath: '/members/profile/$memberId/credits'
      preLoaderRoute: typeof AuthenticatedMembersProfileMemberIdCreditsRouteImport
      parentRoute: typeof AuthenticatedMembersProfileMemberIdRoute
    }
    '/_authenticated/members/profile/$memberId/messages/$conversationId': {
      id: '/_authenticated/members/profile/$memberId/messages/$conversationId'
      path: '/$conversationId'
      fullPath: '/members/profile/$memberId/messages/$conversationId'
      preLoaderRoute: typeof AuthenticatedMembersProfileMemberIdMessagesConversationIdRouteImport
      parentRoute: typeof AuthenticatedMembersProfileMemberIdMessagesRoute
    }
  }
}

interface AuthenticatedAffiliatesRouteRouteChildren {
  AuthenticatedAffiliatesApprovedRoute: typeof AuthenticatedAffiliatesApprovedRoute
  AuthenticatedAffiliatesCreateRoute: typeof AuthenticatedAffiliatesCreateRoute
  AuthenticatedAffiliatesNewRoute: typeof AuthenticatedAffiliatesNewRoute
  AuthenticatedAffiliatesIndexRoute: typeof AuthenticatedAffiliatesIndexRoute
}

const AuthenticatedAffiliatesRouteRouteChildren: AuthenticatedAffiliatesRouteRouteChildren =
  {
    AuthenticatedAffiliatesApprovedRoute: AuthenticatedAffiliatesApprovedRoute,
    AuthenticatedAffiliatesCreateRoute: AuthenticatedAffiliatesCreateRoute,
    AuthenticatedAffiliatesNewRoute: AuthenticatedAffiliatesNewRoute,
    AuthenticatedAffiliatesIndexRoute: AuthenticatedAffiliatesIndexRoute,
  }

const AuthenticatedAffiliatesRouteRouteWithChildren =
  AuthenticatedAffiliatesRouteRoute._addFileChildren(
    AuthenticatedAffiliatesRouteRouteChildren,
  )

interface AuthenticatedAffiliatesOffersRouteRouteChildren {
  AuthenticatedAffiliatesOffersAddRoute: typeof AuthenticatedAffiliatesOffersAddRoute
  AuthenticatedAffiliatesOffersIndexRoute: typeof AuthenticatedAffiliatesOffersIndexRoute
}

const AuthenticatedAffiliatesOffersRouteRouteChildren: AuthenticatedAffiliatesOffersRouteRouteChildren =
  {
    AuthenticatedAffiliatesOffersAddRoute:
      AuthenticatedAffiliatesOffersAddRoute,
    AuthenticatedAffiliatesOffersIndexRoute:
      AuthenticatedAffiliatesOffersIndexRoute,
  }

const AuthenticatedAffiliatesOffersRouteRouteWithChildren =
  AuthenticatedAffiliatesOffersRouteRoute._addFileChildren(
    AuthenticatedAffiliatesOffersRouteRouteChildren,
  )

interface AuthenticatedAnnouncementsRouteRouteChildren {
  AuthenticatedAnnouncementsAffiliatesRoute: typeof AuthenticatedAnnouncementsAffiliatesRoute
  AuthenticatedAnnouncementsModeratorsRoute: typeof AuthenticatedAnnouncementsModeratorsRoute
  AuthenticatedAnnouncementsWhiteLabelsRoute: typeof AuthenticatedAnnouncementsWhiteLabelsRoute
  AuthenticatedAnnouncementsIndexRoute: typeof AuthenticatedAnnouncementsIndexRoute
}

const AuthenticatedAnnouncementsRouteRouteChildren: AuthenticatedAnnouncementsRouteRouteChildren =
  {
    AuthenticatedAnnouncementsAffiliatesRoute:
      AuthenticatedAnnouncementsAffiliatesRoute,
    AuthenticatedAnnouncementsModeratorsRoute:
      AuthenticatedAnnouncementsModeratorsRoute,
    AuthenticatedAnnouncementsWhiteLabelsRoute:
      AuthenticatedAnnouncementsWhiteLabelsRoute,
    AuthenticatedAnnouncementsIndexRoute: AuthenticatedAnnouncementsIndexRoute,
  }

const AuthenticatedAnnouncementsRouteRouteWithChildren =
  AuthenticatedAnnouncementsRouteRoute._addFileChildren(
    AuthenticatedAnnouncementsRouteRouteChildren,
  )

interface AuthenticatedBotMessagesRouteRouteChildren {
  AuthenticatedBotMessagesAddRoute: typeof AuthenticatedBotMessagesAddRoute
  AuthenticatedBotMessagesIndexRoute: typeof AuthenticatedBotMessagesIndexRoute
  AuthenticatedBotMessagesUpdateMsgIdRoute: typeof AuthenticatedBotMessagesUpdateMsgIdRoute
}

const AuthenticatedBotMessagesRouteRouteChildren: AuthenticatedBotMessagesRouteRouteChildren =
  {
    AuthenticatedBotMessagesAddRoute: AuthenticatedBotMessagesAddRoute,
    AuthenticatedBotMessagesIndexRoute: AuthenticatedBotMessagesIndexRoute,
    AuthenticatedBotMessagesUpdateMsgIdRoute:
      AuthenticatedBotMessagesUpdateMsgIdRoute,
  }

const AuthenticatedBotMessagesRouteRouteWithChildren =
  AuthenticatedBotMessagesRouteRoute._addFileChildren(
    AuthenticatedBotMessagesRouteRouteChildren,
  )

interface AuthenticatedCurrenciesRouteRouteChildren {
  AuthenticatedCurrenciesIndexRoute: typeof AuthenticatedCurrenciesIndexRoute
}

const AuthenticatedCurrenciesRouteRouteChildren: AuthenticatedCurrenciesRouteRouteChildren =
  {
    AuthenticatedCurrenciesIndexRoute: AuthenticatedCurrenciesIndexRoute,
  }

const AuthenticatedCurrenciesRouteRouteWithChildren =
  AuthenticatedCurrenciesRouteRoute._addFileChildren(
    AuthenticatedCurrenciesRouteRouteChildren,
  )

interface AuthenticatedFlirtMessagesRouteRouteChildren {
  AuthenticatedFlirtMessagesAddRoute: typeof AuthenticatedFlirtMessagesAddRoute
  AuthenticatedFlirtMessagesIndexRoute: typeof AuthenticatedFlirtMessagesIndexRoute
  AuthenticatedFlirtMessagesUpdateMsgIdRoute: typeof AuthenticatedFlirtMessagesUpdateMsgIdRoute
}

const AuthenticatedFlirtMessagesRouteRouteChildren: AuthenticatedFlirtMessagesRouteRouteChildren =
  {
    AuthenticatedFlirtMessagesAddRoute: AuthenticatedFlirtMessagesAddRoute,
    AuthenticatedFlirtMessagesIndexRoute: AuthenticatedFlirtMessagesIndexRoute,
    AuthenticatedFlirtMessagesUpdateMsgIdRoute:
      AuthenticatedFlirtMessagesUpdateMsgIdRoute,
  }

const AuthenticatedFlirtMessagesRouteRouteWithChildren =
  AuthenticatedFlirtMessagesRouteRoute._addFileChildren(
    AuthenticatedFlirtMessagesRouteRouteChildren,
  )

interface AuthenticatedGifsRouteRouteChildren {
  AuthenticatedGifsAddRoute: typeof AuthenticatedGifsAddRoute
  AuthenticatedGifsIndexRoute: typeof AuthenticatedGifsIndexRoute
  AuthenticatedGifsUpdateMsgIdRoute: typeof AuthenticatedGifsUpdateMsgIdRoute
}

const AuthenticatedGifsRouteRouteChildren: AuthenticatedGifsRouteRouteChildren =
  {
    AuthenticatedGifsAddRoute: AuthenticatedGifsAddRoute,
    AuthenticatedGifsIndexRoute: AuthenticatedGifsIndexRoute,
    AuthenticatedGifsUpdateMsgIdRoute: AuthenticatedGifsUpdateMsgIdRoute,
  }

const AuthenticatedGifsRouteRouteWithChildren =
  AuthenticatedGifsRouteRoute._addFileChildren(
    AuthenticatedGifsRouteRouteChildren,
  )

interface AuthenticatedGiftsRouteRouteChildren {
  AuthenticatedGiftsAddRoute: typeof AuthenticatedGiftsAddRoute
  AuthenticatedGiftsIndexRoute: typeof AuthenticatedGiftsIndexRoute
  AuthenticatedGiftsUpdateMsgIdRoute: typeof AuthenticatedGiftsUpdateMsgIdRoute
}

const AuthenticatedGiftsRouteRouteChildren: AuthenticatedGiftsRouteRouteChildren =
  {
    AuthenticatedGiftsAddRoute: AuthenticatedGiftsAddRoute,
    AuthenticatedGiftsIndexRoute: AuthenticatedGiftsIndexRoute,
    AuthenticatedGiftsUpdateMsgIdRoute: AuthenticatedGiftsUpdateMsgIdRoute,
  }

const AuthenticatedGiftsRouteRouteWithChildren =
  AuthenticatedGiftsRouteRoute._addFileChildren(
    AuthenticatedGiftsRouteRouteChildren,
  )

interface AuthenticatedMembersProfileMemberIdMessagesRouteChildren {
  AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute: typeof AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute
}

const AuthenticatedMembersProfileMemberIdMessagesRouteChildren: AuthenticatedMembersProfileMemberIdMessagesRouteChildren =
  {
    AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute:
      AuthenticatedMembersProfileMemberIdMessagesConversationIdRoute,
  }

const AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren =
  AuthenticatedMembersProfileMemberIdMessagesRoute._addFileChildren(
    AuthenticatedMembersProfileMemberIdMessagesRouteChildren,
  )

interface AuthenticatedMembersProfileMemberIdRouteChildren {
  AuthenticatedMembersProfileMemberIdCreditsRoute: typeof AuthenticatedMembersProfileMemberIdCreditsRoute
  AuthenticatedMembersProfileMemberIdMessagesRoute: typeof AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren
  AuthenticatedMembersProfileMemberIdPicturesRoute: typeof AuthenticatedMembersProfileMemberIdPicturesRoute
}

const AuthenticatedMembersProfileMemberIdRouteChildren: AuthenticatedMembersProfileMemberIdRouteChildren =
  {
    AuthenticatedMembersProfileMemberIdCreditsRoute:
      AuthenticatedMembersProfileMemberIdCreditsRoute,
    AuthenticatedMembersProfileMemberIdMessagesRoute:
      AuthenticatedMembersProfileMemberIdMessagesRouteWithChildren,
    AuthenticatedMembersProfileMemberIdPicturesRoute:
      AuthenticatedMembersProfileMemberIdPicturesRoute,
  }

const AuthenticatedMembersProfileMemberIdRouteWithChildren =
  AuthenticatedMembersProfileMemberIdRoute._addFileChildren(
    AuthenticatedMembersProfileMemberIdRouteChildren,
  )

interface AuthenticatedMembersRouteRouteChildren {
  AuthenticatedMembersIndexRoute: typeof AuthenticatedMembersIndexRoute
  AuthenticatedMembersProfileMemberIdRoute: typeof AuthenticatedMembersProfileMemberIdRouteWithChildren
}

const AuthenticatedMembersRouteRouteChildren: AuthenticatedMembersRouteRouteChildren =
  {
    AuthenticatedMembersIndexRoute: AuthenticatedMembersIndexRoute,
    AuthenticatedMembersProfileMemberIdRoute:
      AuthenticatedMembersProfileMemberIdRouteWithChildren,
  }

const AuthenticatedMembersRouteRouteWithChildren =
  AuthenticatedMembersRouteRoute._addFileChildren(
    AuthenticatedMembersRouteRouteChildren,
  )

interface AuthenticatedModelsRouteRouteChildren {
  AuthenticatedModelsCreateModelRoute: typeof AuthenticatedModelsCreateModelRoute
  AuthenticatedModelsIndexRoute: typeof AuthenticatedModelsIndexRoute
  AuthenticatedModelsProfileImagesModelIdRoute: typeof AuthenticatedModelsProfileImagesModelIdRoute
  AuthenticatedModelsProfileModelIdRoute: typeof AuthenticatedModelsProfileModelIdRoute
  AuthenticatedModelsUpdateModelModelIdRoute: typeof AuthenticatedModelsUpdateModelModelIdRoute
}

const AuthenticatedModelsRouteRouteChildren: AuthenticatedModelsRouteRouteChildren =
  {
    AuthenticatedModelsCreateModelRoute: AuthenticatedModelsCreateModelRoute,
    AuthenticatedModelsIndexRoute: AuthenticatedModelsIndexRoute,
    AuthenticatedModelsProfileImagesModelIdRoute:
      AuthenticatedModelsProfileImagesModelIdRoute,
    AuthenticatedModelsProfileModelIdRoute:
      AuthenticatedModelsProfileModelIdRoute,
    AuthenticatedModelsUpdateModelModelIdRoute:
      AuthenticatedModelsUpdateModelModelIdRoute,
  }

const AuthenticatedModelsRouteRouteWithChildren =
  AuthenticatedModelsRouteRoute._addFileChildren(
    AuthenticatedModelsRouteRouteChildren,
  )

interface AuthenticatedModeratorsRouteRouteChildren {
  AuthenticatedModeratorsChatGroupRoute: typeof AuthenticatedModeratorsChatGroupRoute
  AuthenticatedModeratorsCreateModeratorRoute: typeof AuthenticatedModeratorsCreateModeratorRoute
  AuthenticatedModeratorsDomainRoute: typeof AuthenticatedModeratorsDomainRoute
  AuthenticatedModeratorsLoginActivityRoute: typeof AuthenticatedModeratorsLoginActivityRoute
  AuthenticatedModeratorsReplyMessagesRoute: typeof AuthenticatedModeratorsReplyMessagesRoute
  AuthenticatedModeratorsIndexRoute: typeof AuthenticatedModeratorsIndexRoute
  AuthenticatedModeratorsProfileModeratorIdRoute: typeof AuthenticatedModeratorsProfileModeratorIdRoute
  AuthenticatedModeratorsUpdateModeratorModeratorIdRoute: typeof AuthenticatedModeratorsUpdateModeratorModeratorIdRoute
}

const AuthenticatedModeratorsRouteRouteChildren: AuthenticatedModeratorsRouteRouteChildren =
  {
    AuthenticatedModeratorsChatGroupRoute:
      AuthenticatedModeratorsChatGroupRoute,
    AuthenticatedModeratorsCreateModeratorRoute:
      AuthenticatedModeratorsCreateModeratorRoute,
    AuthenticatedModeratorsDomainRoute: AuthenticatedModeratorsDomainRoute,
    AuthenticatedModeratorsLoginActivityRoute:
      AuthenticatedModeratorsLoginActivityRoute,
    AuthenticatedModeratorsReplyMessagesRoute:
      AuthenticatedModeratorsReplyMessagesRoute,
    AuthenticatedModeratorsIndexRoute: AuthenticatedModeratorsIndexRoute,
    AuthenticatedModeratorsProfileModeratorIdRoute:
      AuthenticatedModeratorsProfileModeratorIdRoute,
    AuthenticatedModeratorsUpdateModeratorModeratorIdRoute:
      AuthenticatedModeratorsUpdateModeratorModeratorIdRoute,
  }

const AuthenticatedModeratorsRouteRouteWithChildren =
  AuthenticatedModeratorsRouteRoute._addFileChildren(
    AuthenticatedModeratorsRouteRouteChildren,
  )

interface AuthenticatedPackagesRouteRouteChildren {
  AuthenticatedPackagesAddRoute: typeof AuthenticatedPackagesAddRoute
  AuthenticatedPackagesIndexRoute: typeof AuthenticatedPackagesIndexRoute
  AuthenticatedPackagesUpdateMsgIdRoute: typeof AuthenticatedPackagesUpdateMsgIdRoute
}

const AuthenticatedPackagesRouteRouteChildren: AuthenticatedPackagesRouteRouteChildren =
  {
    AuthenticatedPackagesAddRoute: AuthenticatedPackagesAddRoute,
    AuthenticatedPackagesIndexRoute: AuthenticatedPackagesIndexRoute,
    AuthenticatedPackagesUpdateMsgIdRoute:
      AuthenticatedPackagesUpdateMsgIdRoute,
  }

const AuthenticatedPackagesRouteRouteWithChildren =
  AuthenticatedPackagesRouteRoute._addFileChildren(
    AuthenticatedPackagesRouteRouteChildren,
  )

interface AuthenticatedProfileValuesRouteRouteChildren {
  AuthenticatedProfileValuesIndexRoute: typeof AuthenticatedProfileValuesIndexRoute
  AuthenticatedProfileValuesAppearancesAddRoute: typeof AuthenticatedProfileValuesAppearancesAddRoute
  AuthenticatedProfileValuesBestFeaturesAddRoute: typeof AuthenticatedProfileValuesBestFeaturesAddRoute
  AuthenticatedProfileValuesBodyArtsAddRoute: typeof AuthenticatedProfileValuesBodyArtsAddRoute
  AuthenticatedProfileValuesBodyTypesAddRoute: typeof AuthenticatedProfileValuesBodyTypesAddRoute
  AuthenticatedProfileValuesDrinkingHabitsAddRoute: typeof AuthenticatedProfileValuesDrinkingHabitsAddRoute
  AuthenticatedProfileValuesEthnicitiesAddRoute: typeof AuthenticatedProfileValuesEthnicitiesAddRoute
  AuthenticatedProfileValuesEyeColorsAddRoute: typeof AuthenticatedProfileValuesEyeColorsAddRoute
  AuthenticatedProfileValuesHairColorsAddRoute: typeof AuthenticatedProfileValuesHairColorsAddRoute
  AuthenticatedProfileValuesInterestsAddRoute: typeof AuthenticatedProfileValuesInterestsAddRoute
  AuthenticatedProfileValuesPersonalitiesAddRoute: typeof AuthenticatedProfileValuesPersonalitiesAddRoute
  AuthenticatedProfileValuesRelationshipStatusAddRoute: typeof AuthenticatedProfileValuesRelationshipStatusAddRoute
  AuthenticatedProfileValuesReligionsAddRoute: typeof AuthenticatedProfileValuesReligionsAddRoute
  AuthenticatedProfileValuesSexualOrientationsAddRoute: typeof AuthenticatedProfileValuesSexualOrientationsAddRoute
  AuthenticatedProfileValuesSmokingHabitsAddRoute: typeof AuthenticatedProfileValuesSmokingHabitsAddRoute
  AuthenticatedProfileValuesStarSignsAddRoute: typeof AuthenticatedProfileValuesStarSignsAddRoute
  AuthenticatedProfileValuesAppearancesIndexRoute: typeof AuthenticatedProfileValuesAppearancesIndexRoute
  AuthenticatedProfileValuesBestFeaturesIndexRoute: typeof AuthenticatedProfileValuesBestFeaturesIndexRoute
  AuthenticatedProfileValuesBodyArtsIndexRoute: typeof AuthenticatedProfileValuesBodyArtsIndexRoute
  AuthenticatedProfileValuesBodyTypesIndexRoute: typeof AuthenticatedProfileValuesBodyTypesIndexRoute
  AuthenticatedProfileValuesDrinkingHabitsIndexRoute: typeof AuthenticatedProfileValuesDrinkingHabitsIndexRoute
  AuthenticatedProfileValuesEthnicitiesIndexRoute: typeof AuthenticatedProfileValuesEthnicitiesIndexRoute
  AuthenticatedProfileValuesEyeColorsIndexRoute: typeof AuthenticatedProfileValuesEyeColorsIndexRoute
  AuthenticatedProfileValuesHairColorsIndexRoute: typeof AuthenticatedProfileValuesHairColorsIndexRoute
  AuthenticatedProfileValuesInterestsIndexRoute: typeof AuthenticatedProfileValuesInterestsIndexRoute
  AuthenticatedProfileValuesPersonalitiesIndexRoute: typeof AuthenticatedProfileValuesPersonalitiesIndexRoute
  AuthenticatedProfileValuesRelationshipStatusIndexRoute: typeof AuthenticatedProfileValuesRelationshipStatusIndexRoute
  AuthenticatedProfileValuesReligionsIndexRoute: typeof AuthenticatedProfileValuesReligionsIndexRoute
  AuthenticatedProfileValuesSexualOrientationsIndexRoute: typeof AuthenticatedProfileValuesSexualOrientationsIndexRoute
  AuthenticatedProfileValuesSmokingHabitsIndexRoute: typeof AuthenticatedProfileValuesSmokingHabitsIndexRoute
  AuthenticatedProfileValuesStarSignsIndexRoute: typeof AuthenticatedProfileValuesStarSignsIndexRoute
  AuthenticatedProfileValuesAppearancesUpdateAppearancesIdRoute: typeof AuthenticatedProfileValuesAppearancesUpdateAppearancesIdRoute
  AuthenticatedProfileValuesBestFeaturesUpdateBestfeaturesIdRoute: typeof AuthenticatedProfileValuesBestFeaturesUpdateBestfeaturesIdRoute
  AuthenticatedProfileValuesBodyArtsUpdateBodyartsIdRoute: typeof AuthenticatedProfileValuesBodyArtsUpdateBodyartsIdRoute
  AuthenticatedProfileValuesBodyTypesUpdateBodytypesIdRoute: typeof AuthenticatedProfileValuesBodyTypesUpdateBodytypesIdRoute
  AuthenticatedProfileValuesDrinkingHabitsUpdateDrinkinghabitsIdRoute: typeof AuthenticatedProfileValuesDrinkingHabitsUpdateDrinkinghabitsIdRoute
  AuthenticatedProfileValuesEthnicitiesUpdateEthnicitiesIdRoute: typeof AuthenticatedProfileValuesEthnicitiesUpdateEthnicitiesIdRoute
  AuthenticatedProfileValuesEyeColorsUpdateEyecolorsIdRoute: typeof AuthenticatedProfileValuesEyeColorsUpdateEyecolorsIdRoute
  AuthenticatedProfileValuesHairColorsUpdateHaircolorsIdRoute: typeof AuthenticatedProfileValuesHairColorsUpdateHaircolorsIdRoute
  AuthenticatedProfileValuesInterestsUpdateInterestsIdRoute: typeof AuthenticatedProfileValuesInterestsUpdateInterestsIdRoute
  AuthenticatedProfileValuesPersonalitiesUpdatePersonalityIdRoute: typeof AuthenticatedProfileValuesPersonalitiesUpdatePersonalityIdRoute
  AuthenticatedProfileValuesRelationshipStatusUpdateRelationshipStatusIdRoute: typeof AuthenticatedProfileValuesRelationshipStatusUpdateRelationshipStatusIdRoute
  AuthenticatedProfileValuesReligionsUpdateReligionsIdRoute: typeof AuthenticatedProfileValuesReligionsUpdateReligionsIdRoute
  AuthenticatedProfileValuesSexualOrientationsUpdateSexualorientationsIdRoute: typeof AuthenticatedProfileValuesSexualOrientationsUpdateSexualorientationsIdRoute
  AuthenticatedProfileValuesSmokingHabitsUpdateSmokinghabitsIdRoute: typeof AuthenticatedProfileValuesSmokingHabitsUpdateSmokinghabitsIdRoute
  AuthenticatedProfileValuesStarSignsUpdateStarsignsIdRoute: typeof AuthenticatedProfileValuesStarSignsUpdateStarsignsIdRoute
}

const AuthenticatedProfileValuesRouteRouteChildren: AuthenticatedProfileValuesRouteRouteChildren =
  {
    AuthenticatedProfileValuesIndexRoute: AuthenticatedProfileValuesIndexRoute,
    AuthenticatedProfileValuesAppearancesAddRoute:
      AuthenticatedProfileValuesAppearancesAddRoute,
    AuthenticatedProfileValuesBestFeaturesAddRoute:
      AuthenticatedProfileValuesBestFeaturesAddRoute,
    AuthenticatedProfileValuesBodyArtsAddRoute:
      AuthenticatedProfileValuesBodyArtsAddRoute,
    AuthenticatedProfileValuesBodyTypesAddRoute:
      AuthenticatedProfileValuesBodyTypesAddRoute,
    AuthenticatedProfileValuesDrinkingHabitsAddRoute:
      AuthenticatedProfileValuesDrinkingHabitsAddRoute,
    AuthenticatedProfileValuesEthnicitiesAddRoute:
      AuthenticatedProfileValuesEthnicitiesAddRoute,
    AuthenticatedProfileValuesEyeColorsAddRoute:
      AuthenticatedProfileValuesEyeColorsAddRoute,
    AuthenticatedProfileValuesHairColorsAddRoute:
      AuthenticatedProfileValuesHairColorsAddRoute,
    AuthenticatedProfileValuesInterestsAddRoute:
      AuthenticatedProfileValuesInterestsAddRoute,
    AuthenticatedProfileValuesPersonalitiesAddRoute:
      AuthenticatedProfileValuesPersonalitiesAddRoute,
    AuthenticatedProfileValuesRelationshipStatusAddRoute:
      AuthenticatedProfileValuesRelationshipStatusAddRoute,
    AuthenticatedProfileValuesReligionsAddRoute:
      AuthenticatedProfileValuesReligionsAddRoute,
    AuthenticatedProfileValuesSexualOrientationsAddRoute:
      AuthenticatedProfileValuesSexualOrientationsAddRoute,
    AuthenticatedProfileValuesSmokingHabitsAddRoute:
      AuthenticatedProfileValuesSmokingHabitsAddRoute,
    AuthenticatedProfileValuesStarSignsAddRoute:
      AuthenticatedProfileValuesStarSignsAddRoute,
    AuthenticatedProfileValuesAppearancesIndexRoute:
      AuthenticatedProfileValuesAppearancesIndexRoute,
    AuthenticatedProfileValuesBestFeaturesIndexRoute:
      AuthenticatedProfileValuesBestFeaturesIndexRoute,
    AuthenticatedProfileValuesBodyArtsIndexRoute:
      AuthenticatedProfileValuesBodyArtsIndexRoute,
    AuthenticatedProfileValuesBodyTypesIndexRoute:
      AuthenticatedProfileValuesBodyTypesIndexRoute,
    AuthenticatedProfileValuesDrinkingHabitsIndexRoute:
      AuthenticatedProfileValuesDrinkingHabitsIndexRoute,
    AuthenticatedProfileValuesEthnicitiesIndexRoute:
      AuthenticatedProfileValuesEthnicitiesIndexRoute,
    AuthenticatedProfileValuesEyeColorsIndexRoute:
      AuthenticatedProfileValuesEyeColorsIndexRoute,
    AuthenticatedProfileValuesHairColorsIndexRoute:
      AuthenticatedProfileValuesHairColorsIndexRoute,
    AuthenticatedProfileValuesInterestsIndexRoute:
      AuthenticatedProfileValuesInterestsIndexRoute,
    AuthenticatedProfileValuesPersonalitiesIndexRoute:
      AuthenticatedProfileValuesPersonalitiesIndexRoute,
    AuthenticatedProfileValuesRelationshipStatusIndexRoute:
      AuthenticatedProfileValuesRelationshipStatusIndexRoute,
    AuthenticatedProfileValuesReligionsIndexRoute:
      AuthenticatedProfileValuesReligionsIndexRoute,
    AuthenticatedProfileValuesSexualOrientationsIndexRoute:
      AuthenticatedProfileValuesSexualOrientationsIndexRoute,
    AuthenticatedProfileValuesSmokingHabitsIndexRoute:
      AuthenticatedProfileValuesSmokingHabitsIndexRoute,
    AuthenticatedProfileValuesStarSignsIndexRoute:
      AuthenticatedProfileValuesStarSignsIndexRoute,
    AuthenticatedProfileValuesAppearancesUpdateAppearancesIdRoute:
      AuthenticatedProfileValuesAppearancesUpdateAppearancesIdRoute,
    AuthenticatedProfileValuesBestFeaturesUpdateBestfeaturesIdRoute:
      AuthenticatedProfileValuesBestFeaturesUpdateBestfeaturesIdRoute,
    AuthenticatedProfileValuesBodyArtsUpdateBodyartsIdRoute:
      AuthenticatedProfileValuesBodyArtsUpdateBodyartsIdRoute,
    AuthenticatedProfileValuesBodyTypesUpdateBodytypesIdRoute:
      AuthenticatedProfileValuesBodyTypesUpdateBodytypesIdRoute,
    AuthenticatedProfileValuesDrinkingHabitsUpdateDrinkinghabitsIdRoute:
      AuthenticatedProfileValuesDrinkingHabitsUpdateDrinkinghabitsIdRoute,
    AuthenticatedProfileValuesEthnicitiesUpdateEthnicitiesIdRoute:
      AuthenticatedProfileValuesEthnicitiesUpdateEthnicitiesIdRoute,
    AuthenticatedProfileValuesEyeColorsUpdateEyecolorsIdRoute:
      AuthenticatedProfileValuesEyeColorsUpdateEyecolorsIdRoute,
    AuthenticatedProfileValuesHairColorsUpdateHaircolorsIdRoute:
      AuthenticatedProfileValuesHairColorsUpdateHaircolorsIdRoute,
    AuthenticatedProfileValuesInterestsUpdateInterestsIdRoute:
      AuthenticatedProfileValuesInterestsUpdateInterestsIdRoute,
    AuthenticatedProfileValuesPersonalitiesUpdatePersonalityIdRoute:
      AuthenticatedProfileValuesPersonalitiesUpdatePersonalityIdRoute,
    AuthenticatedProfileValuesRelationshipStatusUpdateRelationshipStatusIdRoute:
      AuthenticatedProfileValuesRelationshipStatusUpdateRelationshipStatusIdRoute,
    AuthenticatedProfileValuesReligionsUpdateReligionsIdRoute:
      AuthenticatedProfileValuesReligionsUpdateReligionsIdRoute,
    AuthenticatedProfileValuesSexualOrientationsUpdateSexualorientationsIdRoute:
      AuthenticatedProfileValuesSexualOrientationsUpdateSexualorientationsIdRoute,
    AuthenticatedProfileValuesSmokingHabitsUpdateSmokinghabitsIdRoute:
      AuthenticatedProfileValuesSmokingHabitsUpdateSmokinghabitsIdRoute,
    AuthenticatedProfileValuesStarSignsUpdateStarsignsIdRoute:
      AuthenticatedProfileValuesStarSignsUpdateStarsignsIdRoute,
  }

const AuthenticatedProfileValuesRouteRouteWithChildren =
  AuthenticatedProfileValuesRouteRoute._addFileChildren(
    AuthenticatedProfileValuesRouteRouteChildren,
  )

interface AuthenticatedSessionsRouteRouteChildren {
  AuthenticatedSessionsLobyRoute: typeof AuthenticatedSessionsLobyRoute
  AuthenticatedSessionsIndexRoute: typeof AuthenticatedSessionsIndexRoute
  AuthenticatedSessionsChatModConversationIdRoute: typeof AuthenticatedSessionsChatModConversationIdRoute
}

const AuthenticatedSessionsRouteRouteChildren: AuthenticatedSessionsRouteRouteChildren =
  {
    AuthenticatedSessionsLobyRoute: AuthenticatedSessionsLobyRoute,
    AuthenticatedSessionsIndexRoute: AuthenticatedSessionsIndexRoute,
    AuthenticatedSessionsChatModConversationIdRoute:
      AuthenticatedSessionsChatModConversationIdRoute,
  }

const AuthenticatedSessionsRouteRouteWithChildren =
  AuthenticatedSessionsRouteRoute._addFileChildren(
    AuthenticatedSessionsRouteRouteChildren,
  )

interface AuthenticatedSettingsRouteRouteChildren {
  AuthenticatedSettingsAccountRoute: typeof AuthenticatedSettingsAccountRoute
  AuthenticatedSettingsAppearanceRoute: typeof AuthenticatedSettingsAppearanceRoute
  AuthenticatedSettingsDisplayRoute: typeof AuthenticatedSettingsDisplayRoute
  AuthenticatedSettingsNotificationsRoute: typeof AuthenticatedSettingsNotificationsRoute
  AuthenticatedSettingsIndexRoute: typeof AuthenticatedSettingsIndexRoute
}

const AuthenticatedSettingsRouteRouteChildren: AuthenticatedSettingsRouteRouteChildren =
  {
    AuthenticatedSettingsAccountRoute: AuthenticatedSettingsAccountRoute,
    AuthenticatedSettingsAppearanceRoute: AuthenticatedSettingsAppearanceRoute,
    AuthenticatedSettingsDisplayRoute: AuthenticatedSettingsDisplayRoute,
    AuthenticatedSettingsNotificationsRoute:
      AuthenticatedSettingsNotificationsRoute,
    AuthenticatedSettingsIndexRoute: AuthenticatedSettingsIndexRoute,
  }

const AuthenticatedSettingsRouteRouteWithChildren =
  AuthenticatedSettingsRouteRoute._addFileChildren(
    AuthenticatedSettingsRouteRouteChildren,
  )

interface AuthenticatedSmiliesRouteRouteChildren {
  AuthenticatedSmiliesAddRoute: typeof AuthenticatedSmiliesAddRoute
  AuthenticatedSmiliesIndexRoute: typeof AuthenticatedSmiliesIndexRoute
  AuthenticatedSmiliesUpdateMsgIdRoute: typeof AuthenticatedSmiliesUpdateMsgIdRoute
}

const AuthenticatedSmiliesRouteRouteChildren: AuthenticatedSmiliesRouteRouteChildren =
  {
    AuthenticatedSmiliesAddRoute: AuthenticatedSmiliesAddRoute,
    AuthenticatedSmiliesIndexRoute: AuthenticatedSmiliesIndexRoute,
    AuthenticatedSmiliesUpdateMsgIdRoute: AuthenticatedSmiliesUpdateMsgIdRoute,
  }

const AuthenticatedSmiliesRouteRouteWithChildren =
  AuthenticatedSmiliesRouteRoute._addFileChildren(
    AuthenticatedSmiliesRouteRouteChildren,
  )

interface AuthenticatedRouteRouteChildren {
  AuthenticatedAffiliatesRouteRoute: typeof AuthenticatedAffiliatesRouteRouteWithChildren
  AuthenticatedAffiliatesOffersRouteRoute: typeof AuthenticatedAffiliatesOffersRouteRouteWithChildren
  AuthenticatedAnnouncementsRouteRoute: typeof AuthenticatedAnnouncementsRouteRouteWithChildren
  AuthenticatedBotMessagesRouteRoute: typeof AuthenticatedBotMessagesRouteRouteWithChildren
  AuthenticatedCurrenciesRouteRoute: typeof AuthenticatedCurrenciesRouteRouteWithChildren
  AuthenticatedFlirtMessagesRouteRoute: typeof AuthenticatedFlirtMessagesRouteRouteWithChildren
  AuthenticatedGifsRouteRoute: typeof AuthenticatedGifsRouteRouteWithChildren
  AuthenticatedGiftsRouteRoute: typeof AuthenticatedGiftsRouteRouteWithChildren
  AuthenticatedMembersRouteRoute: typeof AuthenticatedMembersRouteRouteWithChildren
  AuthenticatedModelsRouteRoute: typeof AuthenticatedModelsRouteRouteWithChildren
  AuthenticatedModeratorsRouteRoute: typeof AuthenticatedModeratorsRouteRouteWithChildren
  AuthenticatedPackagesRouteRoute: typeof AuthenticatedPackagesRouteRouteWithChildren
  AuthenticatedProfileValuesRouteRoute: typeof AuthenticatedProfileValuesRouteRouteWithChildren
  AuthenticatedSessionsRouteRoute: typeof AuthenticatedSessionsRouteRouteWithChildren
  AuthenticatedSettingsRouteRoute: typeof AuthenticatedSettingsRouteRouteWithChildren
  AuthenticatedSmiliesRouteRoute: typeof AuthenticatedSmiliesRouteRouteWithChildren
  AuthenticatedAdminLoginActivityRoute: typeof AuthenticatedAdminLoginActivityRoute
  AuthenticatedContactUsRoute: typeof AuthenticatedContactUsRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedChatsIndexRoute: typeof AuthenticatedChatsIndexRoute
  AuthenticatedHelpCenterIndexRoute: typeof AuthenticatedHelpCenterIndexRoute
  AuthenticatedUsersIndexRoute: typeof AuthenticatedUsersIndexRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedAffiliatesRouteRoute:
    AuthenticatedAffiliatesRouteRouteWithChildren,
  AuthenticatedAffiliatesOffersRouteRoute:
    AuthenticatedAffiliatesOffersRouteRouteWithChildren,
  AuthenticatedAnnouncementsRouteRoute:
    AuthenticatedAnnouncementsRouteRouteWithChildren,
  AuthenticatedBotMessagesRouteRoute:
    AuthenticatedBotMessagesRouteRouteWithChildren,
  AuthenticatedCurrenciesRouteRoute:
    AuthenticatedCurrenciesRouteRouteWithChildren,
  AuthenticatedFlirtMessagesRouteRoute:
    AuthenticatedFlirtMessagesRouteRouteWithChildren,
  AuthenticatedGifsRouteRoute: AuthenticatedGifsRouteRouteWithChildren,
  AuthenticatedGiftsRouteRoute: AuthenticatedGiftsRouteRouteWithChildren,
  AuthenticatedMembersRouteRoute: AuthenticatedMembersRouteRouteWithChildren,
  AuthenticatedModelsRouteRoute: AuthenticatedModelsRouteRouteWithChildren,
  AuthenticatedModeratorsRouteRoute:
    AuthenticatedModeratorsRouteRouteWithChildren,
  AuthenticatedPackagesRouteRoute: AuthenticatedPackagesRouteRouteWithChildren,
  AuthenticatedProfileValuesRouteRoute:
    AuthenticatedProfileValuesRouteRouteWithChildren,
  AuthenticatedSessionsRouteRoute: AuthenticatedSessionsRouteRouteWithChildren,
  AuthenticatedSettingsRouteRoute: AuthenticatedSettingsRouteRouteWithChildren,
  AuthenticatedSmiliesRouteRoute: AuthenticatedSmiliesRouteRouteWithChildren,
  AuthenticatedAdminLoginActivityRoute: AuthenticatedAdminLoginActivityRoute,
  AuthenticatedContactUsRoute: AuthenticatedContactUsRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedChatsIndexRoute: AuthenticatedChatsIndexRoute,
  AuthenticatedHelpCenterIndexRoute: AuthenticatedHelpCenterIndexRoute,
  AuthenticatedUsersIndexRoute: AuthenticatedUsersIndexRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  UnauthorizedRoute: UnauthorizedRoute,
  authForgotPasswordRoute: authForgotPasswordRoute,
  authOtpRoute: authOtpRoute,
  authResetPasswordRoute: authResetPasswordRoute,
  authSignInRoute: authSignInRoute,
  authSignUpRoute: authSignUpRoute,
  errors401Route: errors401Route,
  errors403Route: errors403Route,
  errors404Route: errors404Route,
  errors500Route: errors500Route,
  errors503Route: errors503Route,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
