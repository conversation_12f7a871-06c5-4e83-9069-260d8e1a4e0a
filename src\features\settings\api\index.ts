import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";

export const getBasicSettings = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.GET_SETTINGS);
            return response?.data ?? {}; // return [] or {} as a fallback

        },
        queryKey: ["settings"],
        staleTime: 0
    });

export const updateBasicSettings = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.UPDATE_SETTINGS, payload);
        },
    });

export const getPageContent = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.GET_PAGE_CONTENT);
            return response?.data ?? {}; // return [] or {} as a fallback

        },
        queryKey: ["page-content"],
        staleTime: 0
    });


export const updatePageContent = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.UPDATE_PAGE_CONTENT, payload);
        },
    });

