import { Main } from "@/components/layout/main";
import { FlirtMessagesTable } from "./components/flirt-messages-table";
import { columns } from "./components/flirt-messages-columns";
import { FlirtMessagePrimaryButtons } from "./components/flirt-message-primary-button";

export default function List() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">
                        Flirt Messages
                    </h2>
                </div>
                <FlirtMessagePrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <FlirtMessagesTable columns={columns} />
            </div>
        </Main>
    );
} 