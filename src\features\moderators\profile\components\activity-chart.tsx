import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { IconDotsVertical } from '@tabler/icons-react'
import { useTheme } from '@/context/theme-context'
import { DatePicker } from '@/components/date-picker'
import { useState } from 'react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  ReferenceDot,
  ReferenceLine,
} from 'recharts';

const data = [
  { month: 'Jan', a: 600, b: 580, c: 560, d: 530 },
  { month: 'Feb', a: 590, b: 560, c: 540, d: 520 },
  { month: 'Mar', a: 570, b: 550, c: 520, d: 510 },
  { month: 'Apr', a: 550, b: 530, c: 500, d: 490 },
  { month: 'May', a: 530, b: 510, c: 490, d: 480 },
  { month: 'Jun', a: 540, b: 500, c: 510, d: 470 },
  { month: 'Jul', a: 580, b: 510, c: 520, d: 500 },
  { month: 'Aug', a: 620, b: 520, c: 540, d: 530 },
  { month: 'Sept', a: 660, b: 530, c: 560, d: 550 },
  { month: 'Oct', a: 690, b: 540, c: 580, d: 570 },
  { month: 'Nov', a: 710, b: 550, c: 600, d: 580 },
  { month: 'Dec', a: 730, b: 560, c: 620, d: 590 },
];
export default function ActivityChart() {
  const { theme } = useTheme()

  // Date state management
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)

  // Theme-aware colors
  const isDark = theme === 'dark'
  const lineColor = isDark ? '#ffffff' : '#000000'
  const gridColor = isDark ? 'hsl(var(--border))' : '#e0e0e0'
  const textColor = isDark ? 'hsl(var(--muted-foreground))' : '#666'

  // Handle date changes with validation
  const handleStartDateChange = (date: Date | undefined) => {
    setStartDate(date)
    // If end date is set and new start date is after end date, clear end date
    if (date && endDate && date >= endDate) {
      setEndDate(undefined)
    }
  }

  const handleEndDateChange = (date: Date | undefined) => {
    // Only set end date if it's after start date or if no start date is set
    if (!startDate || (date && date > startDate)) {
      setEndDate(date)
    }
  }

  return (
    <div className="bg-card rounded-lg border overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between flex-wrap gap-3">
          <h3 className="text-lg font-semibold text-foreground">Activity</h3>
          <div className="flex items-center gap-4 flex-wrap">
            <Select defaultValue="january-2024">
              <SelectTrigger className="w-36 h-8 text-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="january-2024">January 2024</SelectItem>
                <SelectItem value="february-2024">February 2024</SelectItem>
                <SelectItem value="march-2024">March 2024</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center gap-4">
              <DatePicker
                value={startDate}
                placeholder="Start Date"
                onChange={handleStartDateChange}
                max={endDate}
                className="w-32 h-8 text-sm"
              />
              <DatePicker
                value={endDate}
                placeholder="End Date"
                onChange={handleEndDateChange}
                min={startDate}
                className="w-32 h-8 text-sm"
              />
            </div>
            {/* <Button variant="ghost" size="icon">
              <IconDotsVertical className="h-3 w-3" />
            </Button> */}
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="p-4">
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={data} margin={{ top: 30, right: 30, bottom: 30, left: 30 }}>
              <CartesianGrid stroke="#e0e0e0" strokeDasharray="3 3" />
              <XAxis dataKey="month" tick={{ fill: '#444' }} />
              <YAxis tick={{ fill: '#444' }} />

              {/* Optional: Highlight vertical line at 'May' */}
              <ReferenceLine x="May" stroke="#555" strokeDasharray="3 3" />

              {/* Reference Dot (bold dot on 'May') */}
              <ReferenceDot x="May" y={600} r={6} fill="#333" stroke="none" />

              {/* Lines */}
              <Line type="monotone" dataKey="a" stroke="#333" strokeWidth={3} dot={false} />
              <Line type="monotone" dataKey="b" stroke="#333" strokeWidth={3} dot={false} />
              <Line type="monotone" dataKey="c" stroke="#333" strokeWidth={3} dot={false} />
              <Line type="monotone" dataKey="d" stroke="#333" strokeWidth={3} dot={false} />
            </LineChart>
          </ResponsiveContainer>
        </div>
        <div className="flex flex-wrap justify-between gap-4 mt-4 pt-4 border-t" style={{ marginTop: '60px' }}>
          <div className="flex gap-2 flex-1">
            <div
              className="w-3 h-3 rounded-full bg-muted-foreground"
            ></div>
            <div className='flex flex-col gap-1'>
              <span className="text-xs text-muted-foreground">Sent Messages</span>
              <p className="text-sm font-semibold text-muted-foreground">1135</p>
            </div>
          </div>
          <div className="flex gap-2 flex-1">
            <div
              className="w-3 h-3 rounded-full bg-muted-foreground"
            ></div>
            <div className='flex flex-col gap-1'>
              <span className="text-xs text-muted-foreground">Received Messages</span>
              <p className="text-sm font-semibold text-muted-foreground">1135</p>
            </div>
          </div>
          <div className="flex gap-2 flex-1">
            <div
              className="w-3 h-3 rounded-full bg-muted-foreground"
            ></div>
            <div className='flex flex-col gap-1'>
              <span className="text-xs text-muted-foreground">Rum Messages</span>
              <p className="text-sm font-semibold text-muted-foreground">1135</p>
            </div>
          </div>
          <div className="flex gap-2 flex-1">
            <div
              className="w-3 h-3 rounded-full bg-muted-foreground"
            ></div>
            <div className='flex flex-col gap-1'>
              <span className="text-xs text-muted-foreground">Sent Triggers</span>
              <p className="text-sm font-semibold text-muted-foreground">1135</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
