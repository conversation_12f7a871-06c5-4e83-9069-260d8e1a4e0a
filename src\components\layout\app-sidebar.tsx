import { NavGroup } from '@/components/layout/nav-group'
import { TeamSwitcher } from '@/components/layout/team-switcher'
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarRail
} from '@/components/ui/sidebar'
import { sidebarData } from './data/sidebar-data'
import { useAuthStore } from '@/stores/authStore'
import { getFilteredSidebarData, getUserRoles } from '@/utils/role-based-navigation'
import { useMemo } from 'react'

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, roles } = useAuthStore((state) => state.auth)

  // Get user roles from different possible sources
  const userRoles = useMemo(() => {
    // First try to get roles from the roles property in auth store
    if (roles && Array.isArray(roles)) {
      return roles;
    }

    // Then try to get roles from user object
    return getUserRoles(user);
  }, [user, roles]);

  // Filter sidebar data based on user roles
  const filteredSidebarData = useMemo(() => {
    return getFilteredSidebarData(sidebarData, userRoles);
  }, [userRoles]);

  return (
    <Sidebar collapsible='icon' variant='floating' {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={filteredSidebarData.teams} />
      </SidebarHeader>
      <SidebarContent>
        {filteredSidebarData.navGroups.map((props: any) => (
          <NavGroup key={props.title} {...props} />
        ))}
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  )
}
