import { IconUserPlus } from "@tabler/icons-react";
import { But<PERSON> } from "@/components/ui/button";
import { useChatGroup } from "../context/chat-group-context";

export function ChatGroupPrimaryButtons() {
  const { setOpen } = useChatGroup();

  return (
    <div className="flex gap-2">
      <Button
        className="space-x-1"
        onClick={() => setOpen('add')}
      >
        <span>+ Add Moderator Chat Group</span> <IconUserPlus size={18} />
      </Button>
    </div>
  );
}
