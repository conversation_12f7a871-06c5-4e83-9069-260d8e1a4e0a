import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTableColumnHeader } from '@/components/data-table-column-header'
import { cn } from '@/lib/utils'
import { ReplyMessage } from '../data/schema'
import LongText from '@/components/long-text'

export const columns: ColumnDef<ReplyMessage>[] = [
  {
    accessorKey: 'serialNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='#' />
    ),
    cell: ({ row }) => (
      <div className='w-8'>{row.getValue('serialNumber')}</div>
    ),
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-6 md:table-cell w-16'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'domain',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Domain' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-36'>{row.getValue('domain')}</LongText>
    ),
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-6 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'client',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Client' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-32'>{row.getValue('client')}</LongText>
    ),
    meta: { className: 'w-32' },
  },
  {
    accessorKey: 'profile',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Profile' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-32'>{row.getValue('profile')}</LongText>
    ),
    meta: { className: 'w-32' },
  },
  {
    accessorKey: 'clientMessage',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Client Message' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-48'>{row.getValue('clientMessage')}</LongText>
    ),
    meta: { className: 'w-48' },
  },
  {
    accessorKey: 'moderatorReply',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Moderator Reply' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-48'>{row.getValue('moderatorReply')}</LongText>
    ),
    meta: { className: 'w-48' },
  },
  {
    accessorKey: 'messageType',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Message Type' />
    ),
    cell: ({ row }) => {
      const messageType = row.getValue('messageType') as string
      return (
        <div className='flex items-center'>
          <span className='capitalize'>{messageType}</span>
        </div>
      )
    },
    meta: { className: 'w-32' },
  },
]
