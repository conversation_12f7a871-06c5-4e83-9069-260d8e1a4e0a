export interface Note {
  id: string;
  note: string;
  createdAt: string;
  updatedAt: string;
}

export interface NotesData {
  notes: Note[];
  currentNote: string;
}

export interface NotesApiResponse {
  customerNotes: Note[];
  modelNotes: Note[];
}

export interface FieldConfig {
  id: string;
  type: string;
  label: string;
  placeholder: string;
  inputType: "input" | "textarea";
  apiKey: string;
}

export interface InformationField {
  id: string;
  type: string;
  items: string;
}

export interface InformationFormData {
  type: string;
  content: string;
}

export interface FieldValues {
  [fieldId: string]: string;
}

export interface NotesComponentProps {
  data: NotesData;
  onAddNote: (note: string) => void;
  onDeleteNote: (noteId: string) => void;
  isLoading?: boolean;
  title?: string;
}

export interface InformationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedField?: InformationField;
  onSave: (data: InformationFormData) => void;
  fieldValues: FieldValues;
  onFieldValuesChange: (values: FieldValues) => void;
}

export interface NotesHistoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  notes: Note[];
  onDeleteNote: (noteId: string) => void;
  isDeleting?: boolean;
  title?: string;
}
