import { Table } from '@tanstack/react-table'
import { ReactNode } from 'react'

export interface DataTableToolbarProps<TData> {
  table: Table<TData>
  searchKey?: string
  searchPlaceholder?: string
  filters?: DataTableFilter[]
  showViewOptions?: boolean
  children?: ReactNode
}

export interface DataTableFilter {
  column: string
  title: string
  options: Array<{
    label: string
    value: string
    icon?: ReactNode
  }>
}

export interface DataTablePaginationProps<TData> {
  table: Table<TData>
  showRowsPerPage?: boolean
  showSelectedCount?: boolean
}

export interface DataTableProps<TData, TValue> {
  columns: any[]
  data: TData[]
  searchKey?: string
  searchPlaceholder?: string
  filters?: DataTableFilter[]
  enableRowSelection?: boolean
  enableSorting?: boolean
  enableFiltering?: boolean
  enablePagination?: boolean
  showToolbar?: boolean
  showViewOptions?: boolean
  showPagination?: boolean
  pageSize?: number
  className?: string
  toolbarChildren?: ReactNode
}
