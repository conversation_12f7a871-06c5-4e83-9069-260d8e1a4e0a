import { z } from "zod";

export const affiliateCreateSchema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    email: z.string().email("Please enter a valid email address"),
    password: z.string().min(6, "Password must be at least 6 characters"),
    country: z.string().min(1, "Please select a country"),
    city: z.string().min(1, "City is required"),
    nickname: z.string().optional(),
    phone: z.string().optional(),
    company: z.string().optional(),
    skype: z.string().optional(),
    currentWebsites: z.string().optional(),
    marketingMethods: z.string().optional(),
    chargebackRate: z.string().optional(),
    minimumChargeback: z.string().optional(),
    maximumChargeback: z.string().optional(),
    postbackLink: z.string().optional(),
    canSeeAffiliateClientInfo: z.enum(["yes", "no"]),
    accessAffiliateClientMessages: z.enum(["yes", "no"]),
    receiveSalesEmail: z.enum(["yes", "no"]),
    assignAsOwner: z.enum(["yes", "no"]),
});

export type AffiliateFormValues = z.infer<typeof affiliateCreateSchema>;
