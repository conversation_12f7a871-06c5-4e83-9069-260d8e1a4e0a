import { Main } from "@/components/layout/main";
import { PersonalitiesPrimaryButtons } from "./components/personalities-primary-buttons";
import { PersonalitiesTable } from "./components/personalities-table";
import { columns } from "./components/personalities-columns";

export default function PersonalitiesList() {
    return (
        <Main>
            <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">
                        Personalities List
                    </h2>
                </div>
                <PersonalitiesPrimaryButtons />
            </div>
            <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
                <PersonalitiesTable columns={columns} />
            </div>
        </Main>
    );
}
