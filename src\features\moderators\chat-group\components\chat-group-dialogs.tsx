import { useChatGroup } from "../context/chat-group-context"
import { ChatGroupActionDialog } from "./chat-group-action-dialog"
import { ChatGroupDeleteDialog } from "./chat-group-delete-dialog"

export function ChatGroupDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useChatGroup()

  return (
    <>
      <ChatGroupActionDialog
        key='chat-group-add'
        open={open === 'add'}
        onOpenChange={() => setOpen('add')}
      />

      {currentRow && (
        <>
          <ChatGroupActionDialog
            key={`chat-group-edit-${currentRow.id}`}
            open={open === 'edit'}
            onOpenChange={() => {
              setOpen('edit')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />

          <ChatGroupDeleteDialog
            key={`chat-group-delete-${currentRow.id}`}
            open={open === 'delete'}
            onOpenChange={() => {
              setOpen('delete')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />
        </>
      )}
    </>
  )
}
