import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";


// export const useGetReports = (params = {}) =>
//     useQuery({
//         queryFn: async () => {
//             const response = await apiClient.get(API_ENDPOINTS.USER_LOGIN, {
//                 params,
//             });
//             return response?.data;
//         },
//         queryKey: ["reports-list", ...Object.values(params)],
//     });


export const getSignin = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.USER_LOGIN, payload);
        },
    });
export const getOtp = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.REQ_OTP, payload);
        },
    });
export const verifyOtp = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.VERIFY_OTP, payload);
        },
    });
export const resetPassword = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.RESET_PASSWORD, payload);
        },
    });

export const logOut = () =>
    useMutation({
        mutationFn: async () => {
            return await apiClient.get(API_ENDPOINTS.LOG_OUT);
        },
    });