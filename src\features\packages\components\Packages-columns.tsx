import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import LongText from '@/components/long-text'
import { DataTableColumnHeader } from '@/components/data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'

export const columns: ColumnDef<any>[] = [
    {
        accessorKey: 'serialNumber',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='#' />
        ),
        cell: ({ row }) => (
            <div className='w-8'>{row.getValue('serialNumber')}</div>
        ),
        meta: {
            className: cn(
                'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
                'w-8'
            ),
        },
        enableHiding: false,
    },
    {
        id: 'packageName',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Package Name' />
        ),
        cell: ({ row }) => {
            const translations = row.original.translations || [];
            const enTranslation = translations.find((t: any) => t.languageCode === 'en');
            return <LongText className='max-w-36'>{enTranslation?.name || ''}</LongText>;
        },
        meta: { className: 'w-36' },
    },
    {
        accessorKey: 'amomunt',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Amount' />
        ),
        cell: ({ row }) => {

            return (
                <LongText className='max-w-36'>{row.original.amount}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        accessorKey: 'coins',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Coins' />
        ),
        cell: ({ row }) => {

            return (
                <LongText className='max-w-36'>{row.original.coins}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        accessorKey: 'discountAmount',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Discount Amount' />
        ),
        cell: ({ row }) => {

            return (
                <LongText className='max-w-36'>{row.original.discountAmount}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        accessorKey: 'isSuspended',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Status' />
        ),
        cell: ({ row }) => {

            return (
                <LongText className='max-w-36'>{row.original.isSuspended ? "Inactive" : "Avtive"}</LongText>
            )
        },
        meta: { className: 'w-36' },
    },
    {
        id: 'actions',
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title='Action' />
        ),
        enableSorting: false,
        cell: DataTableRowActions,
        meta: { className: 'w-16' },
    },
]
